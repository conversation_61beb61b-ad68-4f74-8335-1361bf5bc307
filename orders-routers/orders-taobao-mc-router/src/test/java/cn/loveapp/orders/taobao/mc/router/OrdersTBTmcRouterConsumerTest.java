package cn.loveapp.orders.taobao.mc.router;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.rocketmq.aliyun.TaobaoOnsOrdersDelayAppConfig;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.common.service.TradeSendOrderHandleService;
import cn.loveapp.orders.dto.proto.OrderMcNotifyProto;
import cn.loveapp.orders.dto.proto.OrderTradeRequest;
import cn.loveapp.orders.taobao.mc.router.config.TmcRouterConfig;
import cn.loveapp.orders.taobao.mc.router.consumer.OrdersTBTmcRouterConsumer;
import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.*;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-25
 * @time: 21:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {OrdersTBTmcRouterConsumerTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class,
		TaobaoOnsOrdersDelayAppConfig.class, TmcRouterConfig.class})
public class OrdersTBTmcRouterConsumerTest {

	@SpyBean
	private TmcRouterConfig tmcRouterConfig;

	@MockBean
	private TaobaoOnsOrdersDelayAppConfig taobaoOnsOrdersDelayAppConfig;

	@MockBean
	private RocketMqQueueHelper rocketMqQueueHelper;

	@MockBean
	private DefaultMQProducer defaultMQProducer;


	@MockBean
	private UserProductionInfoExtService userProductionInfoExtService;

	@MockBean
	private TradeSendOrderHandleService tradeSendOrderHandleService;

	@SpyBean
	private OrdersTBTmcRouterConsumer ordersDelayPushDataConsumer;

	@Test
	public void consume() throws Exception {
		String content = JSON.toJSONString(generateDelayRequestProto());
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId),
				isNull(), eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), eq(0), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
	}

//	/**
//	 * modified相同不更新
//	 * @throws Exception
//	 */
//	@Test
//	public void consume1() throws Exception {
//		String content = JSON.toJSONString(generateDelayRequestProto());
//		AyTradeMain tradeMain = generateTradeMain();
//		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871");
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"))).thenReturn(userDbId);
//		when(tradeHandleService.queryByTid(eq("226543854469216871"), anyString(), any())).thenReturn(tradeMain);
//		ordersDelayPushDataConsumer.execute(content);
//		verify(tradeSendOrderHandleService, never())
//			.pushOrder(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull()
//				, eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS));
//		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
//	}

//	/**
//	 * modified相同, 但状态不相同也要更新
//	 *
//	 * @throws Exception
//	 */
//	@Test
//	public void consume1_2() throws Exception {
//		OrderMcNotifyProto OrderMcNotifyProto = generateDelayRequestProto();
//		OrderMcNotifyProto.getOrderTradeRequest().setTaoStatus(TaobaoStatusConstant.TRADE_FINISHED);
//		OrderMcNotifyProto.getOrderTradeRequest().setTid(226543854469216871L);
//		String content = JSON.toJSONString(OrderMcNotifyProto);
//		AyTradeMain tradeMain = generateTradeMain();
//		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871");
//		when(statusCodeConfigService.getPlatformStatusAyStatus(eq(TaobaoStatusConstant.TRADE_FINISHED))).thenReturn(
//			String.valueOf(AyStatusConstant.TRADE_FINISHED));
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"))).thenReturn(userDbId);
//		when(tradeHandleService.queryByTid(eq("226543854469216871"), anyString(), any())).thenReturn(tradeMain);
//		ordersDelayPushDataConsumer.execute(content);
//		verify(tradeSendOrderHandleService)
//			.pushOrder(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull()
//				, eq(TaobaoStatusConstant.TRADE_FINISHED));
//		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
//	}

//	/**
//	 * modified相同, 状态不相同, 但tmc的状态是待发货时不更新
//	 *
//	 * @throws Exception
//	 */
//	@Test
//	public void consume1_3() throws Exception {
//		OrderMcNotifyProto OrderMcNotifyProto = generateDelayRequestProto();
//		OrderMcNotifyProto.getOrderTradeRequest().setTaoStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
//		String content = JSON.toJSONString(OrderMcNotifyProto);
//		AyTradeMain tradeMain = generateTradeMain();
//		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871");
//		when(statusCodeConfigService.getPlatformStatusAyStatus(eq(TaobaoStatusConstant.WAIT_BUYER_PAY))).thenReturn(
//			String.valueOf(AyStatusConstant.WAIT_BUYER_PAY));
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"))).thenReturn(userDbId);
//		when(tradeHandleService.queryByTid(eq("226543854469216871"), anyString(), any())).thenReturn(tradeMain);
//		ordersDelayPushDataConsumer.execute(content);
//		verify(tradeSendOrderHandleService, never())
//			.pushOrder(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(), any());
//		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
//	}

//	/**
//	 * modified相同, 状态不相同, 但tmc的状态比库里旧时不更新
//	 *
//	 * @throws Exception
//	 */
//	@Test
//	public void consume1_4() throws Exception {
//		OrderMcNotifyProto OrderMcNotifyProto = generateDelayRequestProto();
//		OrderMcNotifyProto.getOrderTradeRequest().setTaoStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);
//		String content = JSON.toJSONString(OrderMcNotifyProto);
//		AyTradeMain tradeMain = generateTradeMain();
//		tradeMain.setTaoStatus(TaobaoStatusConstant.WAIT_BUYER_RETURN_GOODS);
//		tradeMain.setAyStatus(AyStatusConstant.SELLER_GOODS_SENDED);
//		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871");
//		when(statusCodeConfigService.getPlatformStatusAyStatus(eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS))).thenReturn(
//			String.valueOf(AyStatusConstant.WAIT_SELLER_SEND_GOODS));
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"))).thenReturn(userDbId);
//		when(tradeHandleService.queryByTid(eq("226543854469216871"), anyString(), any())).thenReturn(tradeMain);
//		ordersDelayPushDataConsumer.execute(content);
//		verify(tradeSendOrderHandleService, never())
//			.pushOrder(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(), any());
//		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
//	}

//	/**
//	 * modified大于库中
//	 *
//	 * @throws Exception
//	 */
//	@Test
//	public void consume2() throws Exception {
//		String content = JSON.toJSONString(generateDelayRequestProto());
//		AyTradeMain tradeMain = generateMinTradeMain();
//		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871");
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"))).thenReturn(userDbId);
//		when(tradeHandleService.queryByTid(eq("226543854469216871"), anyString(), any())).thenReturn(tradeMain);
//		ordersDelayPushDataConsumer.execute(content);
//		verify(tradeSendOrderHandleService)
//			.pushOrder(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
//				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS));
//		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
//	}

	/**
	 * 卖家评价, 缺少评价方, 发送全fullinfo
	 *
	 * @throws Exception
	 */
	@Test
	public void consume3() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus(OrdersTBTmcRouterConsumer.TRADE_RATED);
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq("TradeRated"), eq(0), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
	}

	/**
	 * 卖家评价, 部分更新
	 *
	 * @throws Exception
	 */
	@Test
	public void consume4() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus(OrdersTBTmcRouterConsumer.TRADE_RATED);
		proto.getTradeRequest().setOid("oid");
		proto.getTradeRequest().setRater(OrdersTBTmcRouterConsumer.TRADE_RATED_SELLER);
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setSellerRate(true);
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "226543854469216871", "oid", proto.getTradeRequest().getModified(),
				null, tradeRated, null);

		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), eq(tmcOrdersRequest),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq(OrdersTBTmcRouterConsumer.TRADE_RATED), eq(0), CommonAppConstants.APP_TRADE);

		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();

	}

	/**
	 * 买家评价, 部分更新
	 *
	 * @throws Exception
	 */
	@Test
	public void consume5() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus(OrdersTBTmcRouterConsumer.TRADE_RATED);
		proto.getTradeRequest().setOid("oid");
		proto.getTradeRequest().setRater(OrdersTBTmcRouterConsumer.TRADE_RATED_BUYER);
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setBuyeRate(true);
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "226543854469216871", "oid", proto.getTradeRequest().getModified(),
				null, tradeRated, null);

		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), eq(tmcOrdersRequest),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq(OrdersTBTmcRouterConsumer.TRADE_RATED), eq(0), CommonAppConstants.APP_TRADE);

		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();

	}

	/**
	 * 修改备注, 没有备注信息, 全部更新
	 *
	 * @throws Exception
	 */
	@Test
	public void consume6() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus(OrdersTBTmcRouterConsumer.TRADE_MEMO_MODIFIED);
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq(OrdersTBTmcRouterConsumer.TRADE_MEMO_MODIFIED), eq(0), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
	}

	/**
	 * 修改备注, 部分更新
	 *
	 * @throws Exception
	 */
	@Test
	public void consume7() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);

		String sellerMemo = "sellerMemo";
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus(OrdersTBTmcRouterConsumer.TRADE_MEMO_MODIFIED);
		proto.getTradeRequest().setOid("oid");
		proto.getTradeRequest().setSellerMemo(sellerMemo);

		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		tradeMemoModified.setSellerMemo(sellerMemo);
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "226543854469216871", "oid", proto.getTradeRequest().getModified(),
				tradeMemoModified, null, null);
		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), eq(tmcOrdersRequest),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq(OrdersTBTmcRouterConsumer.TRADE_MEMO_MODIFIED), eq(0), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
	}

	/**
	 * 非特殊消息, 全更新
	 *
	 * @throws Exception
	 */
	@Test
	public void consume9() throws Exception {
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus("TradeLogisticsAddressChanged");
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS),eq("TradeLogisticsAddressChanged"), eq(0), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer).rateLimiterAcquire();
	}

	/**
	 * 未开户用户
	 *
	 * @throws Exception
	 */
	@Test
	public void consume11() throws Exception {
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), eq("trade"))).thenReturn(null);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setStatus("TradeLogisticsAddressChanged");
		String content = JSON.toJSONString(proto);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService, never())
			.pushTmcFullinfo(any(), any(), any(), any(), any(), any(), any(), any(), anyInt(), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer, never()).rateLimiterAcquire();
	}

	/**
	 * 丢弃待付款消息
	 *
	 * @throws Exception
	 */
	@Test
	public void consume12() throws Exception {
		when(tmcRouterConfig.isDiscardWaitBuyerPayMessage()).thenReturn(true);
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setTaoStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		String content = JSON.toJSONString(proto);
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), eq("trade"))).thenReturn(userDbId);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));
		verify(tradeSendOrderHandleService, never())
			.pushTmcFullinfo(any(), any(), any(), any(), any(), any(), any(), any(), anyInt(), CommonAppConstants.APP_TRADE);
		verify(ordersDelayPushDataConsumer, never()).rateLimiterAcquire();
	}

	/**
	 * 第一次待付款消息, 发回队列延时消费
	 *
	 * @throws Exception
	 */
	@Test
	public void consume13() throws Exception {
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setTaoStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		String content = JSON.toJSONString(proto);
		when(tmcRouterConfig.getWaitBuyerPayMessageDelayLevel()).thenReturn(4);

		when(taobaoOnsOrdersDelayAppConfig.getTopic()).thenReturn("tmc");
		when(taobaoOnsOrdersDelayAppConfig.getTag()).thenReturn("tag");

		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				eq(TaobaoStatusConstant.WAIT_BUYER_PAY),eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), eq(4), CommonAppConstants.APP_TRADE);

	}

	/**
	 * 第一次TRADE_NO_CREATE_PAY消息, 发回队列延时消费
	 *
	 * @throws Exception
	 */
	@Test
	public void consume14() throws Exception {
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setTaoStatus(TaobaoStatusConstant.TRADE_NO_CREATE_PAY);
		String content = JSON.toJSONString(proto);
		when(tmcRouterConfig.getWaitBuyerPayMessageDelayLevel()).thenReturn(4);

		when(taobaoOnsOrdersDelayAppConfig.getTopic()).thenReturn("tmc");
		when(taobaoOnsOrdersDelayAppConfig.getTag()).thenReturn("tag");

		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				eq(TaobaoStatusConstant.TRADE_NO_CREATE_PAY),eq(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), eq(4), CommonAppConstants.APP_TRADE);
	}

	/**
	 * taoStatus为null, 发回队列延时消费
	 *
	 * @throws Exception
	 */
	@Test
	public void consume15() throws Exception {
		OrderMcNotifyProto proto = generateDelayRequestProto();
		proto.getTradeRequest().setTaoStatus(null);
		proto.getTradeRequest().setStatus("TradeChanged");
		String content = JSON.toJSONString(proto);
		when(tmcRouterConfig.getWaitBuyerPayMessageDelayLevel()).thenReturn(4);

		when(taobaoOnsOrdersDelayAppConfig.getTopic()).thenReturn("tmc");
		when(taobaoOnsOrdersDelayAppConfig.getTag()).thenReturn("tag");

		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "226543854469216871", "226543854469216871", "TAO", null);
		when(userProductionInfoExtService.getDbIdBySellerNick(eq("中华人民共和国"), eq("TAO"), isNull())).thenReturn(userDbId);
//		ordersDelayPushDataConsumer.execute(new AiyongMessageExt(content, false));

		verify(tradeSendOrderHandleService)
			.pushTmcFullinfo(eq("226543854469216871"), eq("中华人民共和国"), any(), any(), eq(userDbId), isNull(),
				isNull(),eq("TradeChanged"), eq(4), CommonAppConstants.APP_TRADE);
	}


	private OrderMcNotifyProto generateDelayRequestProto() {
		OrderTradeRequest OrderTradeRequest = new OrderTradeRequest();
		OrderTradeRequest.setTopic("notify_order");
		OrderTradeRequest.setSellerId("2231153436");
		OrderTradeRequest.setSellerNick("中华人民共和国");
		OrderTradeRequest.setPlatformId("TAO");
		OrderTradeRequest.setAppName("trade");
		OrderTradeRequest.setTaoStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);
		OrderTradeRequest.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);
		OrderTradeRequest.setTid("226543854469216871");
		OrderTradeRequest.setModified(DateUtil.parseString("2019-01-25 00:27:19"));
		OrderMcNotifyProto OrderMcNotifyProto = new OrderMcNotifyProto();
		OrderMcNotifyProto.setTradeRequest(OrderTradeRequest);
		return OrderMcNotifyProto;
	}

	private OrderMcNotifyProto generateRefunedDelayRequestProto() {
		OrderTradeRequest OrderTradeRequest = new OrderTradeRequest();
		OrderTradeRequest.setTopic("notify_order");
		OrderTradeRequest.setSellerId("2231153436");
		OrderTradeRequest.setSellerNick("中华人民共和国");
		OrderTradeRequest.setTid("226543854469216871L");
		OrderTradeRequest.setPlatformId("TAO");
		OrderTradeRequest.setAppName("trade");
		OrderTradeRequest.setModified(DateUtil.parseString("2019-01-25 00:27:19"));
		OrderMcNotifyProto OrderMcNotifyProto = new OrderMcNotifyProto();
		OrderMcNotifyProto.setOrderRefunedRequest(OrderTradeRequest);
		return OrderMcNotifyProto;
	}
}
