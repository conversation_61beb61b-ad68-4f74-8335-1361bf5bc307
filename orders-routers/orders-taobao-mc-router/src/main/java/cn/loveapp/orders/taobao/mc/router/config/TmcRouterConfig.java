package cn.loveapp.orders.taobao.mc.router.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * TmcRouterConfig
 *
 * <AUTHOR>
 * @date 2020/5/14
 */
@Data
@Configuration
public class TmcRouterConfig {

	/**
	 * 待付款消息延时时间
	 */
	@Value("${orders.tmc.waitBuyerPayMessageDelayLevel:4}")
	private int waitBuyerPayMessageDelayLevel;

	/**
	 * 是否丢弃待付款消息
	 */
	@Value("${orders.tmc.discardWaitBuyerPayMessage:false}")
	private boolean discardWaitBuyerPayMessage;

	/**
	 * 待付款消息的status
	 */
	@Value("${orders.tmc.waitBuyerPayStatus:WAIT_BUYER_PAY,TRADE_NO_CREATE_PAY}")
	private List<String> waitBuyerPayStatus = Lists.newArrayList();

	/**
	 * 可以部分更新的特殊status
	 */
	@Value("${orders.tmc.specialStatus:TradeRated,TradeMemoModified,TradeLogisticsAddressChanged}")
	private List<String> specialStatus = Lists.newArrayList();

	/**
	 * 要忽略的dbId
	 */
	@Value("${orders.tmc.ignoreDbIds:}")
	private List<Integer> ignoreDbIds = Lists.newArrayList();
}
