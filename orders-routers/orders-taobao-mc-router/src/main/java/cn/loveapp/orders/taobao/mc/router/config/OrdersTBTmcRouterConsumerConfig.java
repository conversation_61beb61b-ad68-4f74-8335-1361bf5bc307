package cn.loveapp.orders.taobao.mc.router.config;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoAppConfig;
import cn.loveapp.orders.common.config.rocketmq.aliyun.TaobaoOnsOrdersDelayAppConfig;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.taobao.mc.router.consumer.OrdersTBTmcRouterConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragelyByCircle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

/**
 * @program: orders-services-group
 * @description: OrdersDelayOnsConsumerConfig
 * @author: Jason
 * @create: 2019-01-03 13:48
 **/
@Configuration
public class OrdersTBTmcRouterConsumerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersTBTmcRouterConsumerConfig.class);

	@Autowired
	private RocketMQTaobaoAppConfig rocketMQTaobaoAppConfig;

	@Autowired
	private TaobaoOnsOrdersDelayAppConfig taobaoOnsOrdersDelayAppConfig;

	private DefaultMQPushConsumer consumer = null;

	@Bean(destroyMethod = "", name = "ordersDelayPushDataOnsConsumer")
	public DefaultMQPushConsumer ordersBatchDataOnsConsumer(){
		//启动ONS消息队列
		try {
			consumer = new DefaultMQPushConsumer(taobaoOnsOrdersDelayAppConfig.getConsumerid());
			consumer.setNamesrvAddr(rocketMQTaobaoAppConfig.getNamesrvAddr());
			consumer.setConsumeThreadMax(taobaoOnsOrdersDelayAppConfig.getMaxThreadNum());
			consumer.setConsumeThreadMin(taobaoOnsOrdersDelayAppConfig.getMaxThreadNum());
			consumer.setAllocateMessageQueueStrategy(new AllocateMessageQueueAveragelyByCircle());
		} catch (Exception e) {
			LOGGER.logError("create order delay ONS Consumer failed", e);
		}
		return consumer;
	}

	@Bean(name = "ordersDelayOnsConsumerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager(){
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2018/11/9
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>, Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired
		@Qualifier("ordersDelayPushDataOnsConsumer")
		private DefaultMQPushConsumer ordersDelayPushDataOnsConsumer;

		@Autowired
		private OrdersTBTmcRouterConsumer ordersDelayPushDataConsumer;

		@Autowired
		private TaobaoOnsOrdersDelayAppConfig taobaoOnsOrdersDelayAppConfig;

		@Autowired
		private RocketMqQueueHelper rocketMqQueueHelper;

		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS消费者
			if (ordersDelayPushDataOnsConsumer != null) {
				ordersDelayPushDataOnsConsumer.subscribe(taobaoOnsOrdersDelayAppConfig.getTopic(), taobaoOnsOrdersDelayAppConfig
						.getTag());
				ordersDelayPushDataOnsConsumer.registerMessageListener(ordersDelayPushDataConsumer);
				LOGGER.logInfo("order delay Consumer is startting");
				ordersDelayPushDataOnsConsumer.start();
				LOGGER.logInfo("order delay Consumer is started, Topic: " + taobaoOnsOrdersDelayAppConfig.getTopic()
						+ " Tag: " + taobaoOnsOrdersDelayAppConfig.getTag());
			}
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
				return;
			}
			if(ordersDelayPushDataOnsConsumer != null){
				LOGGER.logInfo("正在关闭订单延迟入库OnsConsumer...");
				try {
					ordersDelayPushDataConsumer.stop();
					rocketMqQueueHelper.stopOnsConsumer(ordersDelayPushDataOnsConsumer);
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("延迟订单入库OnsConsumer已关闭");
			}

		}

		@Override
		public int getOrder() {
			return Ordered.HIGHEST_PRECEDENCE;
		}
	}
}
