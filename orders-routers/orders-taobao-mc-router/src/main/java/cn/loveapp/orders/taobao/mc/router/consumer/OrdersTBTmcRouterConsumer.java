package cn.loveapp.orders.taobao.mc.router.consumer;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.response.AyLogisticsTraceSearchResponse;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.rocketmq.RocketMqReopenCheckUserConfig;
import cn.loveapp.orders.common.config.trade.OrderLogisticsTraceTypeConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQOrdersPretestConfig;
import cn.loveapp.orders.common.constant.OnsRateLimitConstant;
import cn.loveapp.orders.common.consumer.BaseDbIdOnsConsumer;
import cn.loveapp.orders.common.dto.AyLogisticsStatusDTO;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.dto.proto.*;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.service.TradeSendOrderHandleService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.taobao.mc.router.config.TmcRouterConfig;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.OrderUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @program: orders-services-group
 * @description: OrdersDelayPushDataConsumer
 * @author: Jason
 * @create: 2019-01-03 13:53
 **/
@Component
public class OrdersTBTmcRouterConsumer extends BaseDbIdOnsConsumer {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersTBTmcRouterConsumer.class);
	public static final String TRADE_RATED = "TradeRated";
	public static final String TRADE_MEMO_MODIFIED = "TradeMemoModified";
	public static final String TRADE_RATED_SELLER = "seller";
	public static final String TRADE_RATED_BUYER = "buyer";

	/**
	 * tmcsub转发来的消息类型为自助修改地址
	 * taobao_modifyaddress_ResultNotify
	 */
	private static final String TOPIC_TAOBAO_SELF_MODIFYADDR = "modifyaddress";

	/**
	 * tmcsub转发来的消息类型为自助修改sku
	 * taobao_modifysku_ResultNotify
	 */
	private static final String TOPIC_TAOBAO_SELF_MODIFYSKU = "modifysku";

	private static final String TAG_DISTRIBUTOR_ORDER_STATUS_CHANGE = "DistributorOrderStatusChange";

	private static final String TAG_SUPPLIER_ORDER_STATUS_CHANGE = "SupplierOrderStatusChange";

    /**
     * 预发货物流平台列表
     */
    private static final List<String> PREPARE_CONSIGN_LOGISTICS_STORE_ID_LIST =
        Lists.newArrayList(CommonLogisticsConstants.PLATFORM_KDNIAO, CommonLogisticsConstants.PLATFORM_CAINIAO);

	@Autowired
	private TmcRouterConfig tmcRouterConfig;

	@Autowired
	private OrderLogisticsTraceTypeConfig orderLogisticsTraceTypeConfig;

	@Autowired
	private UserProductionInfoExtService userProductionInfoExtService;

	@Autowired
	private TradeSendOrderHandleService tradeSendOrderHandleService;

    @Autowired
    private RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    private DefaultMQProducer defaultMQProducer;

    @Autowired
    private RocketMQOrdersPretestConfig rocketMQOrdersPretestConfig;

    @Autowired
    private RocketMqReopenCheckUserConfig rocketMqReopenCheckUserConfig;


    /**
     * 业务处理：真正处理的数据类
     *
     * @param message
     * @param messageDeserializationResult
     * @throws Exception
     */
    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws Exception {
        String messageLog = messageDeserializationResult.getMessageLog();
        OrderTradeRequest orderTradeRequest = new OrderTradeRequest();
		LogisticsTraceRequest logisticsTraceRequest = new LogisticsTraceRequest();
		DistributorOrderStatusChangeRequest distributorOrderStatusChangeRequest = new DistributorOrderStatusChangeRequest();
		OrderMcNotifyProto orderTradeDelayRequestProto = (OrderMcNotifyProto) messageDeserializationResult.getContent();
		boolean distributorFlag = false;

        FuwuRequest fuwuRequest = orderTradeDelayRequestProto.getFuwuRequest();

		//判断一下notify_trade notify_refuned
		if (!Objects.isNull(orderTradeDelayRequestProto.getTradeRequest())) {
			orderTradeRequest = orderTradeDelayRequestProto.getTradeRequest();
		} else if (!Objects.isNull(orderTradeDelayRequestProto.getOrderRefunedRequest())) {
			orderTradeRequest = orderTradeDelayRequestProto.getOrderRefunedRequest();
		} else if (!Objects.isNull(orderTradeDelayRequestProto.getSelfModifyRequest())) {
			//淘宝自助修改tmc
			orderTradeRequest = orderTradeDelayRequestProto.getSelfModifyRequest();
		} else if (!Objects.isNull(orderTradeDelayRequestProto.getLogisticsTraceRequest())){
			logisticsTraceRequest = orderTradeDelayRequestProto.getLogisticsTraceRequest();
			orderTradeRequest.setTid(logisticsTraceRequest.getTid());
			orderTradeRequest.setSellerNick(logisticsTraceRequest.getSellerNick());
            if (logisticsTraceRequest.getModified() != null) {
                orderTradeRequest.setModified(logisticsTraceRequest.getModified());
            } else {
                orderTradeRequest.setModified(logisticsTraceRequest.getTime());
            }
		} else if (!Objects.isNull(orderTradeDelayRequestProto.getDistributorOrderStatusChangeRequest())){
			distributorOrderStatusChangeRequest = orderTradeDelayRequestProto.getDistributorOrderStatusChangeRequest();
			String tag = distributorOrderStatusChangeRequest.getTag();
			if (TAG_DISTRIBUTOR_ORDER_STATUS_CHANGE.equals(tag)) {
				distributorFlag = true;
				orderTradeRequest.setTid(distributorOrderStatusChangeRequest.getTid());
			} else if (TAG_SUPPLIER_ORDER_STATUS_CHANGE.equals(tag)) {
				orderTradeRequest.setTid(distributorOrderStatusChangeRequest.getScpOrderId());
			}
			orderTradeRequest.setSellerNick(distributorOrderStatusChangeRequest.getSellerNick());
			orderTradeRequest.setModified(distributorOrderStatusChangeRequest.getModified());
            orderTradeRequest.setAppName(distributorOrderStatusChangeRequest.getAppName());
		} else if (!Objects.isNull(fuwuRequest)) {
            orderTradeRequest.setSellerNick(fuwuRequest.getSellerNick());
        }

		String tid = orderTradeRequest.getTid();
		String sellerNick = orderTradeRequest.getSellerNick();
        String appName = OrderUtil.defaultAppName(CommonPlatformConstants.PLATFORM_TAO, orderTradeRequest.getAppName());

        //校验是否灰度转发 - router转灰度router
        if (rocketMQOrdersPretestConfig.isPretestUser(sellerNick)) {
            String tag = message.getTags();
            if (tag == null) {
                tag = "*";
            }
            Map<String, String> newProperties = serializationProperties(message);
            rocketMqQueueHelper.pushBackToQueue(rocketMQOrdersPretestConfig.getTaoMcRouterTopic(), tag, message.getBody(),
                defaultMQProducer, 0, newProperties, messageLog);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        if (Objects.nonNull(fuwuRequest)) {
            rocketMqQueueHelper.push(rocketMqReopenCheckUserConfig.getTopic(), "*", fuwuRequest, defaultMQProducer);
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        if (StringUtils.isEmpty(tid)) {
            LOGGER.logError("tid有误");
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

		if (StringUtils.isEmpty(sellerNick)) {
			LOGGER.logError("sellerNick为空");
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		}

        UserDbId userDbId = userProductionInfoExtService.getDbIdBySellerNick(sellerNick,
            CommonPlatformConstants.PLATFORM_TAO, StringUtils.isBlank(appName) ? null : appName);
		if(userDbId == null || userDbId.getDbId() == null){
			LOGGER.logError(sellerNick, tid, "未开户用户: " + sellerNick);
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		}
		if(tmcRouterConfig.getIgnoreDbIds().contains(userDbId.getDbId())){
			LOGGER.logInfo(sellerNick, tid, "忽略" + userDbId.getDbId() + "库用户tmc消息: " + messageLog);
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		}
		MDC.put("tid", tid);
		MDC.put("sellerNick", sellerNick);

		String taoStatus = orderTradeRequest.getTaoStatus();
		String status = orderTradeRequest.getStatus();
		int delayLevel = 0;
		// 校验待付款订单消息, 收到的待付款订单需要特殊处理
		if(delayWaitBuyerPay(tid, sellerNick, taoStatus, status)) {
			if(StringUtils.isNotEmpty(taoStatus) && tmcRouterConfig.isDiscardWaitBuyerPayMessage()){
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
			delayLevel = tmcRouterConfig.getWaitBuyerPayMessageDelayLevel();
		}
		String sellerId = userDbId.getSellerId();
		LocalDateTime modified = orderTradeRequest.getModified();
		rateLimiterAcquire();
		try {
			//如果是自主修改地址/sku消息 直接发送至fullinfo
			TmcOrdersRequest tmc = null;
			if (!Objects.isNull(orderTradeDelayRequestProto.getSelfModifyRequest())) {

				if (TOPIC_TAOBAO_SELF_MODIFYADDR.equals(orderTradeDelayRequestProto.getSelfModifyRequest().getTopic())) {
					tmc = new TmcOrdersRequest(sellerNick, tid, modified,
						new TmcOrdersRequest.TradeAddrModifiedBySelf(orderTradeDelayRequestProto.getSelfModifyRequest().getResult()), appName);
				}else if (TOPIC_TAOBAO_SELF_MODIFYSKU.equals(orderTradeDelayRequestProto.getSelfModifyRequest().getTopic())){
					tmc = new TmcOrdersRequest(sellerNick, tid, orderTradeRequest.getOid(), modified,
						new TmcOrdersRequest.TradeSkuModifiedBySelf(orderTradeDelayRequestProto.getSelfModifyRequest().getResult()), appName);
				}
                tradeSendOrderHandleService.pushTmcFullinfo(tid, sellerNick, sellerId, modified, userDbId, tmc,
                    taoStatus, status, delayLevel, appName);
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
			// 如果是物流消息 直接发送fullinfo
            LogisticsTraceRequest logisticsTraceRequestProto = orderTradeDelayRequestProto.getLogisticsTraceRequest();
            if (!Objects.isNull(logisticsTraceRequestProto)){
                // 取下物流里面的appName
                appName = OrderUtil.defaultAppName(logisticsTraceRequestProto.getPlatformId(), logisticsTraceRequestProto.getAppName());
                // 用户订阅的物流消息发送预发货
                if (PREPARE_CONSIGN_LOGISTICS_STORE_ID_LIST
                    .contains(logisticsTraceRequestProto.getLogisticsStoreId())) {
                    // 非售后物流转预发货
                    if (CollectionUtils.isNotEmpty(logisticsTraceRequestProto.getTidList())
                        || CollectionUtils.isEmpty(logisticsTraceRequestProto.getRefundList())) {
                        tradeSendOrderHandleService.pushLogisticsTraceForwardToAutoConsign(logisticsTraceRequest);
                    }
                }

                Set<String> tidList = new HashSet<>();
                if (CollectionUtils.isNotEmpty(logisticsTraceRequestProto.getTidList())) {
					// 物流服务过来的订单消息
                    tidList.addAll(logisticsTraceRequestProto.getTidList());
                } else if (CollectionUtils.isNotEmpty(logisticsTraceRequestProto.getRefundList())) {
					// 物流服务过来的售后单消息
					LOGGER.logInfo("售后物流暂未实现处理，跳过");
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
				} else if (StringUtils.isNotEmpty(tid)) {
					// tmc-sub过来的
                    tidList.add(tid);
                }

                if (CollectionUtils.isEmpty(tidList)) {
                    LOGGER.logInfo("不存在需要更新物流信息的订单id，跳过");
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                delayLevel = 0;
				String action = logisticsTraceRequestProto.getAction();
				String desc = logisticsTraceRequestProto.getDesc();
				Boolean isCollected = null;
				if (!orderLogisticsTraceTypeConfig.isSearchCollected(action, desc)){
					isCollected = orderLogisticsTraceTypeConfig.isCollected(action, desc);
				} else {
                    //修改物流时，调用物流轨迹接口存在延迟，这里5秒后消费
                    delayLevel = 2;
                }

                //判断成分品爱用物流状态
                AyLogisticsTraceSearchResponse.TransitStepResult result = AyLogisticsTraceSearchResponse.getAyLogisticsStatus(logisticsTraceRequest);
                AyLogisticsStatusDTO ayLogisticsStatusDTO = new AyLogisticsStatusDTO();
                orderLogisticsTraceTypeConfig.setAyLogisticsStatus(result, ayLogisticsStatusDTO);


                //如果isCollection为空，或者爱用状态标记值全部不为true，无需发送消息
                if (BooleanUtils.isFalse(isCollected) && ayLogisticsStatusDTO.isInvalidStatus()) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                for (String tidStr : tidList) {
                    tmc = new TmcOrdersRequest(sellerNick, tidStr, modified, new TmcOrdersRequest.TradeLogisticsTrace(
                        isCollected, logisticsTraceRequest.getOutSid(), ayLogisticsStatusDTO), appName);
                    tradeSendOrderHandleService.pushTmcFullinfo(tidStr, sellerNick, sellerId, modified, userDbId, tmc,
                        taoStatus, status, delayLevel, appName);
                }
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}

			// 如果是代发单据消息 直接发送代发distributeGet
			if (!Objects.isNull(orderTradeDelayRequestProto.getDistributorOrderStatusChangeRequest())){
				if (distributorFlag) {
					// 代发消息实时更新
					delayLevel = 0;
					String scpOrderId = distributorOrderStatusChangeRequest.getScpOrderId();
					String distributeStatus = distributorOrderStatusChangeRequest.getOrderStatus();
					String changeReason = distributorOrderStatusChangeRequest.getChangeReason();
					tmc = new TmcOrdersRequest(sellerNick, tid, distributorOrderStatusChangeRequest.getOid(), modified,
						new TmcOrdersRequest.TradeDistributeChange(scpOrderId, distributeStatus, changeReason), null);
					tradeSendOrderHandleService.pushDistributeGet(tid, sellerNick, sellerId, modified, tmc, false, delayLevel);
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                } else {
                    // 供货商消息走fullinfo
                    delayLevel = 0;
                    String scpOrderId = distributorOrderStatusChangeRequest.getScpOrderId();
                    String distributeStatus = distributorOrderStatusChangeRequest.getOrderStatus();
                    String changeReason = distributorOrderStatusChangeRequest.getChangeReason();
                    String supplierId = distributorOrderStatusChangeRequest.getSupplierId();
                    tmc = new TmcOrdersRequest(sellerNick, tid, distributorOrderStatusChangeRequest.getOid(), modified,
                        new TmcOrdersRequest.TradeSupplierChange(scpOrderId, distributeStatus, changeReason,
                            supplierId), appName);
                    tradeSendOrderHandleService.pushTmcFullinfo(tid, sellerNick, sellerId, modified, userDbId, tmc,
                        taoStatus, status, delayLevel, appName);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
			}

			if (tmcRouterConfig.getSpecialStatus().contains(status)) {
				tmc = getStatusBatchPush(orderTradeRequest, tid, sellerNick, modified, appName);
				if(tmc != null){
					LOGGER.logInfo(sellerNick, tid, "特殊处理的status, 需要部分更新 =>" + orderTradeRequest.getStatus());
					//直接透传到batch队列
                    tradeSendOrderHandleService.pushTmcFullinfo(tid, sellerNick, sellerId, modified, userDbId, tmc,
                        taoStatus, status, delayLevel, appName);
				}else{
					//加入fullinfo队列
                    tradeSendOrderHandleService.pushTmcFullinfo(tid, sellerNick, sellerId, modified, userDbId, null,
                        taoStatus, status, delayLevel, appName);
				}
			}else {
				//加入fullinfo队列
                tradeSendOrderHandleService.pushTmcFullinfo(tid, sellerNick, sellerId, modified, userDbId, null,
                    taoStatus, status, delayLevel, appName);
			}
		} catch (Exception e) {
			String error = "无法获取数据库分配数据";
			if(StringUtils.contains(e.getMessage(), error)){
				// 数据库失败, 说明用户尚未开通, 不再抛出异常
				LOGGER.logError(sellerNick, tid, "未开户用户", e);
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
			LOGGER.logError(sellerNick, tid, "程序出错=>" + e.getMessage(), e);
			throw e;
		}
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}


	private boolean delayWaitBuyerPay(String tid, String sellerNick, String taoStatus, String status) {
		if(StringUtils.isEmpty(taoStatus) || !tmcRouterConfig.getWaitBuyerPayStatus().contains(taoStatus)){
			return false;
		}
		int delayLevel = tmcRouterConfig.getWaitBuyerPayMessageDelayLevel();
        if (tmcRouterConfig.isDiscardWaitBuyerPayMessage()) {
            // 丢待付款
            LOGGER.logInfo(sellerNick, tid, "丢弃待付款订单消息, status=" + status);
            return true;
        } else if (delayLevel > 0) {
            return true;
        }
		return false;
	}

	/**
	 * 依据status判断是否需要推送到batch队列中
	 *
	 * @param tid
	 * @param sellerNick
	 * @return
	 */
	protected TmcOrdersRequest getStatusBatchPush(OrderTradeRequest orderTradeRequest, String tid, String sellerNick,
		LocalDateTime modified, String appName) {
		String status = orderTradeRequest.getStatus();
		TmcOrdersRequest.TradeRated tradeRated = null;
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = null;
		if(TRADE_RATED.equals(status)){
			tradeRated = new TmcOrdersRequest.TradeRated();
			if(TRADE_RATED_SELLER.equals(orderTradeRequest.getRater())){
				tradeRated.setSellerRate(true);
			}else if(TRADE_RATED_BUYER.equals(orderTradeRequest.getRater())){
				tradeRated.setBuyeRate(true);
			}else{
				LOGGER.logWarn(sellerNick, tid, "TradeRated 缺少评价内容");
				return null;
			}
		}else if(TRADE_MEMO_MODIFIED.equals(status)){
			if(StringUtils.isNotEmpty(orderTradeRequest.getSellerMemo())){
				tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
				tradeMemoModified.setSellerMemo(orderTradeRequest.getSellerMemo());
			}else{
				LOGGER.logWarn(sellerNick, tid, "TradeMemoModified 缺少备注内容");
				return null;
			}
		}else{
			return null;
		}
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest(sellerNick, tid, orderTradeRequest.getOid(), modified, tradeMemoModified,
				tradeRated, appName);
		return tmcOrdersRequest;
	}

	public OrdersTBTmcRouterConsumer(MeterRegistry registry, Environment environment){
        super(registry, "延迟入库.QPS", OnsRateLimitConstant.ROCKETMQ_ORDERS_RATELIMIT_TAOBAO_MC,
            environment, true, false);
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt message) {
        return OrderMcNotifyProto.class;
    }

}
