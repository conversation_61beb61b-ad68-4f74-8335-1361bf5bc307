<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>orders-routers</artifactId>
		<groupId>cn.loveapp.orders</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>orders-pdd-mc-router</artifactId>

	<name>订单-路由-拼多多-MC路由</name>
	<description>爱用基础服务-订单服务-拼多多-MC路由</description>

	<properties>
		<orders-router-common.version>1.0-SNAPSHOT</orders-router-common.version>
		<orders-consumer-common.version>1.1-SNAPSHOT</orders-consumer-common.version>
	</properties>

	<organization>
		<name>Loveapp Inc.</name>
		<url>http://www.aiyongbao.com</url>
	</organization>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-api</artifactId>
			<version>${orders-api.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-consumer-common</artifactId>
			<version>${orders-consumer-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-mc-router-common</artifactId>
			<version>${orders-router-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-spring-boot-web-starter</artifactId>
		</dependency>
		<!--测试-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-platformsdk-starter</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<!--<plugin>-->
			<!--<groupId>com.taobao.pandora</groupId>-->
			<!--<artifactId>pandora-boot-maven-plugin</artifactId>-->
			<!--<version>${pandora.maven-plugin.version}</version>-->
			<!--<executions>-->
			<!--<execution>-->
			<!--<phase>package</phase>-->
			<!--<goals>-->
			<!--<goal>repackage</goal>-->
			<!--</goals>-->
			<!--</execution>-->
			<!--</executions>-->
			<!--</plugin>-->
		</plugins>
	</build>

</project>
