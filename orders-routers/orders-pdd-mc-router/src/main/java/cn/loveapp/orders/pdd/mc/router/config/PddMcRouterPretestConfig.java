package cn.loveapp.orders.pdd.mc.router.config;

import cn.loveapp.orders.common.config.rocketmq.RocketMQDefaultConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * pdd mctouter消息灰度转发配置
 * <AUTHOR>
 * @create 2022-02-16 下午5:14
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "order.pdd.mc.router.pretest")
public class PddMcRouterPretestConfig extends RocketMQDefaultConfig {

    private List<String> users = new ArrayList<>();

    private boolean enabled = true;

}
