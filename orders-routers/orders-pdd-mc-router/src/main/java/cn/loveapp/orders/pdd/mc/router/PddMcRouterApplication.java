package cn.loveapp.orders.pdd.mc.router;

import cn.loveapp.common.utils.LoggerHelper;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;

/**
 * @program: orders-services-group
 * @description: SchedulerApplication
 * @author: Jason
 * @create: 2018-11-29 17:53
 **/
@EnableScheduling
@EnableCaching
@SpringBootApplication(exclude = {RedisAutoConfiguration.class,
	RedisRepositoriesAutoConfiguration.class, DataSourceAutoConfiguration.class, MybatisAutoConfiguration.class,
	RedisReactiveAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class},
	scanBasePackages = {"cn.loveapp.orders.pdd.mc.router","cn.loveapp.orders.router.common"
		,"cn.loveapp.orders.consumer.common","cn.loveapp.orders.common"})
public class PddMcRouterApplication {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddMcRouterApplication.class);
	private static final String APOLLO_ENV = "env";

	/**
	 * Description 程序主入口
	 *
	 * <AUTHOR> Jiang
	 * @date 2018-09-21 23:37
	 */
	public static void main(String[] args) {
		new SpringApplicationBuilder(PddMcRouterApplication.class)
			.initializers((ConfigurableApplicationContext applicationContext) -> {
				//初始化apollo的env配置
				if (StringUtils.isEmpty(System.getProperty(APOLLO_ENV))) {
					String env = applicationContext.getEnvironment().getProperty(APOLLO_ENV);
					if (!StringUtils.isEmpty(env)) {
						System.setProperty(APOLLO_ENV, env);
					}
				}
			}).application().run(args);
	}

}
