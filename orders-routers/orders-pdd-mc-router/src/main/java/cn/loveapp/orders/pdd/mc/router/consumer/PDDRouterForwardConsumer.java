package cn.loveapp.orders.pdd.mc.router.consumer;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.response.AyLogisticsTraceSearchResponse;
import cn.loveapp.orders.common.config.rocketmq.RocketMQLogisticsAutoConsignConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQMultiMcRouterConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQPDDPromiseInfoConfig;
import cn.loveapp.orders.common.config.trade.OrderLogisticsTraceTypeConfig;
import cn.loveapp.orders.common.constant.OnsRateLimitConstant;
import cn.loveapp.orders.common.consumer.BaseDbIdOnsConsumer;
import cn.loveapp.orders.common.dao.es.CommonAyTradeSearchESDao;
import cn.loveapp.orders.common.dto.AyLogisticsStatusDTO;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.dto.proto.*;
import cn.loveapp.orders.common.proto.PullApiOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequestProto;
import cn.loveapp.orders.common.proto.PullLogisticsForAutoConsignRequestProto;
import cn.loveapp.orders.common.proto.PullPromiseInfoRequestProto;
import cn.loveapp.orders.common.proto.PullRefundGetRequestProto;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.proto.head.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.orders.common.service.TradeSendOrderHandleService;
import cn.loveapp.orders.common.service.UserCenterService;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.pdd.mc.router.config.PddMcRouterPretestConfig;
import cn.loveapp.orders.pdd.mc.router.config.RocketMQPDDToBtoBConfig;
import cn.loveapp.orders.pdd.mc.router.config.RocketMQPDDToFullInfoConfig;
import cn.loveapp.uac.response.UserInfoResponse;
import com.alibaba.fastjson.JSON;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 路由转发
 * @program: orders-services-group
 * @description: RouterForwardRunner
 * @author: Jason
 * @create: 2019-11-18 11:56
 **/
@Component
public class PDDRouterForwardConsumer extends BaseDbIdOnsConsumer {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PDDRouterForwardConsumer.class);

	@Autowired
	private TradeSendOrderHandleService tradeSendOrderHandleService;

	@Autowired
	private RocketMQPDDToBtoBConfig rocketMQPDDToBtoBConfig;

	@Autowired
	private UserCenterService userCenterService;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private DefaultMQProducer defaultMQProducer;

	@Autowired
	private RocketMQPDDPromiseInfoConfig rocketMQPDDPromiseInfoConfig;

	@Autowired
	private RocketMQLogisticsAutoConsignConfig rocketMQLogisticsAutoConsignConfig;

    @Autowired
    private PddMcRouterPretestConfig pddMcRouterPretestConfig;

    @Autowired
    private RocketMQMultiMcRouterConfig mcRouterConfig;

    @Autowired
    private OrderLogisticsTraceTypeConfig orderLogisticsTraceTypeConfig;

    @Autowired
	@Qualifier("commonAyTradeSearchESDao")
	private CommonAyTradeSearchESDao commonAyTradeSearchESDao;

    @Autowired
    private RocketMQPDDToFullInfoConfig rocketMQPDDToFullInfoConfig;

    /**
     * 需要转发售后消息的应用
     */
    @Value("${order.pdd.mc.router.forward.apps:trade,guanDian}")
    private List<String> needRefundForwardApps = new ArrayList<>();



    /**
     * 业务处理：真正处理的数据类
     *
     * @param message
     * @param messageDeserializationResult
     * @throws Exception
     */
    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws Exception {

		OrderMcNotifyProto orderDelayRequestProto = (OrderMcNotifyProto) messageDeserializationResult.getContent();
        OrderTradeRequest orderTradeRequest = orderDelayRequestProto.getTradeRequest();
        OrderPromiseRequest orderPromiseRequest = orderDelayRequestProto.getOrderPromiseRequest();
        LogisticsTraceRequest logisticsTraceRequest = orderDelayRequestProto.getLogisticsTraceRequest();
        OrderTradeRequest selfModifyRequest = orderDelayRequestProto.getSelfModifyRequest();
        DistributorOrderStatusChangeRequest distributorOrderStatusChangeRequest = orderDelayRequestProto.getDistributorOrderStatusChangeRequest();
        FuwuRequest fuwuRequest = orderDelayRequestProto.getFuwuRequest();


        //判断一下notify_trade notify_refunded
        boolean isRefundMsg = false;
        if (!Objects.isNull(orderDelayRequestProto.getOrderRefunedRequest())) {
            orderTradeRequest = orderDelayRequestProto.getOrderRefunedRequest();
            orderTradeRequest.setNotifyType(OrderMcNotifyProto.NotifyTypes.NOTIFY_REFUND.name());
            isRefundMsg = true;
        }

        String sellerId = null;
        if (orderTradeRequest != null) {
            sellerId = orderTradeRequest.getSellerId();
        } else if (orderPromiseRequest != null) {
            sellerId = orderPromiseRequest.getSellerId();
        } else if (logisticsTraceRequest != null){
            sellerId = logisticsTraceRequest.getSellerId();
        } else if (selfModifyRequest != null) {
            sellerId = selfModifyRequest.getSellerId();
        } else if (distributorOrderStatusChangeRequest != null) {
            sellerId = distributorOrderStatusChangeRequest.getSellerId();
        } else if (fuwuRequest != null) {
            sellerId = fuwuRequest.getSellerId();
        }

        // 如果是预发用户直接转发
        if (StringUtils.isNotEmpty(sellerId)) {
            if (isGrayForward(sellerId, message, messageDeserializationResult)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        }


        if (isRefundMsg) {
            //转发到refundget
            this.pddRefundGetForward(orderTradeRequest);
        }

        if (!Objects.isNull(orderTradeRequest)) {
            String sellerNick = getSellerNickFromRequest(orderTradeRequest);
            if (StringUtils.isNotEmpty(sellerNick)) {
                pddRouterForwardTo1688(orderTradeRequest, sellerNick);
                pddRouterForwardOrderToFullInfo(orderTradeRequest, sellerNick);
            } else {
                LOGGER.logError(orderTradeRequest.getSellerId(), orderTradeRequest.getTid(), "无法获取用户nick, 发送订单消息失败");
            }
        }

        if (rocketMQPDDPromiseInfoConfig.isEnabled()) {
            if (!Objects.isNull(orderPromiseRequest)) {
                pddOrderPromiseForward(orderPromiseRequest);
            }
        }

        if (!Objects.isNull(logisticsTraceRequest)) {
            if (rocketMQLogisticsAutoConsignConfig.isEnabled()) {
                logisticsTraceForwardToAutoConsign(logisticsTraceRequest);
            }

            // 转发multi-fullinfo 消费pdd物流消息
            pddRouterForwardLogisticsToFullinfo(logisticsTraceRequest);
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}

    /**
     * 灰度用户转发 router转router
     * @param sellerId
     * @param message
     * @param messageDeserializationResult
     * @return
     */
    private boolean isGrayForward(String sellerId, MessageExt message, MessageDeserializationResult messageDeserializationResult) {
        //校验是否灰度转发 - router转灰度router
        if (pddMcRouterPretestConfig.isEnabled() && pddMcRouterPretestConfig.getUsers().contains(sellerId)) {
            Map<String, String> newProperties = serializationProperties(message);
            rocketMqQueueHelper.pushBackToQueue(pddMcRouterPretestConfig.getTopic(), pddMcRouterPretestConfig.getTag(),
                message.getBody(), defaultMQProducer, 0, newProperties, messageDeserializationResult.getMessageLog());
            return true;
        }
        return false;
    }


    /**
     * 将pdd的物流消息转发到multi-fullinfo
     * @param logisticsTraceRequest
     */
    private void pddRouterForwardLogisticsToFullinfo(LogisticsTraceRequest logisticsTraceRequest){
        if (logisticsTraceRequest == null || StringUtils.isAnyEmpty(logisticsTraceRequest.getOutSid(), logisticsTraceRequest.getSellerId(), logisticsTraceRequest.getAppName(), logisticsTraceRequest.getPlatformId())) {
            LOGGER.logInfo("pdd发送fullinfo查询订单信息时，信息为空：" + JSON.toJSONString(logisticsTraceRequest));
            return;
        }
        RocketMQMultiMcRouterConfig.Config target = mcRouterConfig.getTargets().get(CommonPlatformConstants.PLATFORM_PDD);
        if(target == null){
            LOGGER.logError("没有配置目标信息");
            return;
        }
        String toTopic = target.getToTopic();
        List<String> tidList = logisticsTraceRequest.getTidList();
        String sellerId = logisticsTraceRequest.getSellerId();
        String platformId = logisticsTraceRequest.getPlatformId();
        String appName = logisticsTraceRequest.getAppName();
        String sellerNick = null;
        if (CollectionUtils.isEmpty(tidList)) {
            // 兼容旧物流消息
            List<AyTradeSearchES> ayTradeSearchList = commonAyTradeSearchESDao.queryTradeByInvoiceNo(sellerId, sellerId, logisticsTraceRequest.getOutSid(), platformId, appName);
            if (CollectionUtils.isEmpty(ayTradeSearchList)) {
                LOGGER.logError("通过运单号查询订单失败:" + JSON.toJSONString(logisticsTraceRequest));
                return;
            }
            tidList = ayTradeSearchList.stream().map(AyTradeSearchES::getTid).collect(Collectors.toList());
            sellerNick = ayTradeSearchList.get(0).getSellerNick();
        } else {
            UserInfoResponse userInfo = userCenterService.getUserInfo(sellerId, platformId, appName);
            if (Objects.isNull(userInfo)) {
                LOGGER.logError("找不到用户");
                return;
            }
            sellerNick = userInfo.getSellerNick();
        }

        for (String tid: tidList) {
            AyLogisticsTraceSearchResponse.TransitStepResult result = AyLogisticsTraceSearchResponse.getAyLogisticsStatus(logisticsTraceRequest);
            AyLogisticsStatusDTO ayLogisticsStatusDTO = new AyLogisticsStatusDTO();
            orderLogisticsTraceTypeConfig.setAyLogisticsStatus(result, ayLogisticsStatusDTO);
            //判断成分品爱用物流状态
            TmcOrdersRequest tmc = new TmcOrdersRequest(sellerNick, tid, logisticsTraceRequest.getTime(),
                new TmcOrdersRequest.TradeLogisticsTrace(null, logisticsTraceRequest.getOutSid(), ayLogisticsStatusDTO),
                appName);
            PullHistoryOrdersRequestProto proto = new PullHistoryOrdersRequestProto();
            ComLoveRpcInnerprocessRequestHead head = new ComLoveRpcInnerprocessRequestHead();
            head.setPlatformId(platformId);
            head.setAppName(appName);
            head.setSellerNick(sellerNick);
            head.setSellerId(sellerId);
            proto.setComLoveRpcInnerprocessRequestHead(head);
            proto.setTmcOrdersRequest(tmc);
            rocketMqQueueHelper.push(toTopic, target.getToTag(), proto, defaultMQProducer);
        }
    }

    private void pddRouterForwardTo1688(OrderTradeRequest orderTradeRequest, String sellerNick) {
        PullApiOrdersRequest pullApiOrdersRequest = createPullApiOrderRequest(orderTradeRequest, sellerNick);
        List<String> allowAppNameList = rocketMQPDDToBtoBConfig.getAllowAppNameList();
        if (BooleanUtils.isTrue(rocketMQPDDToBtoBConfig.getEnable()) && allowAppNameList != null
            && allowAppNameList.contains(orderTradeRequest.getAppName())) {
            tradeSendOrderHandleService.pushRequestFullInfoTopic(pullApiOrdersRequest, sellerNick,
                orderTradeRequest.getSellerId(), null, CommonPlatformConstants.PLATFORM_PDD,
                orderTradeRequest.getAppName(), rocketMQPDDToBtoBConfig.getTopic(), rocketMQPDDToBtoBConfig.getTag(),
                0);
        }
    }

    /**
     * 发送订单fullInfo消息
     *
     * @param orderTradeRequest
     */
    private void pddRouterForwardOrderToFullInfo(OrderTradeRequest orderTradeRequest, String sellerNick) {
        PullApiOrdersRequest pullApiOrdersRequest = createPullApiOrderRequest(orderTradeRequest, sellerNick);
        List<String> allowAppNameList = rocketMQPDDToFullInfoConfig.getAllowAppNameList();
        if (BooleanUtils.isTrue(rocketMQPDDToFullInfoConfig.getEnable()) && allowAppNameList != null
            && allowAppNameList.contains(orderTradeRequest.getAppName())) {
            tradeSendOrderHandleService.pushRequestFullInfoTopic(pullApiOrdersRequest, sellerNick,
                orderTradeRequest.getSellerId(), null, CommonPlatformConstants.PLATFORM_PDD,
                orderTradeRequest.getAppName(), rocketMQPDDToFullInfoConfig.getTopic(),
                rocketMQPDDToFullInfoConfig.getTag(), 0);
        }
    }

    private void pddRefundGetForward(OrderTradeRequest orderTradeRequest) {
        if (needRefundForwardApps.contains(orderTradeRequest.getAppName())) {
            String sellerNick = getSellerNickFromRequest(orderTradeRequest);
            PullRefundGetRequestProto proto = new PullRefundGetRequestProto();
            proto.setTid(orderTradeRequest.getTid());
            proto.setPlatformId(orderTradeRequest.getPlatformId());
            proto.setAppName(orderTradeRequest.getAppName());
            proto.setSellerId(orderTradeRequest.getSellerId());
            proto.setModified(orderTradeRequest.getModified());
            proto.setSellerNick(sellerNick);
            proto.setRefundId(StringUtils.isEmpty(orderTradeRequest.getRefundId()) ? null : orderTradeRequest.getRefundId());
            tradeSendOrderHandleService.pushRefundGetMessage(proto, 0);
        }
    }

	private void pddOrderPromiseForward(OrderPromiseRequest orderPromiseRequest) {
		String sellerNick = getSellerNickFromRequest(orderPromiseRequest);

		if (StringUtils.isEmpty(sellerNick)) {
			LOGGER.logWarn(orderPromiseRequest.getSellerNick(), orderPromiseRequest.getTidStr(), "未获取到用户sellerNick, request => " + JSON.toJSONString(orderPromiseRequest));
			return;
		}

		PullPromiseInfoRequestProto proto = new PullPromiseInfoRequestProto();
		proto.setSellerNick(sellerNick);
		proto.setAppName(orderPromiseRequest.getAppName());
		proto.setTid(orderPromiseRequest.getTidStr());
		proto.setPromiseId(orderPromiseRequest.getPromiseId());
		proto.setAction(orderPromiseRequest.getAction());

		// 转发到 pull-pdd-promiseinfo 进行消费
		rocketMqQueueHelper.push(rocketMQPDDPromiseInfoConfig.getTopic(), rocketMQPDDPromiseInfoConfig.getTag(), proto, defaultMQProducer);
	}

	/**
	 * 物流轨迹转发到预发货consumer
	 *
	 * @param logisticsTraceRequest
	 */
	private void logisticsTraceForwardToAutoConsign(LogisticsTraceRequest logisticsTraceRequest) {
		if (StringUtils.isEmpty(logisticsTraceRequest.getSellerId())) {
			// 过滤没有用户信息的物流推送
			LOGGER.logInfo("物流消息推送缺少用户信息, 跳过");
			return;
		}

		PullLogisticsForAutoConsignRequestProto proto = new PullLogisticsForAutoConsignRequestProto();
        proto.setSellerId(logisticsTraceRequest.getSellerId());
        proto.setAppName(logisticsTraceRequest.getAppName());
        proto.setStoreId(CommonPlatformConstants.PLATFORM_PDD);
        proto.setTidList(logisticsTraceRequest.getTidList());
		proto.setLogisticsId(logisticsTraceRequest.getLogisticsId());
		proto.setLogisticsCompanyCode(logisticsTraceRequest.getLogisticsCompanyCode());
		proto.setLogisticsInvoiceNo(logisticsTraceRequest.getOutSid());
		proto.setLogisticsTraceAction(logisticsTraceRequest.getAction());

		rocketMqQueueHelper.push(rocketMQLogisticsAutoConsignConfig.getTopic(), rocketMQLogisticsAutoConsignConfig.getTag(), proto, defaultMQProducer, rocketMQLogisticsAutoConsignConfig.getDelayTimeLevel());
	}

	private <T extends BaseOrderRequest> String getSellerNickFromRequest(T request) {
		String sellerNick = request.getSellerNick();
		if (StringUtils.isNotEmpty(sellerNick)) {
			return sellerNick;
		}
		UserInfoResponse userInfo = userCenterService.getUserInfo(request.getSellerId(), request.getAppName(), CommonPlatformConstants.PLATFORM_PDD);
		if (userInfo != null) {
			sellerNick = userInfo.getSellerNick();
		}
		return sellerNick;
	}

	@NotNull
	private PullApiOrdersRequest createPullApiOrderRequest(OrderTradeRequest orderTradeRequest, String sellerNick) {
		PullHistoryOrdersRequest pullHistoryOrdersRequest = new PullHistoryOrdersRequest();
		pullHistoryOrdersRequest.setTid(orderTradeRequest.getTid());
		pullHistoryOrdersRequest.setModified(orderTradeRequest.getModified());
		pullHistoryOrdersRequest.setTag(orderTradeRequest.getStatus());
		pullHistoryOrdersRequest.setTaoStatus(orderTradeRequest.getTaoStatus());
        pullHistoryOrdersRequest.setRefundId(orderTradeRequest.getRefundId());
        pullHistoryOrdersRequest.setRefundOperation(orderTradeRequest.getRefundOperation());
        pullHistoryOrdersRequest.setRefundType(orderTradeRequest.getRefundType());
		pullHistoryOrdersRequest.setNick(sellerNick);
		List<PullHistoryOrdersRequest> pullHistoryOrdersRequests = Collections.singletonList(pullHistoryOrdersRequest);

		//封装协议包
		PullApiOrdersRequest pullApiOrdersRequest = new PullApiOrdersRequest();
		pullApiOrdersRequest.setPullHistoryOrdersRequests(pullHistoryOrdersRequests);
		pullApiOrdersRequest.setEndPoint(false);

		return pullApiOrdersRequest;
	}

	public PDDRouterForwardConsumer(MeterRegistry registry, Environment environment) {
        super(registry, "pdd-ONS路由转发器.QPS", OnsRateLimitConstant.ROCKETMQ_ORDERS_RATELIMIT_PDD_MC,
            environment, true, true);
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt message) {
        return OrderMcNotifyProto.class;
    }

}
