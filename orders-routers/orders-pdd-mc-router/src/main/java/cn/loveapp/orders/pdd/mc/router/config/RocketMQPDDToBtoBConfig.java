package cn.loveapp.orders.pdd.mc.router.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @email  <EMAIL>
 * @create 2019-12-27 下午5:14
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "rocketmq.pdd.mc.router.pdd2btob")
public class RocketMQPDDToBtoBConfig {

	private Boolean enable;

	private String topic;

	private String tag;

	private List<String> allowAppNameList = Lists.newArrayList("distribute");

}
