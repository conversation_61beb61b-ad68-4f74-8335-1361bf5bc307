package cn.loveapp.orders.pdd.mc.router.config;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.rocketmq.RocketMQPDDMcRouterConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoAppConfig;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.pdd.mc.router.consumer.PDDRouterForwardConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragelyByCircle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

/**
 * @program: orders-services-group
 * @description: OrdersBatchDataOnsConsumerConfig
 * @author: Jason
 * @create: 2018-12-18 17:46
 **/
@Configuration
public class OrdersPDDMcRouterConsumerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersPDDMcRouterConsumerConfig.class);

	@Autowired
	private RocketMQTaobaoAppConfig rocketMQTaobaoAppConfig;

	@Autowired
	private RocketMQPDDMcRouterConfig rocketMQPDDMcRouterConfig;

	private DefaultMQPushConsumer consumer = null;

	@Bean(destroyMethod = "", name = "ordersPDDMcRouterConsumer")
	public DefaultMQPushConsumer ordersPDDMcRouterConsumer() {
		//启动ONS消息队列
		try {
			consumer = new DefaultMQPushConsumer(rocketMQPDDMcRouterConfig.getConsumerId());
			consumer.setNamesrvAddr(rocketMQTaobaoAppConfig.getNamesrvAddr());
			consumer.setConsumeThreadMax(rocketMQPDDMcRouterConfig.getMaxThreadNum());
			consumer.setConsumeThreadMin(rocketMQPDDMcRouterConfig.getMaxThreadNum());
			consumer.setAllocateMessageQueueStrategy(new AllocateMessageQueueAveragelyByCircle());
		} catch (Exception e) {
			LOGGER.logError("create ordersPDDMcRouterConsumer failed", e);
		}
		return consumer;
	}

	@Bean(name = "ordersPDDMcRouterConsumerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager() {
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2018/11/9
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>, Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired(required = false)
		@Qualifier("ordersPDDMcRouterConsumer")
		private DefaultMQPushConsumer ordersPDDMcRouterConsumer;

		@Autowired
		private PDDRouterForwardConsumer routerForwardConsumer;

		@Autowired
		private RocketMQPDDMcRouterConfig rocketMQPDDMcRouterConfig;

		@Autowired
		private RocketMqQueueHelper rocketMqQueueHelper;

		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS消费者
			if (ordersPDDMcRouterConsumer != null) {
				ordersPDDMcRouterConsumer
					.subscribe(rocketMQPDDMcRouterConfig.getTopic(), rocketMQPDDMcRouterConfig
						.getTag());
				ordersPDDMcRouterConsumer.registerMessageListener(routerForwardConsumer);
				LOGGER.logInfo("ordersPDDMcRouterConsumer is startting");
				ordersPDDMcRouterConsumer.start();
				LOGGER.logInfo(
					"ordersPDDMcRouterConsumer is started, Topic: " + rocketMQPDDMcRouterConfig.getTopic()
						+ " Tag: " + rocketMQPDDMcRouterConfig.getTag());
			}
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
				return;
			}
			if (ordersPDDMcRouterConsumer != null) {
				LOGGER.logInfo("正在关闭批量入库ordersPDDMcRouterConsumer...");
				try {
					routerForwardConsumer.stop();
					rocketMqQueueHelper.stopOnsConsumer(ordersPDDMcRouterConsumer);
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("批量入库ordersPDDMcRouterConsumer已关闭");
			}
		}

		@Override
		public int getOrder() {
			return Ordered.HIGHEST_PRECEDENCE;
		}
	}
}
