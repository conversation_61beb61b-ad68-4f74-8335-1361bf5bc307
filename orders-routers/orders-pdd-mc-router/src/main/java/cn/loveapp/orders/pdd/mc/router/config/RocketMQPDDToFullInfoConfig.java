package cn.loveapp.orders.pdd.mc.router.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-12 17:14
 * @description: pdd消息转发fullInfo配置
 */

@Configuration
@Data
@ConfigurationProperties(prefix = "rocketmq.pdd.mc.router.pdd2fullinfo")
public class RocketMQPDDToFullInfoConfig {
    private Boolean enable;

    private String topic;

    private String tag;

    private List<String> allowAppNameList = Lists.newArrayList("tradeERP");
}
