<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>orders-services-group</artifactId>
		<groupId>cn.loveapp.orders</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<name>订单-消费-基础组件</name>
	<description>爱用基础服务-订单服务-消费者基础组件</description>
	<artifactId>orders-consumer-common</artifactId>
	<version>1.1-SNAPSHOT</version>

	<organization>
		<name>Loveapp Inc.</name>
		<url>http://www.aiyongbao.com</url>
	</organization>

	<properties>
		<orders-common.version>1.0-SNAPSHOT</orders-common.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-api</artifactId>
			<version>${orders-api.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-common</artifactId>
			<version>${orders-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-spring-boot-web-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao</groupId>
			<artifactId>topsdk</artifactId>
		</dependency>
		<!-- 阿里淘宝 -->
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
		</dependency>
		<!--测试-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>cn.loveapp.logistics</groupId>
            <artifactId>logistics-api</artifactId>
            <version>${logistics-api.version}</version>
        </dependency>
    </dependencies>

</project>
