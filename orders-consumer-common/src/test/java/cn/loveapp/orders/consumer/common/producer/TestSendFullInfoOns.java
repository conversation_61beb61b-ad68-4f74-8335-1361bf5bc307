package cn.loveapp.orders.consumer.common.producer;

import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoAppConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoHistoryAppConfig;
import cn.loveapp.orders.common.config.rocketmq.producer.OrdersDefaultProducerConfig;
import cn.loveapp.orders.common.proto.PullApiOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequestProto;
import cn.loveapp.orders.common.proto.head.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @program: orders-services-group
 * @description: TestSendFullInfoOns
 * @author: Jason
 * @create: 2018-12-11 17:03
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = {RocketMqQueueHelper.class, RocketMQTaobaoHistoryAppConfig.class, OrdersDefaultProducerConfig.class,
		RocketMQTaobaoAppConfig.class, ConfigurationPropertiesAutoConfiguration.class})
@ActiveProfiles("test")
public class TestSendFullInfoOns {

	@Autowired
	private DefaultMQProducer ordersPullApiHistoryOnsProducer;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private RocketMQTaobaoHistoryAppConfig rocketMQTaobaoHistoryAppConfig;

	@Ignore
	@Test
	public void sendFullInfoOnsMsg() {
		String sellerNick1 = "蝶衣无心";
////		String sellerNick2 = "凡菲兔旗舰店";
////		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
////		comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick1);
////		PullHistoryOrdersRequestProto pullSoldGetApiOrdersRequest = new PullHistoryOrdersRequestProto();
////		pullSoldGetApiOrdersRequest.setRuleAttr(PullHistoryRuleConstant.PULL_NEARLY_THREE_MONTHS_ORDERS);
////		PullSoldGetApiOrdersRequestProto pullSoldGetApiOrdersRequestProto = new PullSoldGetApiOrdersRequestProto();
////		pullSoldGetApiOrdersRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
////		pullSoldGetApiOrdersRequestProto.setPullHistoryApiOrdersRequest(pullSoldGetApiOrdersRequest);
//
		//封装协议头
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick1);
		//封装协议体
		List<PullHistoryOrdersRequest> pullHistoryOrdersRequests = new ArrayList<>();
		PullHistoryOrdersRequest pullHistoryOrdersRequest = new PullHistoryOrdersRequest();
		pullHistoryOrdersRequest.setModified(LocalDateTime.now());
		pullHistoryOrdersRequest.setTid("338762593022298161");
		pullHistoryOrdersRequests.add(pullHistoryOrdersRequest);
		PullApiOrdersRequest pullApiOrdersRequest = new PullApiOrdersRequest();
		pullApiOrdersRequest.setPullHistoryOrdersRequests(pullHistoryOrdersRequests);
		//封装协议包
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		pullHistoryOrdersRequestProto.setPullApiOrdersRequest(pullApiOrdersRequest);

//		for (int i = 0; i < 10; i++) {
			rocketMqQueueHelper.push(rocketMQTaobaoHistoryAppConfig.getTopic(), rocketMQTaobaoHistoryAppConfig.getTag(),
				pullHistoryOrdersRequestProto, ordersPullApiHistoryOnsProducer);
//		}
	}
}
