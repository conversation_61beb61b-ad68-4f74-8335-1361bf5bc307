package cn.loveapp.orders.consumer.common.service.impl;

import static org.junit.Assert.fail;
import static org.mockito.BDDMockito.*;

import java.time.LocalDateTime;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.taobao.api.domain.Trade;
import com.taobao.api.internal.util.TaobaoUtils;
import com.taobao.api.response.RefundGetResponse;
import com.taobao.api.response.TradeFullinfoGetResponse;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.convert.CommonConvertMapper;
import cn.loveapp.orders.common.entity.JdpTbRefund;
import cn.loveapp.orders.common.entity.JdpTbTrade;
import cn.loveapp.orders.common.service.PrintService;
import cn.loveapp.orders.common.service.TaobaoAuthService;
import cn.loveapp.orders.common.service.impl.PrintServiceImpl;
import cn.loveapp.orders.common.service.impl.TaobaoAuthServiceImpl;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.consumer.common.bo.TradeRefundHandleBo;
import cn.loveapp.orders.consumer.common.service.MigrateRdsOrderService;
import cn.loveapp.orders.consumer.common.service.MigrateTradeHandleService;
import cn.loveapp.orders.consumer.common.service.TradeRefundHandleService;


/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 00:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {MigrateRdsOrderServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, TaobaoAuthServiceImpl.class,
		PrintServiceImpl.class, MigrateTradeHandleServiceImpl.class, MigrateRdsOrderServiceImpl.class
	})
public class MigrateRdsOrderServiceImplTest {

	@MockBean
	private TaobaoAuthService taobaoAuthService;

	@MockBean
	private PrintService printService;

	@MockBean
	private MigrateTradeHandleService migrateTradeHandleService;

	@MockBean
	private TradeRefundHandleService tradeRefundHandleService;

	@SpyBean
	private MigrateRdsOrderService migrateRdsOrderService;

	private String sellerId = "1";

	@Test
	public void putJdpTradeRecords() throws Exception{
		JdpTbTrade jdpTbTrade = generateSingleMockData();
		Trade trade = generateTradeMockData(jdpTbTrade);

		AyTrade ayTrade = new AyTrade();
		BeanUtils.copyProperties(trade, ayTrade);

		when(taobaoAuthService.authorization(anyString(), anyString())).thenReturn(null);
		migrateRdsOrderService.putJdpTradeRecords(sellerId, jdpTbTrade, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false);
		verify(migrateTradeHandleService).executeBatchOrders(eq(ayTrade)
			, eq(null), eq(sellerId), eq("小文思勇"),
			eq("224473807924134741"), eq(DateUtil.parseString("2018-11-11 04:59:09")), eq(CommonPlatformConstants.PLATFORM_TAO),
			eq(CommonAppConstants.APP_TRADE), eq(false));
	}

	@Test
	public void putJdpTradeRecords2() throws Exception {
		JdpTbTrade jdpTbTrade = generateSingleMockData();
		Trade trade = generateTradeMockData(jdpTbTrade);

		AyTrade ayTrade = new AyTrade();
		BeanUtils.copyProperties(trade, ayTrade);

		when(taobaoAuthService.authorization(anyString(), anyString())).thenReturn("xxxxyyy");
		migrateRdsOrderService.putJdpTradeRecords(sellerId, jdpTbTrade, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false);
		verify(migrateTradeHandleService).executeBatchOrders(eq(ayTrade)
			, eq("xxxxyyy"), eq(sellerId), eq("小文思勇"),
			eq("224473807924134741"), eq(DateUtil.parseString("2018-11-11 04:59:09")), eq(CommonPlatformConstants.PLATFORM_TAO),
			eq(CommonAppConstants.APP_TRADE), eq(false));
	}

	@Test
	public void putJdpTradeRecords3() throws Exception {
		// 时效订单测试
		JdpTbTrade jdpTbTrade = generateTimingOrderMockData("tmallpromise.arrival.timing");
		Trade trade = generateTimingOrderTradeMockData(jdpTbTrade);

		AyTrade ayTrade = new AyTrade();
		BeanUtils.copyProperties(trade, ayTrade);

		when(taobaoAuthService.authorization(anyString(), anyString())).thenReturn("xxxxyyy");
		migrateRdsOrderService.putJdpTradeRecords(sellerId, jdpTbTrade, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false);
		verify(migrateTradeHandleService).executeBatchOrders(eq(ayTrade)
			, eq("xxxxyyy"), eq(sellerId), eq("小文思勇"),
			eq("224473807924134741"), eq(DateUtil.parseString("2018-11-11 04:59:09")), eq(CommonPlatformConstants.PLATFORM_TAO),
			eq(CommonAppConstants.APP_TRADE), eq(false));
	}

	@Test
	public void putJdpTradeRecords4() throws Exception {
		// 时效订单测试
		JdpTbTrade jdpTbTrade = generateTimingOrderMockData("tmallpromise.arrival.timing,tmallpromise.consign.timing");
		Trade trade = generateTimingOrderTradeMockData(jdpTbTrade);

		AyTrade ayTrade = new AyTrade();
		BeanUtils.copyProperties(trade, ayTrade);

		when(taobaoAuthService.authorization(anyString(), anyString())).thenReturn("xxxxyyy");
		migrateRdsOrderService.putJdpTradeRecords(sellerId, jdpTbTrade, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false);
		verify(migrateTradeHandleService).executeBatchOrders(eq(ayTrade)
			, eq("xxxxyyy"), eq(sellerId), eq("小文思勇"),
			eq("224473807924134741"), eq(DateUtil.parseString("2018-11-11 04:59:09")), eq(CommonPlatformConstants.PLATFORM_TAO),
			eq(CommonAppConstants.APP_TRADE), eq(false));
	}

	@Test
	public void putPddTradeRecords() {
		AyTrade trade = new AyTrade();
		String topSession = "topSession";
		String sellerNick = "sellerNick";
		String tid = "tid";
		LocalDateTime jdpModified = LocalDateTime.now();
		String platformId = "platformId";
		String appName = "appName";
        migrateRdsOrderService.putTradeRecords(trade, topSession, sellerNick, sellerId, tid, jdpModified, platformId, appName);
		verify(migrateTradeHandleService).executeBatchOrders(trade, topSession, sellerId, sellerNick, tid, jdpModified, platformId, appName, false);

		migrateRdsOrderService.putTradeRecords(trade, topSession, sellerNick, sellerId, tid, jdpModified, platformId, appName);
		verify(migrateTradeHandleService).executeBatchOrders(trade, topSession, sellerId, sellerNick, tid, jdpModified, platformId, appName, false);
	}

	@Test
	public void putJdpRefundRecords() {
		JdpTbRefund jdpTbRefund = generateJdpTbRefund();
		String sellerId = "3936370796";
		String storeId = CommonPlatformConstants.PLATFORM_TAO;
		String appName = CommonAppConstants.APP_TRADE;
		try {
            migrateRdsOrderService.putJdpRefundRecords(sellerId, jdpTbRefund, storeId, appName, false);
			TradeRefundHandleBo handleBo = new TradeRefundHandleBo();
			handleBo.setSellerNick(jdpTbRefund.getSellerNick());
			handleBo.setSellerId(sellerId);
			handleBo.setStoreId(storeId);
			handleBo.setTid(jdpTbRefund.getTid().toString());
			handleBo.setOid(jdpTbRefund.getOid().toString());

			RefundGetResponse refundGetResponse = TaobaoUtils.parseResponse(
					jdpTbRefund.getJdpResponse(), RefundGetResponse.class);

			handleBo.setRefund(CommonConvertMapper.INSTANCE.toAyRefund(refundGetResponse.getRefund()));
			ArgumentCaptor<TradeRefundHandleBo> argumentCaptor = ArgumentCaptor.forClass(TradeRefundHandleBo.class);
			verify(tradeRefundHandleService).putTradeData(argumentCaptor.capture(), null, anyBoolean());
			TradeRefundHandleBo value = argumentCaptor.getValue();
			// 把入参取出来判断是否与预期值相等
			Assert.assertEquals(JSON.toJSONString(handleBo), JSON.toJSONString(value));
		} catch (Exception e) {
			fail();
		}
	}

	private JdpTbTrade generateSingleMockData() {
		JdpTbTrade jdpTbTrade = new JdpTbTrade();
		jdpTbTrade.setTid(224473807924134741L);
		jdpTbTrade.setStatus("TRADE_FINISHED");
		jdpTbTrade.setType("fixed");
		jdpTbTrade.setSellerNick("小文思勇");
		jdpTbTrade.setBuyerNick("~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~");
		jdpTbTrade.setCreated(DateUtil.parseString("2018-09-21 19:18:28"));
		jdpTbTrade.setModified(DateUtil.parseString("2018-11-11 00:33:58"));
		jdpTbTrade.setJdpHashcode("1847718010");
		jdpTbTrade.setJdpResponse("{\"trade_fullinfo_get_response\":{\"trade\":{\"tid\":224473807924134741,\"tid_str\":\"224473807924134741\",\"status\":\"TRADE_FINISHED\",\"type\":\"fixed\",\"seller_nick\":\"小文思勇\",\"buyer_nick\":\"~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~\",\"created\":\"2018-09-21 19:18:28\",\"modified\":\"2018-11-11 00:33:58\",\"encrypt_alipay_id\":\"~NtkLP5vgaTSBLvk2cOx8BrEj69hZ8/ajUj0pLlXFo5s=~1~\",\"adjust_fee\":\"0.00\",\"alipay_id\":2088232330568785,\"alipay_no\":\"2018092122001168780589596425\",\"alipay_point\":0,\"available_confirm_fee\":\"0.00\",\"buyer_alipay_no\":\"~to3Mc5GUwfX18yqfIip8pg==~1~\",\"buyer_area\":\"陕西电信\",\"buyer_cod_fee\":\"0.00\",\"buyer_email\":\"\",\"buyer_ip\":\"MzYuNDYuMTAuMTM4\",\"buyer_message\":\"创意奖状:最佳吃货奖。   代写贺卡内容:永远的吃货！送你一个空投。(唉呀妈呀，真香！！！)\",\"buyer_obtain_point_fee\":0,\"buyer_rate\":true,\"cod_fee\":\"0.00\",\"cod_status\":\"NEW_CREATED\",\"coupon_fee\":0,\"commission_fee\":\"0.00\",\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"end_time\":\"2018-10-02 08:29:54\",\"has_post_fee\":true,\"has_yfx\":false,\"is_3D\":false,\"is_brand_sale\":false,\"is_daixiao\":false,\"is_force_wlb\":false,\"is_sh_ship\":false,\"is_lgtype\":false,\"is_part_consign\":false,\"is_wt\":false,\"is_gift\":false,\"num\":1,\"num_iid\":************,\"new_presell\":false,\"nr_shop_guide_id\":\"\",\"nr_shop_guide_name\":\"\",\"orders\":{\"order\":[{\"adjust_fee\":\"0.00\",\"buyer_rate\":true,\"cid\":50010535,\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"divide_order_fee\":\"29.80\",\"end_time\":\"2018-10-02 08:29:54\",\"invoice_no\":\"3800540523278\",\"is_daixiao\":false,\"is_oversold\":false,\"logistics_company\":\"韵达快递\",\"num\":1,\"num_iid\":************,\"oid\":224473807924134741,\"oid_str\":\"224473807924134741\",\"order_from\":\"WAP,WAP\",\"payment\":\"29.80\",\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"price\":\"29.80\",\"refund_status\":\"NO_REFUND\",\"seller_rate\":true,\"seller_type\":\"C\",\"shipping_type\":\"express\",\"sku_id\":\"3861953163887\",\"sku_properties_name\":\"口味:空投箱礼盒\",\"snapshot_url\":\"n:224473807924134741_1\",\"status\":\"TRADE_FINISHED\",\"title\":\"抖音空投网红零食大礼包一箱整箱超大吃的休闲食品成人款组合混装\",\"total_fee\":\"29.80\"}]},\"pay_time\":\"2018-09-21 19:19:29\",\"payment\":\"29.80\",\"pcc_af\":0,\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"platform_subsidy_fee\":\"0.00\",\"point_fee\":0,\"post_fee\":\"0.00\",\"price\":\"29.80\",\"real_point_fee\":0,\"received_payment\":\"29.80\",\"receiver_address\":\"建民街道安康大道安康学院江北新校区\",\"receiver_city\":\"安康市\",\"receiver_country\":\"\",\"receiver_district\":\"汉滨区\",\"receiver_mobile\":\"$75e7GGTpTbt8UUHJRxEyBg==$c2KVCN1iPe84tRP5NpQExA==$1$$\",\"receiver_name\":\"~NXErP2LLwlgbByngA8fDhw==~+W+3+CLX~1~~\",\"receiver_state\":\"陕西省\",\"receiver_town\":\"建民街道\",\"receiver_zip\":\"000000\",\"seller_alipay_no\":\"<EMAIL>\",\"seller_can_rate\":false,\"seller_cod_fee\":\"0.00\",\"seller_email\":\"<EMAIL>\",\"seller_flag\":0,\"seller_mobile\":\"18107002287\",\"seller_name\":\"刘小文\",\"seller_rate\":true,\"service_tags\":{\"logistics_tag\":[{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"origAreaId=610902;consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"},{\"service_tag\":\"lgType=-4\",\"service_type\":\"FAST\"}]},\"order_id\":\"224473807924134741\"},{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"}]},\"order_id\":\"224473807924134741\"}]},\"service_type\":\"\",\"shipping_type\":\"express\",\"sid\":\"224473807924134741\",\"snapshot_url\":\"n:224473807924134741_1\",\"title\":\"小文思勇零食大礼包\",\"total_fee\":\"29.80\",\"trade_from\":\"WAP,WAP\",\"you_xiang\":false}}}");
		jdpTbTrade.setJdpCreated(DateUtil.parseString("2018-11-11 04:59:09"));
		jdpTbTrade.setJdpModified(DateUtil.parseString("2018-11-11 04:59:09"));
		return jdpTbTrade;
	}

	private JdpTbRefund generateJdpTbRefund() {
		JdpTbRefund jdpTbRefund = new JdpTbRefund();
		jdpTbRefund.setTid(1159772897615871945L);
		jdpTbRefund.setOid(1159772897615871945L);
		jdpTbRefund.setStatus("WAIT_SELLER_CONFIRM_GOODS");
		jdpTbRefund.setSellerNick("赵东昊的测试店铺");
		jdpTbRefund.setBuyerNick("~1pAM/D01zt/Re8/tsae9FxoesWyoYdFVHUd/tVCe69I=~wFgGxufqSVN9SP4ABosml/zN~1~~");
		jdpTbRefund.setCreated(DateUtil.parseString("2020-08-03 20:18:06"));
		jdpTbRefund.setModified(DateUtil.parseString("2020-08-03 20:19:25"));
		jdpTbRefund.setJdpHashcode("-1019630503");
		jdpTbRefund.setJdpResponse("{\"refund_get_response\":{\"refund\":{\"refund_id\":\"75360641881874519\",\"status\":\"WAIT_SELLER_CONFIRM_GOODS\",\"seller_nick\":\"赵东昊的测试店铺\",\"buyer_nick\":\"~1pAM/D01zt/Re8/tsae9FxoesWyoYdFVHUd/tVCe69I=~wFgGxufqSVN9SP4ABosml/zN~1~~\",\"tid\":1159772897615871945,\"oid\":1159772897615871945,\"created\":\"2020-08-03 20:18:06\",\"modified\":\"2020-08-03 20:19:25\",\"address\":\"赵东昊， 18344343678， 浙江省宁波市宁海县   测试一号， 000000\",\"advance_status\":0,\"alipay_no\":\"2020080322001145881431514678\",\"attribute\":\";enfunddetail:1;reason:401457;gaia:2;ee_trace_id:0b133b4615964571655884606e3fff;bizCode:taobao.general.refund;lastOrder:0;tod:*********;newRefund:rp2;leavesCat:50019776;logisticsOrderCode:ZJS000096554695;intentReturnGoodsType:RETURN_BY_SELF;opRole:buyer;prepaidFailure:TAOBAO_CREDIT_SCORE_NOT_MEET;apply_reason_text:拍错/多拍/效果不好/不喜欢;apply_init_refund_fee:1;itemBuyAmount:1;apply_text_id:401457;userCredit:0;sdkCode:ali.china.taobao;interceptStatus:0;seller_batch:true;logisticsCompanyName:宅急送;restartForXiaoer:1;rootCat:50025004;tos:1;ol_tf:1;ability:1;sku:*************|尺寸#3B40X40cm#3A颜色分类#3B天蓝色#3A材质#3B泰麂绒;sgr:1;bgmtc:2020-08-03 19#3B55#3B16;appName:refundplatform2;payMode:alipay;sellerDoRefundNick:赵东昊的测试店铺;workflowName:return_and_refund;rightsSuspend:1;shop_name:爱用交易测试店铺;ttid:h5;abnormal_dispute_status:0;seller_audit:0;rp3:1;seller_agreed_refund_fee:1;stopAgree:0;itemPrice:1;isVirtual:0;EXmrf:1;refundFrom:2;\",\"company_name\":\"宅急送\",\"cs_status\":1,\"desc\":\"\",\"good_return_time\":\"2020-08-03 20:19:26\",\"good_status\":\"BUYER_RETURNED_GOODS\",\"has_good_return\":true,\"num\":1,\"num_iid\":618780453838,\"operation_contraint\":\"null\",\"order_status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"outer_id\":\"还是睡觉手机\",\"payment\":\"0.00\",\"price\":\"0.01\",\"reason\":\"拍错/多拍/效果不好/不喜欢\",\"refund_fee\":\"0.01\",\"refund_phase\":\"onsale\",\"refund_remind_timeout\":{\"exist_timeout\":true,\"remind_type\":8,\"timeout\":\"2020-08-13 20:19:26\"},\"refund_version\":1596457086786,\"shipping_type\":\"express\",\"sid\":\"ZJS000094342382\",\"sku\":\"*************|尺寸:40X40cm;颜色分类:天蓝色;材质:泰麂绒\",\"title\":\"自动化测试专用商品1\",\"total_fee\":\"0.01\"}}}");
		jdpTbRefund.setJdpCreated(DateUtil.parseString("2020-08-03 20:18:07"));
		jdpTbRefund.setJdpModified(DateUtil.parseString("2020-08-03 20:19:26"));
		return jdpTbRefund;
	}

	private JdpTbTrade generateTimingOrderMockData(String promiseService) throws Exception{
		JdpTbTrade jdpTbTrade = new JdpTbTrade();
		jdpTbTrade.setTid(224473807924134741L);
		jdpTbTrade.setStatus("TRADE_FINISHED");
		jdpTbTrade.setType("fixed");
		jdpTbTrade.setSellerNick("小文思勇");
		jdpTbTrade.setBuyerNick("~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~");
		jdpTbTrade.setCreated(DateUtil.parseString("2018-09-21 19:18:28"));
		jdpTbTrade.setModified(DateUtil.parseString("2018-11-11 00:33:58"));
		jdpTbTrade.setJdpHashcode("1847718010");
		jdpTbTrade.setJdpResponse("{\"trade_fullinfo_get_response\":{\"trade\":{"
			+ "\"timing_promise\":\"tmallPromise\","
			+ "\"promise_service\":\"" + promiseService + "\","
			+ "\"trade_attr\":\"{"
			+ "\\\"cutoffMinutes\\\":\\\"960\\\""
			+ ",\\\"collectTime\\\":\\\"2019-07-03 23:59:59\\\""
			+ ",\\\"deliveryTime\\\":\\\"2019-07-02 23:59:59\\\""
			+ ",\\\"dispatchTime\\\":\\\"2019-07-04 23:59:59\\\""
			+ ",\\\"signTime\\\":\\\"2019-07-04 23:59:00\\\""
			+ ",\\\"esTime\\\":\\\"1\\\""
			+ ",\\\"esDate\\\":\\\"2019-04-12\\\""
			+ ",\\\"esRange\\\":\\\"09:00-21:00\\\""
			+ ",\\\"osDate\\\":\\\"2019-04-13\\\""
			+ ",\\\"osRange\\\":\\\"09:00-22:00\\\""
			+ ",\\\"erpHold\\\":\\\"0\\\",\\\"cnService\\\":\\\"81\\\"}\","
			+ "\"tid\":224473807924134741,\"tid_str\":\"224473807924134741\",\"status\":\"TRADE_FINISHED\",\"type\":\"fixed\",\"seller_nick\":\"小文思勇\",\"buyer_nick\":\"~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~\",\"created\":\"2018-09-21 19:18:28\",\"modified\":\"2018-11-11 00:33:58\",\"encrypt_alipay_id\":\"~NtkLP5vgaTSBLvk2cOx8BrEj69hZ8/ajUj0pLlXFo5s=~1~\",\"adjust_fee\":\"0.00\",\"alipay_id\":2088232330568785,\"alipay_no\":\"2018092122001168780589596425\",\"alipay_point\":0,\"available_confirm_fee\":\"0.00\",\"buyer_alipay_no\":\"~to3Mc5GUwfX18yqfIip8pg==~1~\",\"buyer_area\":\"陕西电信\",\"buyer_cod_fee\":\"0.00\",\"buyer_email\":\"\",\"buyer_ip\":\"MzYuNDYuMTAuMTM4\",\"buyer_message\":\"创意奖状:最佳吃货奖。   代写贺卡内容:永远的吃货！送你一个空投。(唉呀妈呀，真香！！！)\",\"buyer_obtain_point_fee\":0,\"buyer_rate\":true,\"cod_fee\":\"0.00\",\"cod_status\":\"NEW_CREATED\",\"coupon_fee\":0,\"commission_fee\":\"0.00\",\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"end_time\":\"2018-10-02 08:29:54\",\"has_post_fee\":true,\"has_yfx\":false,\"is_3D\":false,\"is_brand_sale\":false,\"is_daixiao\":false,\"is_force_wlb\":false,\"is_sh_ship\":false,\"is_lgtype\":false,\"is_part_consign\":false,\"is_wt\":false,\"is_gift\":false,\"num\":1,\"num_iid\":************,\"new_presell\":false,\"nr_shop_guide_id\":\"\",\"nr_shop_guide_name\":\"\",\"orders\":{\"order\":[{\"adjust_fee\":\"0.00\",\"buyer_rate\":true,\"cid\":50010535,\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"divide_order_fee\":\"29.80\",\"end_time\":\"2018-10-02 08:29:54\",\"invoice_no\":\"3800540523278\",\"is_daixiao\":false,\"is_oversold\":false,\"logistics_company\":\"韵达快递\",\"num\":1,\"num_iid\":************,\"oid\":224473807924134741,\"oid_str\":\"224473807924134741\",\"order_from\":\"WAP,WAP\",\"payment\":\"29.80\",\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"price\":\"29.80\",\"refund_status\":\"NO_REFUND\",\"seller_rate\":true,\"seller_type\":\"C\",\"shipping_type\":\"express\",\"sku_id\":\"3861953163887\",\"sku_properties_name\":\"口味:空投箱礼盒\",\"snapshot_url\":\"n:224473807924134741_1\",\"status\":\"TRADE_FINISHED\",\"title\":\"抖音空投网红零食大礼包一箱整箱超大吃的休闲食品成人款组合混装\",\"total_fee\":\"29.80\"}]},\"pay_time\":\"2018-09-21 19:19:29\",\"payment\":\"29.80\",\"pcc_af\":0,\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"platform_subsidy_fee\":\"0.00\",\"point_fee\":0,\"post_fee\":\"0.00\",\"price\":\"29.80\",\"real_point_fee\":0,\"received_payment\":\"29.80\",\"receiver_address\":\"建民街道安康大道安康学院江北新校区\",\"receiver_city\":\"安康市\",\"receiver_country\":\"\",\"receiver_district\":\"汉滨区\",\"receiver_mobile\":\"$75e7GGTpTbt8UUHJRxEyBg==$c2KVCN1iPe84tRP5NpQExA==$1$$\",\"receiver_name\":\"~NXErP2LLwlgbByngA8fDhw==~+W+3+CLX~1~~\",\"receiver_state\":\"陕西省\",\"receiver_town\":\"建民街道\",\"receiver_zip\":\"000000\",\"seller_alipay_no\":\"<EMAIL>\",\"seller_can_rate\":false,\"seller_cod_fee\":\"0.00\",\"seller_email\":\"<EMAIL>\",\"seller_flag\":0,\"seller_mobile\":\"18107002287\",\"seller_name\":\"刘小文\",\"seller_rate\":true,\"service_tags\":{\"logistics_tag\":[{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"origAreaId=610902;consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"},{\"service_tag\":\"lgType=-4\",\"service_type\":\"FAST\"}]},\"order_id\":\"224473807924134741\"},{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"}]},\"order_id\":\"224473807924134741\"}]},\"service_type\":\"\",\"shipping_type\":\"express\",\"sid\":\"224473807924134741\",\"snapshot_url\":\"n:224473807924134741_1\",\"title\":\"小文思勇零食大礼包\",\"total_fee\":\"29.80\",\"trade_from\":\"WAP,WAP\",\"you_xiang\":false}}}");
		jdpTbTrade.setJdpCreated(DateUtil.parseString("2018-11-11 04:59:09"));
		jdpTbTrade.setJdpModified(DateUtil.parseString("2018-11-11 04:59:09"));

		//ADD COLUMN es_time tinyint DEFAULT null comment '相对到达时间,单位为天',
		//ADD COLUMN collect_time timestamp NULL DEFAULT NULL comment '最晚揽收时间',
		//ADD COLUMN delivery_time timestamp NULL DEFAULT NULL comment '最晚发货时间',
		//ADD COLUMN dispatch_time timestamp NULL DEFAULT NULL comment '最晚派送时间',
		//ADD COLUMN sign_time timestamp NULL DEFAULT NULL comment '最晚签收时间',
		//ADD COLUMN cutoff_minutes int(11) DEFAULT NULL comment '截单时间，表示该订单的截单时间（从商家维护截单时间引用），分钟级别，如660代表11:00',
		//ADD COLUMN es_date varchar(40) DEFAULT null comment '预计送达时间，到天，格式2019-04-12',
		//ADD COLUMN es_range varchar(40) DEFAULT null comment '预计送达达时间段，格式，09:00-21:00',
		//ADD COLUMN os_date varchar(40) DEFAULT null comment '预约配送时间，到天，格式2019-04-12 承诺预约配送的时间',
		//ADD COLUMN os_range varchar(40) DEFAULT null comment '预约配送时间段，格式，09:00-21:00 承诺预约配送的时间'
		return jdpTbTrade;
	}

	private Trade generateTradeMockData(JdpTbTrade tbTrade) throws Exception{
		return TaobaoUtils.parseResponse(tbTrade.getJdpResponse(), TradeFullinfoGetResponse.class).getTrade();
	}

	private Trade generateTimingOrderTradeMockData(JdpTbTrade tbTrade) throws Exception{
		Trade trade = TaobaoUtils.parseResponse(tbTrade.getJdpResponse(), TradeFullinfoGetResponse.class).getTrade();
		trade.setCutoffMinutes("960");
		trade.setCollectTime("2019-07-03 23:59:59");
		trade.setDeliveryTime("2019-07-02 23:59:59");
		trade.setDispatchTime("2019-07-04 23:59:59");
		trade.setSignTime("2019-07-04 23:59:00");
		trade.setEsTime("1");
		trade.setEsDate("2019-04-12");
		trade.setEsRange("09:00-21:00");
		trade.setOsDate("2019-04-13");
		trade.setOsRange("09:00-22:00");
		return trade;
	}

}
