package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.service.UniqueIdService;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.code.ErrorCode.BaseCode;
import cn.loveapp.orders.common.code.ErrorCode.SubCode;
import cn.loveapp.orders.common.code.ErrorResponse;
import cn.loveapp.orders.common.config.OrderCommonConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQPrepareConsignOrderChangeConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoBatchOrdersAppConfig;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.dao.redis.OrderSaveLockRedisDao;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearch;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.exception.LockMergeFailedException;
import cn.loveapp.orders.common.exception.ResendMessageException;
import cn.loveapp.orders.common.platform.api.SecurityApiPlatformHandleService;
import cn.loveapp.orders.common.platform.biz.EncryptionPlatformHandleService;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.service.TaobaoAuthService;
import cn.loveapp.orders.common.service.TradeMergeOrderService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.entity.AyTradeSubOrder;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.TradeHandleService;
import cn.loveapp.orders.consumer.common.service.TradeSearchESHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeReceiverDistrictListMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeSubMongoHandleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.api.domain.Order;
import com.taobao.api.response.TradeFullinfoGetResponse;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.BDDMockito.*;
import static org.mockito.Mockito.verify;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 01:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, MigrateTradeHandleServiceImpl.class, RocketMQTaobaoBatchOrdersAppConfig.class, RocketMQPrepareConsignOrderChangeConfig.class},
	properties = {"orders.taobao.ons.batch.orders.topic=topic", "orders.taobao.ons.batch.orders.tag=tag"})
public class MigrateTradeHandleServiceImplTest {

	@MockBean
	private OrderCommonConfig orderCommonConfig;
	@MockBean
	private TradeHandleService tradeHandleService;
	@MockBean
	private TradeSearchESHandleService tradeSearchESHandleService;

	@MockBean
	private TradeMergeOrderService tradeMergeHandleService;

	@MockBean
	private UniqueIdService uniqueIdService;

	@MockBean
	private TaobaoAuthService taobaoAuthService;

	@MockBean
	private UserProductionInfoExtService userProductionInfoExtService;

	@MockBean
	private OrderSaveLockRedisDao tradeHandleBatchOrderRedisDao;

	@MockBean
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private RocketMQTaobaoBatchOrdersAppConfig rocketMQTaobaoBatchOrdersAppConfig;

	@MockBean
	private DefaultMQProducer ordersBatchDataOnsProducer;

	@SpyBean
	private MigrateTradeHandleServiceImpl migrateTradeHandleService;

	@MockBean
	private TradeMongoHandleService tradeMongoHandleService;

	@MockBean
	private TradeSubMongoHandleService tradeSubMongoHandleService;

	@MockBean
	private TradeReceiverDistrictListMongoHandleService tradeReceiverDistrictListMongoHandleService;

	@MockBean
	private OrderRepository orderRepository;
	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;
	@MockBean
	private EncryptionPlatformHandleService encryptionDynamicHandle;
	@MockBean
	private SubOrderRepository subOrderRepository;

	@MockBean
	private SecurityApiPlatformHandleService securityApiPlatformHandleService;

	@Before
	public void setUp() throws Exception {
		when(tradeHandleBatchOrderRedisDao.lockOrder(anyString(), anyString(), any(), anyInt())).thenReturn("lock");
		when(tradeHandleBatchOrderRedisDao.lockOrder(anyString(), anyString(), any())).thenReturn("lock");
		when(uniqueIdService.generateId(anyString())).thenReturn("TDE111");

		TradeMergeOrderService.MergeFullResult result = new TradeMergeOrderService.MergeFullResult();
		when(tradeMergeHandleService.mergeAndUnlock(any(), any(), anyBoolean())).thenReturn(result);
		when(orderCommonConfig.getPushLockRetryCount()).thenReturn(-1);

		when(orderSavingPlatformHandleService.generateMergeMd5(any(TradeBo.class), any(), eq(CommonPlatformConstants.PLATFORM_TAO),
			eq(CommonAppConstants.APP_TRADE))).thenReturn("MD5");

//		platformMigrateTradeFactory = new PlatformMigrateTradeFactory(Lists.newArrayList(platformMigrateTradeService));

	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}

	private List<AyTradeSubOrder> generateSubOrder() {
		List<AyTradeSubOrder> tradeSubOrders = new ArrayList<>();
		AyTradeSubOrder tradeSubOrder = new AyTradeSubOrder();
		tradeSubOrder.setOid("11111111111");
		tradeSubOrder.setAyOid("11111111111");
		tradeSubOrders.add(tradeSubOrder);
		return tradeSubOrders;
	}

	private ErrorResponse generateErrCode() {
		ErrorResponse errorResponse = new ErrorResponse();
		errorResponse.setCode(BaseCode.SUCCESS.getCode());
		errorResponse.setSubCode(SubCode.RECORD_NOEXIST.getCode());
		return errorResponse;
	}

	private ErrorResponse generateSuccErrCode() {
		ErrorResponse errorResponse = new ErrorResponse();
		errorResponse.setCode(BaseCode.SUCCESS.getCode());
		errorResponse.setSubCode(SubCode.SUCCESS.getCode());
		return errorResponse;
	}

	private ErrorResponse generateErrErrCode() {
		ErrorResponse errorResponse = new ErrorResponse();
		errorResponse.setCode(BaseCode.SUCCESS.getCode());
		errorResponse.setSubCode(SubCode.TRADE_ERR.getCode());
		return errorResponse;
	}

	private ErrorResponse generateFullinfoErrCode() {
		ErrorResponse errorResponse = new ErrorResponse();
		errorResponse.setCode(BaseCode.SUCCESS.getCode());
		errorResponse.setSubCode(SubCode.FULLINFO_ERR.getCode());
		return errorResponse;
	}

	@Test
	public void batchOrdersInfo() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		when(userProductionInfoExtService.getDbIdBySellerNick(anyString(), anyString(), anyString())).thenReturn(null);
		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);
		verify(migrateTradeHandleService, never()).saveOrdersInfo(any(), anyBoolean(), any(), any(), anyBoolean());
	}

	/**
	 * 新订单
	 */
	@Test
	public void batchOrdersInfo1() throws Exception {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");
		tradeBo.setAppName("appName");
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());

		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), eq("appName"))).thenReturn(null);

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService)
			.handle(eq(tradeHandleBo), any(TradeBo.class), eq("TDE111"));
		verify(migrateTradeHandleService)
			.saveOrdersInfo(any(TradeBo.class), eq(true), eq(OrderBatchType.RDS), any(), anyBoolean());

		verify(uniqueIdService).generateId(eq("TDE"));
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", "appName", orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), eq("appName"), eq("lock"));
	}

	/**
	 * modified更新的订单
	 */
	@Test
	public void batchOrdersInfo2() throws Exception {
		AyTrade trade = generateTrade();
		trade.setModified(new Date());
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setAppName("appName");
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");

		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());

		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), eq("appName"))).thenReturn(generateTradeMain());

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService)
			.handle(eq(tradeHandleBo), any(TradeBo.class), eq("TDE111"));
		verify(migrateTradeHandleService)
			.saveOrdersInfo(any(TradeBo.class), eq(false), eq(OrderBatchType.RDS), any(), anyBoolean());

		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", "appName", orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), eq("appName"), eq("lock"));
	}

	/**
	 * modified老的订单
	 */
	@Test
	public void batchOrdersInfo3() throws Exception {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(DateUtil.parseString("1999-01-01 00:00:00")));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setStoreId("TAO");
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());

		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(generateTradeMain());

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService, never()).saveOrdersInfo(any(), anyBoolean(), any(), any(), anyBoolean());
		verify(migrateTradeHandleService, never())
			.handle(any(), any(), any());
		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	/**
	 * modified一样的订单, 正常入库
	 */
	@Test
	public void batchOrdersInfo3_2() throws Exception {
		AyTrade trade = generateTrade();
		trade.setModified(new Date());
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());

		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);

		AyTradeMain ayTradeMain = generateTradeMain();
		ayTradeMain.setModified(trade.getModified());
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(ayTradeMain);

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS,false);

		verify(migrateTradeHandleService)
			.handle(eq(tradeHandleBo), any(TradeBo.class), eq("TDE111"));
		verify(migrateTradeHandleService)
			.saveOrdersInfo(any(TradeBo.class), eq(false), eq(OrderBatchType.RDS), any(), anyBoolean());

		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	/**
	 * modified一样的代付款订单, 但当前库内不是代付款的订单. 不入库
	 */
	@Test
	public void batchOrdersInfo3_3() throws Exception {
		AyTrade trade = generateTrade();
		trade.setModified(new Date());
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");
		trade.setStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());
		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);

		AyTradeMain ayTradeMain = generateTradeMain();
		ayTradeMain.setModified(trade.getModified());
		ayTradeMain.setTaoStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(ayTradeMain);

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService, never()).saveOrdersInfo(any(), anyBoolean(), any(), any(), anyBoolean());
		verify(migrateTradeHandleService, never())
				.handle(any(), any(), any());
		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	/**
	 * modified一样的代付款订单, 而且当前库内是代付款的订单. 入库
	 */
	@Test
	public void batchOrdersInfo3_4() throws Exception {
		AyTrade trade = generateTrade();
		trade.setModified(new Date());
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");
		trade.setStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());
		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);

		AyTradeMain ayTradeMain = generateTradeMain();
		ayTradeMain.setModified(trade.getModified());
		ayTradeMain.setTaoStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(ayTradeMain);

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService)
			.handle(eq(tradeHandleBo), any(TradeBo.class), eq("TDE111"));
		verify(migrateTradeHandleService)
			.saveOrdersInfo(any(TradeBo.class), eq(false), eq(OrderBatchType.RDS), any(), anyBoolean());

		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	/**
	 * modified更新的代付款订单, 无论库内是什么状态的订单. 入库
	 */
	@Test
	public void batchOrdersInfo3_5() throws Exception {
		AyTrade trade = generateTrade();
		trade.setModified(new Date());
		trade.setSellerNick("中华人民共和国");
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		tradeBo.setStoreId("TAO");
		trade.setStatus(TaobaoStatusConstant.WAIT_BUYER_PAY);
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());
		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);

		AyTradeMain ayTradeMain = generateTradeMain();
		ayTradeMain.setTaoStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(ayTradeMain);

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS,false);

		verify(migrateTradeHandleService)
			.handle(eq(tradeHandleBo), any(TradeBo.class), eq("TDE111"));
		verify(migrateTradeHandleService)
			.saveOrdersInfo(any(TradeBo.class), eq(false), eq(OrderBatchType.RDS), any(), anyBoolean());

		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	/**
	 * 测试corpId为null
	 */
	@Test
	public void batchOrdersInfo4() throws Exception {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(DateUtil.parseString("1999-01-01 00:00:00")));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setStoreId("TAO");
		tradeBo.setTid("11111111111");
		tradeBo.setAyTradeMain(generateTradeMain());
		UserDbId userDbId = new UserDbId("中华人民共和国", 1, 1, "23122312", "23122312", "TAO", null);
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(0);
		tradeHandleBo.setCorpId(userDbId.getCorpId());
		tradeHandleBo.setSellerId(userDbId.getSellerId());
		tradeHandleBo.setListId(312);
		tradeHandleBo.setTrade(trade);
		tradeHandleBo.setSellerNick(userDbId.getSellerNick());
		tradeHandleBo.setTid(tradeBo.getTid());
		tradeHandleBo.setCreated(trade.getCreated());
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setAppName(tradeBo.getAppName());
		tradeBo.setSellerId(userDbId.getSellerId());
		when(userProductionInfoExtService.getDbIdBySellerNick(userDbId.getSellerNick(), userDbId.getStoreId(), tradeBo.getAppName())).thenReturn(userDbId);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), any(), eq("23122312"), isNull())).thenReturn(generateTradeMain());

		migrateTradeHandleService.batchOrdersInfo(tradeBo, "中华人民共和国", LocalDateTime.now(), OrderBatchType.RDS, false);

		verify(migrateTradeHandleService, never()).saveOrdersInfo(any(), anyBoolean(), any(), any(), anyBoolean());
		verify(migrateTradeHandleService, never())
			.handle(any(), any(), any());
		verify(uniqueIdService, never()).generateId(anyString());
		verify(tradeHandleBatchOrderRedisDao)
			.lockOrder("中华人民共和国", "11111111111", null, orderCommonConfig.getPushLockRetryCount());
		verify(tradeHandleBatchOrderRedisDao).unLockOrder(eq("中华人民共和国"), eq("11111111111"), isNull(), eq("lock"));
	}

	private AyTradeMain generateTradeMain() {
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setModified(DateUtil.parseDateString("2019-01-26 10:54:38"));
		tradeMain.setSellerNick("中华人民共和国");
		tradeMain.setAyTid("TDE111");
		return tradeMain;
	}

	@Test
	public void executeBatchOrders() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		migrateTradeHandleService.executeBatchOrders(trade, null, "1111", "中华人民共和国", "1111", LocalDateTime.now(),
			CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE,  false);
		verify(migrateTradeHandleService)
			.batchOrdersInfo(any(TradeBo.class), anyString(), any(LocalDateTime.class), any(), anyBoolean());
	}

	@Test
	public void executeBatchOrders1() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		migrateTradeHandleService.executeBatchOrders(trade, "中华人民共和国", "1111", "中华人民共和国", "1111", LocalDateTime.now(),
			CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE,  false);
		verify(migrateTradeHandleService)
			.batchOrdersInfo(any(TradeBo.class), anyString(), any(LocalDateTime.class), any(), anyBoolean());
	}

	@Test
	public void batchTaoBaoFullinfoData() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");

		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);
		verify(tradeSearchESHandleService).putTradeData(any(), eq(true), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData2() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		migrateTradeHandleService.saveOrdersInfo(tradeBo, false, OrderBatchType.RDS, Maps.newHashMap(), false);
		verify(tradeSearchESHandleService).putTradeData(any(), eq(false), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData3() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);

		verify(tradeSubMongoHandleService)
			.putTradeData(any(), anyString(), anyString(), anyString(), eq(true), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService).putTradeData(any(), eq(true), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData4() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		tradeMain.setModified(new Date());

		migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);

		verify(tradeSubMongoHandleService)
			.putTradeData(any(), anyString(), anyString(), anyString(), eq(true), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService).putTradeData(any(), eq(true), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData5() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		tradeMain.setModified(new Date());
		migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);

		verify(tradeSubMongoHandleService)
			.putTradeData(any(), anyString(), anyString(), anyString(), anyBoolean(), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService).putTradeData(any(), anyBoolean(), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData6() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(new Date());
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		tradeMain.setModified(new Date());
		when(tradeMongoHandleService.putTradeData(any(), anyString(), anyString(), any(), anyBoolean(), any()))
			.thenThrow(new UncategorizedSQLException("", "", new SQLException("")));

		try {
			migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);
			fail();
		} catch (Exception e) {
		}

		verify(tradeMongoHandleService)
			.putTradeData(any(), eq("11111111111"), eq("TAO"), eq("123123"), eq(true), eq(OrderBatchType.RDS));
		verify(tradeSubMongoHandleService, never())
			.putTradeData(any(), anyString(), anyString(), anyString(), anyBoolean(), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService, never()).putTradeData(any(), anyBoolean(), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData7() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		tradeMain.setModified(new Date());
		when(tradeMongoHandleService.putTradeData(any(), anyString(), anyString(), any(), anyBoolean(), any()))
			.thenThrow(new UncategorizedSQLException("", "", new SQLException("")));

		try {
			migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);
			fail();
		} catch (Exception e) {
		}

		verify(tradeMongoHandleService)
			.putTradeData(any(), eq("11111111111"), eq("TAO"), eq("123123"), eq(true), eq(OrderBatchType.RDS));
		verify(tradeSubMongoHandleService, never())
			.putTradeData(any(), anyString(), anyString(), anyString(), anyBoolean(), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService, never()).putTradeData(any(), anyBoolean(), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTaoBaoFullinfoData8() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		trade.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		TradeBo tradeBo = new TradeBo();
		tradeBo.setTrade(trade);
		tradeBo.setTid("11111111111");
		tradeBo.setStoreId("TAO");
		tradeBo.setSellerId("123123");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setTid("11111111111");
		tradeMain.setSellerNick("nick");
		AyTradeSearch tradeSearch = new AyTradeSearch();
		tradeSearch.setTid("11111111111");
		tradeBo.setAyTradeMain(tradeMain);
		tradeBo.setAyTradeSearch(tradeSearch);

		tradeMain.setModified(new Date());

		try {
			when(tradeMergeHandleService.tryLockIfNeedMerge(any(), any(), anyBoolean())).thenThrow(new LockMergeFailedException(""));
		} catch (LockMergeFailedException e) {
		}

		try {
			migrateTradeHandleService.saveOrdersInfo(tradeBo, true, OrderBatchType.RDS, Maps.newHashMap(), false);
			fail();
		} catch (ResendMessageException e) {
		}
		verify(tradeMongoHandleService, never()).putTradeData(any(), any(), any(), any(), anyBoolean(), any());
		verify(tradeSubMongoHandleService, never())
			.putTradeData(any(), anyString(), anyString(), anyString(), anyBoolean(), eq(OrderBatchType.RDS));
		verify(tradeSearchESHandleService, never()).putTradeData(any(), anyBoolean(), eq(OrderBatchType.RDS));
	}

	@Test
	public void batchTmcOrdersChangeInfo() {
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, null, null);
        migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo2() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), tradeMemoModified, null, null);
		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo21() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		tradeMemoModified.setSellerMemo("sellerMemo");
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), tradeMemoModified, null, null);
		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo3() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		tradeMemoModified.setSellerMemo("sellerMemo");
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), tradeMemoModified, null, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		when(tradeMongoHandleService.update(any(), any())).thenThrow(new DuplicateKeyException(""));

		try {
			migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);
			fail();
		} catch (Exception e) {
		}

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setSellerId(sellerId);
		ayTradeMain.setTid("11111111111");
		ayTradeMain.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeMain.setSellerMemo("sellerMemo");
		verify(tradeMongoHandleService).update(eq(ayTradeMain), eq(OrderBatchType.API));
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo31() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		tradeMemoModified.setSellerMemo("sellerMemo");
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), tradeMemoModified, null, null);

		AyTradeMain last = new AyTradeMain();
		last.setSellerMemo("sellerMemo");
		last.setModified(new Date());
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo4() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		tradeMemoModified.setSellerMemo("sellerMemo");
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), tradeMemoModified, null, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setSellerId(sellerId);
		ayTradeMain.setTid("11111111111");
		ayTradeMain.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeMain.setSellerMemo("sellerMemo");

		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setId(CommonPlatformConstants.PLATFORM_TAO + "11111111111");
		ayTradeSearchES.setSellerId(sellerId);
		ayTradeSearchES.setTid("11111111111");
		ayTradeSearchES.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeSearchES.setSellerMemo(Lists.newArrayList("sellerMemo"));
		verify(tradeSearchESHandleService).update(eq(ayTradeSearchES), eq(OrderBatchType.API));
	}

	@Test
	public void batchTmcOrdersChangeInfo5() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);
		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo51() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setBuyeRate(true);
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);
		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo6() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setBuyeRate(true);
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);
		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		when(tradeMongoHandleService.update(any(), any())).thenThrow(new DuplicateKeyException(""));

		try {
			migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);
			fail();
		} catch (Exception e) {
		}

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setSellerId(sellerId);
		ayTradeMain.setTid("11111111111");
		ayTradeMain.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeMain.setSellerRate(true);
		ayTradeMain.setBuyerRate(true);
		verify(tradeMongoHandleService).update(eq(ayTradeMain), eq(OrderBatchType.API));
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo61() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setBuyeRate(true);
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(new Date());
		last.setBuyerRate(true);
		last.setSellerRate(true);
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}

	@Test
	public void batchTmcOrdersChangeInfo7() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setBuyeRate(true);
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setSellerId(sellerId);
		ayTradeMain.setTid("11111111111");
		ayTradeMain.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeMain.setBuyerRate(true);
		ayTradeMain.setSellerRate(true);
		verify(tradeMongoHandleService).update(eq(ayTradeMain), eq(OrderBatchType.API));

		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setSellerId(sellerId);
		ayTradeSearchES.setTid("11111111111");
		ayTradeSearchES.setId(CommonPlatformConstants.PLATFORM_TAO + "11111111111");
		ayTradeSearchES.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeSearchES.setBuyerRate(true);
		ayTradeSearchES.setSellerRate(true);
		verify(tradeSearchESHandleService).update(eq(ayTradeSearchES), eq(OrderBatchType.API));
	}

	@Test
	public void batchTmcOrdersChangeInfo8() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setSellerId(sellerId);
		ayTradeMain.setTid("11111111111");
		ayTradeMain.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeMain.setSellerRate(true);
		verify(tradeMongoHandleService).update(eq(ayTradeMain), eq(OrderBatchType.API));

		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setId(CommonPlatformConstants.PLATFORM_TAO + "11111111111");
		ayTradeSearchES.setSellerId(sellerId);
		ayTradeSearchES.setTid("11111111111");
		ayTradeSearchES.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
		ayTradeSearchES.setSellerRate(true);
		verify(tradeSearchESHandleService).update(eq(ayTradeSearchES), eq(OrderBatchType.API));
	}

	/**
	 * 库中的modified比消息中的新, 不入库
	 */
	@Test
	public void batchTmcOrdersChangeInfo9() {
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		tradeRated.setSellerRate(true);
		String sellerId = "111111";
		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest("中华人民共和国", "11111111111", "oid", LocalDateTime.now(), null, tradeRated, null);

		AyTradeMain last = new AyTradeMain();
		last.setModified(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().plusMinutes(1)));
		when(orderRepository.queryByTidGetAyTradeMain(eq("11111111111"), eq(CommonPlatformConstants.PLATFORM_TAO), eq(sellerId), isNull()))
			.thenReturn(last);

		migrateTradeHandleService.batchTmcOrdersChangeInfo(tmcOrdersRequest, sellerId, false, CommonPlatformConstants.PLATFORM_TAO, null);

		verify(tradeMongoHandleService, never()).update(any(), any());
		verify(tradeSearchESHandleService, never()).update(any(), any());
	}
}
