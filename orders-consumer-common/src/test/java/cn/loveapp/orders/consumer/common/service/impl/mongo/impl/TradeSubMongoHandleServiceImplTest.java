package cn.loveapp.orders.consumer.common.service.impl.mongo.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/5/18
 * @time: 3:57 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = { MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, TradeSubMongoHandleServiceImpl.class,
		SubOrderRepository.class
	})
public class TradeSubMongoHandleServiceImplTest {

	@SpyBean
	private TradeSubMongoHandleServiceImpl tradeSubMongoHandleService;

	@MockBean
	private SubOrderRepository subOrderRepository;

	@MockBean
	private AyStatusCodeConfigService ayStatusCodeConfigService;

	@Test
	public void putTradeData() {
		List<TcSubOrder> tradeConsignLogisticList = generateConsignLogistics();
		tradeSubMongoHandleService.putTradeData(tradeConsignLogisticList, "11111111111", "TAO", "11111111111", true, OrderBatchType.API);

		verify(subOrderRepository).insertAll(anyList());
	}

	@Test
	public void putTradeData2() {
		List<TcSubOrder> tradeConsignLogisticList = generateConsignLogistics();
		doThrow(DuplicateKeyException.class).when(subOrderRepository).insertAll(any());
		when(subOrderRepository.bulkWrite(anyList())).thenReturn(1);
		tradeSubMongoHandleService.putTradeData(tradeConsignLogisticList, "11111111111", "TAO", "11111111111", true, OrderBatchType.API);

		verify(subOrderRepository).insertAll(anyList());
		verify(subOrderRepository).bulkWrite(anyList());
	}

	@Test
	public void putTradeData3() {
		when(subOrderRepository.bulkWrite(anyList())).thenReturn(1);
		List<TcSubOrder> tradeConsignLogisticList = generateConsignLogistics();
		tradeSubMongoHandleService.putTradeData(tradeConsignLogisticList, "11111111111", "TAO", "11111111111", false, OrderBatchType.RDS);
		verify(subOrderRepository).bulkWrite(anyList());
		verify(subOrderRepository, never()).insertAll(anyList());
	}

	@Test
	public void putTradeData4() {
		List<TcSubOrder> tradeConsignLogisticList = generateConsignLogistics();
		when(subOrderRepository.bulkWrite(any())).thenReturn(0);
		tradeSubMongoHandleService.putTradeData(tradeConsignLogisticList, "11111111111", "TAO", "11111111111", false, OrderBatchType.RDS);

		verify(subOrderRepository).bulkWrite(anyList());
		verify(subOrderRepository).insertAll(anyList());
	}

	@Test
	public void update() {
		TcSubOrder ayTradeSubOrder = new TcSubOrder();
		ayTradeSubOrder.setBuyerRate(true);
		ayTradeSubOrder.setSellerRate(true);

		tradeSubMongoHandleService.update(Collections.singletonList(ayTradeSubOrder));

		assertTrue(ayTradeSubOrder.getBuyerRate());
		assertTrue(ayTradeSubOrder.getSellerRate());
	}

	@Test
	public void update2() {
		TcSubOrder ayTradeSubOrder = new TcSubOrder();
		ayTradeSubOrder.setBuyerRate(false);
		ayTradeSubOrder.setSellerRate(false);
		tradeSubMongoHandleService.update(Collections.singletonList(ayTradeSubOrder));

		assertFalse(ayTradeSubOrder.getBuyerRate());
		assertFalse(ayTradeSubOrder.getSellerRate());
	}

	@Test
	public void update3() {
		TcSubOrder ayTradeSubOrder = new TcSubOrder();
		ayTradeSubOrder.setBuyerRate(false);
		ayTradeSubOrder.setSellerRate(false);

		tradeSubMongoHandleService.update(Collections.singletonList(ayTradeSubOrder));

		assertFalse(ayTradeSubOrder.getBuyerRate());
		assertFalse(ayTradeSubOrder.getSellerRate());
	}

	@Test
	public void update4() {
		TcSubOrder ayTradeSubOrder = new TcSubOrder();
		ayTradeSubOrder.setBuyerRate(true);
		ayTradeSubOrder.setSellerRate(true);

		tradeSubMongoHandleService.update(Collections.singletonList(ayTradeSubOrder));

		assertTrue(ayTradeSubOrder.getBuyerRate());
		assertTrue(ayTradeSubOrder.getSellerRate());
	}

	private List<TcSubOrder> generateConsignLogistics() {
		List<TcSubOrder> tradeConsignLogisticList = new ArrayList<>();
		TcSubOrder tradeConsignLogistics = new TcSubOrder();
		tradeConsignLogistics.setTid("11111111111");
		tradeConsignLogistics.setAyTid("TDE11111111111");
		tradeConsignLogistics.setSellerRate(true);
		tradeConsignLogistics.setBuyerRate(true);
		tradeConsignLogisticList.add(tradeConsignLogistics);
		return tradeConsignLogisticList;
	}
}
