package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.dao.trade.AyStatusCodeConfigDao;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.platform.biz.OrderMergePlatformHandleService;
import cn.loveapp.orders.common.service.impl.AyStatusCodeConfigServiceImpl;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = { MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, AyStatusCodeConfigServiceImpl.class,
		PddOrderSavingPlatformHandleServiceImpl.class
	})
public class PddOrderSavingPlatformHandleServiceImplTest {
	@Autowired
	private PddOrderSavingPlatformHandleServiceImpl savingPlatformHandleService;

	@MockBean
	protected OrderMergePlatformHandleService orderMergePlatformHandleService;

	@MockBean
	protected AyStatusCodeConfigDao ayStatusCodeConfigDao;

	/**
	 * PDD没有buyerNick时, 获取上次存库的buyerNick
	 */
	@Test
	public void appendToTradeMain() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		TradeBo tradeBo = new TradeBo();

		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setBuyerNick("buyerNick");
		tradeBo.setLastAyTradeMain(lastAyTradeMain);
		savingPlatformHandleService.appendToTradeMain(ayTradeMain, null ,tradeBo, null ,null ,null, null);
		assertEquals(lastAyTradeMain.getBuyerNick(), ayTradeMain.getBuyerNick());
	}

	/**
	 * PDD有buyerNick时, 不使用获取上次存库的buyerNick
	 */
	@Test
	public void appendToTradeMain2() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setBuyerNick("buyerNick1");
		TradeBo tradeBo = new TradeBo();

		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setBuyerNick("buyerNick2");
		tradeBo.setLastAyTradeMain(lastAyTradeMain);
		savingPlatformHandleService.appendToTradeMain(ayTradeMain, null ,tradeBo, null ,null ,null, null);
		assertEquals("buyerNick1", ayTradeMain.getBuyerNick());
	}

	/**
	 * 没有buyerNick 使用 lastAyTradeMain 的 buyerNick
	 */
	@Test
	public void generateMergeMd52() {
		TradeBo tradeBo = new TradeBo();
		AyTrade trade = new AyTrade();
		trade.setReceiverCountry("ReceiverCountry");
		trade.setReceiverState("ReceiverState");
		trade.setReceiverCity("ReceiverCity");
		trade.setReceiverDistrict("ReceiverDistrict");
		trade.setReceiverZip("ReceiverZip");


		OriginDecryptData originDecryptData = new OriginDecryptData();
		originDecryptData.setBuyerNick(null);
		originDecryptData.setReceiverAddress("ReceiverAddress");
		originDecryptData.setReceiverName("ReceiverName");
		originDecryptData.setReceiverMobile("ReceiverMobile");
		originDecryptData.setReceiverPhone("ReceiverPhone");

		tradeBo.setTrade(trade);
		tradeBo.setOriginDecryptData(originDecryptData);

		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setBuyerNick("~AgAAAABR+acF0QAnawAlanUYApZMyd3Q5WmQ1TBAkKQ=~lClK~4~~");

		Mockito.when(orderMergePlatformHandleService.generateMergeMd5(
			"lClK",
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			originDecryptData.getReceiverAddress(),
			trade.getReceiverZip(),
			originDecryptData.getReceiverName(),
			originDecryptData.getReceiverMobile(),
			originDecryptData.getReceiverPhone(),
			CommonPlatformConstants.PLATFORM_PDD, CommonAppConstants.APP_TRADE)).thenReturn("md5");

		String md5 = savingPlatformHandleService.generateMergeMd5(tradeBo, lastAyTradeMain, CommonPlatformConstants.PLATFORM_PDD,
			CommonAppConstants.APP_TRADE);

		Assert.assertEquals("md5", md5);
	}
}
