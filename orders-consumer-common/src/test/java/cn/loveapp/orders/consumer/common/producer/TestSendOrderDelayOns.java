package cn.loveapp.orders.consumer.common.producer;

import cn.loveapp.orders.common.config.rocketmq.aliyun.TaobaoAliyunOnsAppConfig;
import cn.loveapp.orders.common.config.rocketmq.aliyun.TaobaoOnsOrdersDelayAppConfig;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @program: orders-services-group
 * @description: TestSendOrderDelayOns
 * @author: Jason
 * @create: 2019-01-03 16:55
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = {RocketMqQueueHelper.class,
		TaobaoAliyunOnsAppConfig.class, TaobaoOnsOrdersDelayAppConfig.class, ConfigurationPropertiesAutoConfiguration.class})
@ActiveProfiles("test")
public class TestSendOrderDelayOns {

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private TaobaoOnsOrdersDelayAppConfig taobaoOnsOrdersDelayAppConfig;

	@Ignore
	@Test
	public void sendFullInfoOnsMsg() {
//		LocalDateTime l = DateUtil.parseString("2019-01-03 17:03:32");
//		OrderDelayRequestProto orderDelayRequestProto = new OrderDelayRequestProto();
//		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
//		comLoveRpcInnerprocessRequestHead.setSellerNick("四季女装秀");
//		comLoveRpcInnerprocessRequestHead.setTid("315765443734865762");
//		OrderDelayRequest orderDelayRequest = new OrderDelayRequest();
//		orderDelayRequest.setModified(l);
//		orderDelayRequestProto.setOrderDelayRequest(orderDelayRequest);
//		onsQueueHelper.setDelayTime(taobaoOnsOrdersDelayAppConfig.getDelayTime());
//		onsQueueHelper.push(taobaoOnsOrdersDelayAppConfig.getTopic(), taobaoOnsOrdersDelayAppConfig.getTag(),
//			orderDelayRequestProto, ordersDelayDataOnsProducer);
	}
}
