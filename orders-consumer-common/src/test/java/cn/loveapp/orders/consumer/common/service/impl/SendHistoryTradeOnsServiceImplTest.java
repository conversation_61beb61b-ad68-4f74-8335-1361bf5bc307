package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.api.request.SoldGetRequest;
import cn.loveapp.orders.common.api.response.SoldGetResponse;
import cn.loveapp.orders.common.bo.UserInfoBo;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoHistoryAppConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoSoldgetAppConfig;
import cn.loveapp.orders.common.constant.EncryptionTypeConstant;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.platform.biz.EncryptionPlatformHandleService;
import cn.loveapp.orders.common.proto.*;
import cn.loveapp.orders.common.proto.head.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.orders.common.service.TaobaoAuthService;
import cn.loveapp.orders.common.service.UserCenterService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.service.impl.TaobaoAuthServiceImpl;
import cn.loveapp.orders.common.service.impl.UserProductionInfoExtServiceImpl;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.MigrateTaoApiOrderService;
import cn.loveapp.orders.common.service.TradeSendOrderHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeMongoHandleService;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Producer;
import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Trade;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.*;
import static org.mockito.Mockito.verify;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 15:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {PrintServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, RocketMQTaobaoHistoryAppConfig.class,
		RocketMqQueueHelper.class, TaobaoAuthServiceImpl.class,
		TaobaoSoldgetAppConfig.class, SendHistoryTradeOnsServiceImpl.class,
		TradeSendOrderHandleService.class},
	properties = {"orders.taobao.ons.history.topic=topic", "orders.taobao.ons.history.tag=tag"})
public class SendHistoryTradeOnsServiceImplTest {

	protected static final String INVALID_SESSIONKEY_CODE = "27";
	protected static final String INVALID_SESSIONKEY_SUB_CODE = "invalid-sessionkey";

	@MockBean
	private DefaultMQProducer ordersPullApiHistoryOnsProducer;

	@Autowired
	private RocketMQTaobaoHistoryAppConfig rocketMQTaobaoHistoryAppConfig;

	@MockBean
	private RocketMqQueueHelper rocketMqQueueHelper;

	@MockBean
	private MigrateTaoApiOrderService migrateTaoApiOrderService;

	@MockBean
	private TradeSendOrderHandleService tradeSendOrderHandleService;
	@MockBean
	private OrderPullingPlatformHandleService sendHistoryTradeOnsDynamicService;
	@MockBean
	private EncryptionPlatformHandleService encryptionPlatformHandleService;

	@MockBean
	private UserService userService;

	@MockBean
	private OrderRepository orderRepository;

	@MockBean
	private TaobaoAuthService taobaoAuthService;

	@MockBean
	private TaobaoSoldgetAppConfig taobaoSoldgetAppConfig;

	@SpyBean
	private SendHistoryTradeOnsServiceImpl sendHistoryTradeOnsService;

	@MockBean
	private TradeMongoHandleService tradeMongoHandleService;

	@MockBean
	private TradeApiPlatformHandleService tradeApiPlatformHandleService;

	@MockBean
	private UserCenterService userCenterService;

	@MockBean
	private UserProductionInfoExtServiceImpl userProductionInfoExtService;

	@Before
	public void setUp(){
		sendHistoryTradeOnsService.stop = false;
	}

	@Test
	public void sendApiOns() {
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		try {
			sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus, null, null, ignoreSameModified, 1, null, true, false);
			fail();
		} catch (Exception ue) {
		}
	}

	@Test
	public void sendApiOns1() throws Exception{
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		try {
			sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e,
				taoStatus, null, null, ignoreSameModified, 1, null, true, false);
			fail();
		} catch (IllegalStateException ue) {
		} catch (UserNeedAuthException e1) {
		}
	}

	@Test
	public void sendApiOns2() throws Exception{
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName(CommonAppConstants.APP_TRADE);
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO",CommonAppConstants.APP_TRADE))
			.thenReturn(generateTradeSoldGet());
		try {
			boolean result = sendHistoryTradeOnsService
				.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus, null, null, ignoreSameModified, 1, null, true, false);
			Assert.assertFalse(result);
			verify(sendHistoryTradeOnsService)
				.loopTradeEach2Fullinfo(eq(sellerNick), eq("123123123"), any(), eq(CommonAppConstants.APP_TRADE), any(), eq(ignoreSameModified), null);
		} catch (UserNeedAuthException e1) {
		}
	}

	/**
	 * 发送失败
	 *
	 * @throws Exception
	 */
	@Test
	public void sendApiOns3() throws Exception{
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";

		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName("trade");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		int maxRetryCount = 1;
		try {
			when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);
			when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(null, null, 3));
			sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, null, null,
				taoStatus, null, null, ignoreSameModified, maxRetryCount, null, true, false);
			fail();
		} catch (IllegalStateException ue) {
		} catch (UserNeedAuthException e1) {
		}
		verify(tradeApiPlatformHandleService, times((maxRetryCount + 1) * 3)).soldGet(
			any(), anyString(), any(),anyString());
		verify(rocketMqQueueHelper, never()).push(any(), any(), any(), any(DefaultMQProducer.class));
        // 阿里云mq 已废弃，不再使用
//		verify(rocketMqQueueHelper, never()).push(any(), any(), any(), any(Producer.class));

		// 验证进度
		List<SyncOrdersProgress> soldGetProgresses = proto.getHistoryProgressList();
		assertEquals(3, soldGetProgresses.size());
		assertTrue(soldGetProgresses.get(0).getStartTime().isBefore(LocalDateTime.now().minusMonths(1)));
		assertTrue(soldGetProgresses.get(0).getStartTime().isAfter(LocalDateTime.now().minusMonths(2)));
		assertTrue(soldGetProgresses.get(0).getEndTime().isAfter(LocalDateTime.now().minusMinutes(5)));
		assertNull(soldGetProgresses.get(0).getError());
		assertNull(soldGetProgresses.get(0).getFirstTrade());

		assertTrue(soldGetProgresses.get(1).getStartTime().isBefore(LocalDateTime.now().minusMonths(2)));
		assertTrue(soldGetProgresses.get(1).getStartTime().isAfter(LocalDateTime.now().minusMonths(3)));
		assertTrue(soldGetProgresses.get(1).getEndTime().isAfter(LocalDateTime.now().minusMonths(1).minusMinutes(5)));
		assertNull(soldGetProgresses.get(1).getError());
		assertNull(soldGetProgresses.get(1).getFirstTrade());

		assertNull(soldGetProgresses.get(2).getStartTime());
		assertNull(soldGetProgresses.get(2).getError());
		assertNull(soldGetProgresses.get(2).getFirstTrade());
		System.out.println(soldGetProgresses);
		System.out.println(soldGetProgresses.get(2).getEndTime());
		System.out.println(LocalDateTime.now().minusMonths(2).minusMinutes(5));
		assertTrue(soldGetProgresses.get(2).getEndTime().isAfter(LocalDateTime.now().minusMonths(2).minusMinutes(5)));

	}

	@Test
	public void sendApiOns4() {
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		int maxRetryCount = 1;
		try {
			when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);
			SoldGetResponse tradesSoldGetResponse = generateTradeSoldGet();
			tradesSoldGetResponse.setErrorCode(INVALID_SESSIONKEY_CODE);
			tradesSoldGetResponse.setSubCode(INVALID_SESSIONKEY_SUB_CODE);

			when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(null, null, 3));
			when(tradeApiPlatformHandleService.soldGet(any(SoldGetRequest.class), eq(sellerNick), any(),any())).thenReturn(tradesSoldGetResponse);
			when(tradeApiPlatformHandleService.isInvalidSessionKey(eq(tradesSoldGetResponse), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(true);
			sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, null,
				null, taoStatus, null, null, ignoreSameModified, maxRetryCount, null, true, false);
			fail();
		} catch (Exception ue) {
		}
		verify(tradeApiPlatformHandleService, times(6)).soldGet(any(SoldGetRequest.class), eq(sellerNick), any(),any());
		verify(userService, times(7)).getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString());
		verify(rocketMqQueueHelper, never()).push(any(), any(), any(), any(DefaultMQProducer.class));
        // 阿里云mq 已废弃，不再使用
//		verify(rocketMqQueueHelper, never()).push(any(), any(), any(), any(Producer.class));
	}

	@Test
	public void sendApiOns5() throws Exception {
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName("trade");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		SyncOrdersProgress soldGetProgress1 = new SyncOrdersProgress(LocalDateTime.now().minusMonths(1), LocalDateTime.now());
		SyncOrdersProgress soldGetProgress2 = new SyncOrdersProgress(LocalDateTime.now().minusMonths(2), LocalDateTime.now().minusMonths(1));
		SyncOrdersProgress soldGetProgress3 = new SyncOrdersProgress(null, LocalDateTime.now().minusMonths(2));
		soldGetProgressList.add(soldGetProgress1);
		soldGetProgressList.add(soldGetProgress2);
		soldGetProgressList.add(soldGetProgress3);
		proto.setHistoryProgressList(soldGetProgressList);

		int maxRetryCount = 1;
		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		SoldGetRequest soldGetRequest2 = generateSoldGet();
		soldGetRequest2.setEndCreated(soldGetProgress2.getEndTime());
		soldGetRequest2.setStartCreated(soldGetProgress2.getStartTime());
		soldGetRequest2.setTopSession(topSession);
		soldGetRequest2.setOrderStatus(taoStatus);
		soldGetRequest2.setRefundStatus(null);
		soldGetRequest2.setUseHasNext(true);

		SoldGetResponse tradesSoldGetResponse2 = generateTradeSoldGet();
		tradesSoldGetResponse2.getTrades().get(0).setTid(21L);
		tradesSoldGetResponse2.getTrades().get(1).setTid(22L);
		tradesSoldGetResponse2.getTrades().get(0).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(7)));
		tradesSoldGetResponse2.getTrades().get(1).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(8)));
		tradesSoldGetResponse2.setHasNext(false);
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest2, sellerNick)).thenReturn(tradesSoldGetResponse2);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest2, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(tradesSoldGetResponse2);

		SoldGetRequest lastSoldGetRequest = generateSoldGet();
		lastSoldGetRequest.setEndCreated(soldGetProgress3.getEndTime());
		lastSoldGetRequest.setStartCreated(soldGetProgress3.getStartTime());
		lastSoldGetRequest.setTopSession(topSession);
		lastSoldGetRequest.setOrderStatus(taoStatus);
		lastSoldGetRequest.setRefundStatus(null);
		lastSoldGetRequest.setUseHasNext(true);

		SoldGetResponse lastTradesSoldGetResponse = generateTradeSoldGet();
		lastTradesSoldGetResponse.getTrades().get(0).setTid(31L);
		lastTradesSoldGetResponse.getTrades().get(1).setTid(32L);
		lastTradesSoldGetResponse.getTrades().get(0).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(9)));
		lastTradesSoldGetResponse.getTrades().get(1).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(10)));
		lastTradesSoldGetResponse.setHasNext(false);
//		when(tradeSoldGetService.spinSoldGet(lastSoldGetRequest, sellerNick)).thenReturn(lastTradesSoldGetResponse);
		when(tradeApiPlatformHandleService.soldGet(lastSoldGetRequest, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(lastTradesSoldGetResponse);

		SoldGetRequest soldGetRequest1 = generateSoldGet();
		soldGetRequest1.setEndCreated(soldGetProgress1.getEndTime());
		soldGetRequest1.setStartCreated(soldGetProgress1.getStartTime());
		soldGetRequest1.setTopSession(topSession);
		soldGetRequest1.setOrderStatus(taoStatus);
		soldGetRequest1.setRefundStatus(null);
		soldGetRequest1.setUseHasNext(true);

		SoldGetResponse tradesSoldGetResponse1 = generateTradeSoldGet();
		AyTrade firstTrade = tradesSoldGetResponse1.getTrades().get(0);
		firstTrade.setTid(11111111L);
		firstTrade.setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		tradesSoldGetResponse1.getTrades().get(1).setTid(22222222L);
		tradesSoldGetResponse1.getTrades().get(1).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(2)));
		tradesSoldGetResponse1.setHasNext(true);
		Date date = tradesSoldGetResponse1.getTrades().get(tradesSoldGetResponse1.getTrades().size() - 1).getCreated();
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest1, sellerNick)).thenReturn(tradesSoldGetResponse1);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest1, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(tradesSoldGetResponse1);

		SoldGetRequest soldGetRequest12 = generateSoldGet();
		soldGetRequest12.setPageNo(2L);
		soldGetRequest12.setEndCreated(soldGetProgress1.getEndTime());
		soldGetRequest12.setStartCreated(soldGetProgress1.getStartTime());
		soldGetRequest12.setTopSession(topSession);
		soldGetRequest12.setOrderStatus(taoStatus);
		soldGetRequest12.setRefundStatus(null);
		soldGetRequest12.setUseHasNext(true);

		SoldGetResponse tradesSoldGetResponse12 = generateTradeSoldGet();
		tradesSoldGetResponse12.setErrorCode(SendHistoryTradeOnsServiceImpl.UNAVAILABLE_CODE);
		tradesSoldGetResponse12.setSubCode(sendHistoryTradeOnsService.unavailableCodes.get(0));
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest12, sellerNick)).thenReturn(tradesSoldGetResponse12);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest12, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(tradesSoldGetResponse12);

		SoldGetRequest soldGetRequest13 = generateSoldGet();
		soldGetRequest13.setPageNo(1L);
		soldGetRequest13.setStartCreated(soldGetProgress1.getStartTime());
		soldGetRequest13.setEndCreated(DateUtil.parseDate(date));
		soldGetRequest13.setTopSession(topSession);
		soldGetRequest13.setOrderStatus(taoStatus);
		soldGetRequest13.setRefundStatus(null);
		soldGetRequest13.setUseHasNext(true);
		SoldGetResponse tradesSoldGetResponse13 = generateTradeSoldGet();
		tradesSoldGetResponse13.getTrades().get(0).setTid(33333333L);
		tradesSoldGetResponse13.getTrades().get(1).setTid(44444444L);
		tradesSoldGetResponse13.getTrades().get(0).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(3)));
		tradesSoldGetResponse13.getTrades().get(1).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(4)));
		tradesSoldGetResponse13.setHasNext(true);
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest13, sellerNick)).thenReturn(tradesSoldGetResponse13);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest13, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(tradesSoldGetResponse13);

		SoldGetRequest soldGetRequest14 = generateSoldGet();
		soldGetRequest14.setPageNo(2L);
		soldGetRequest14.setTopSession(topSession);
		soldGetRequest14.setOrderStatus(taoStatus);
		soldGetRequest14.setRefundStatus(null);
		soldGetRequest14.setStartCreated(soldGetProgress1.getStartTime());
		soldGetRequest14.setEndCreated(DateUtil.parseDate(date));
		soldGetRequest14.setUseHasNext(true);
		SoldGetResponse tradesSoldGetResponse14 = generateTradeSoldGet();
		tradesSoldGetResponse14.getTrades().get(0).setTid(55555555L);
		tradesSoldGetResponse14.getTrades().get(1).setTid(66666666L);
		tradesSoldGetResponse14.getTrades().get(0).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(5)));
		tradesSoldGetResponse14.getTrades().get(1).setCreated(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now().minusMinutes(6)));
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest14, sellerNick)).thenReturn(tradesSoldGetResponse14);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest14, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(tradesSoldGetResponse14);
		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(soldGetProgressList);
		when(userService.getSellerInfoBySellerNick(any(), any(), any())).thenReturn(userInfoBo);

		try {
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, null,
				null, taoStatus, null, null, ignoreSameModified, maxRetryCount, "soldget", true, false);
			Assert.assertFalse(result);
		} catch (UserNeedAuthException e1) {
		} catch (Exception e) {
		}

//		assertEquals(2L, cn.loveapp.orders.router.common.proto.getPageNo().longValue());
		assertEquals(3, proto.getHistoryProgressList().size());

		verify(tradeApiPlatformHandleService, times(6)).soldGet(any(), any(), any(),anyString());

		comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick);

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead1 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead1.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead1.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead1.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest1 = new PullApiOrdersRequest();
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(tradesSoldGetResponse1));
		pullApiOrdersRequest1.setEndPoint(false);
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest1.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto1 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto1.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto1.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest1),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSellerId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		PullApiOrdersRequest pullApiOrdersRequest13 = new PullApiOrdersRequest();
		pullApiOrdersRequest13.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(tradesSoldGetResponse13));
		pullApiOrdersRequest13.setEndPoint(false);
		pullApiOrdersRequest13.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest13.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto13 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto13.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);

		//封装authrequest
		pullHistoryOrdersRequestProto13.setPullApiOrdersRequest(pullApiOrdersRequest13);

//		verify(rocketMqQueueHelper)
//			.push(any(), any(), eq(pullApiOrdersRequest13), eq(ordersPullApiHistoryOnsProducer));

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest13),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		PullApiOrdersRequest pullApiOrdersRequest14 = new PullApiOrdersRequest();
		pullApiOrdersRequest14.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(tradesSoldGetResponse14));
		pullApiOrdersRequest14.setEndPoint(false);
		pullApiOrdersRequest14.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest14.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto14 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto14.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);

		//封装authrequest
		pullHistoryOrdersRequestProto14.setPullApiOrdersRequest(pullApiOrdersRequest14);

//		verify(rocketMqQueueHelper)
//			.push(any(), any(), eq(pullHistoryOrdersRequestProto14), eq(ordersPullApiHistoryOnsProducer));

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest14),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		SoldGetResponse newTradesSoldGetResponseEnd = new SoldGetResponse();
		BeanUtils.copyProperties(lastTradesSoldGetResponse, newTradesSoldGetResponseEnd);
		newTradesSoldGetResponseEnd.setTrades(Lists.newArrayList(firstTrade));
		PullApiOrdersRequest pullApiOrdersRequestEnd = new PullApiOrdersRequest();
		pullApiOrdersRequestEnd.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(newTradesSoldGetResponseEnd));
		pullApiOrdersRequestEnd.setEndPoint(true);
		pullApiOrdersRequestEnd.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequestEnd.getPullHistoryOrdersRequests().get(0)));

		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProtoEnd = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProtoEnd.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);

		//封装authrequest
		pullHistoryOrdersRequestProtoEnd.setPullApiOrdersRequest(pullApiOrdersRequestEnd);

//		verify(rocketMqQueueHelper)
//			.push(any(), any(), eq(pullHistoryOrdersRequestProtoEnd), eq(ordersPullApiHistoryOnsProducer));

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequestEnd),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));
	}

	@Test
	public void sendApiOns6() throws Exception {
		boolean ignoreSameModified = true;
		sendHistoryTradeOnsService.stop();
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		try {
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, null, true, false);
			Assert.assertFalse(result);

		} catch (UserNeedAuthException e1) {
		}


		verify(tradeApiPlatformHandleService, never())
			.soldGet(any(), anyString(), anyString(), anyString());

	}

	@Test
	public void sendApiOns7() throws Exception {
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName(CommonAppConstants.APP_TRADE);
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);

		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);

		soldGetRequest.setPageNo(1L);
		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setRefundStatus(null);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setTotalResults(0L);
		response.getTrades().clear();
		response.setHasNext(false);
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick)).thenReturn(response);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO",StringUtils.EMPTY))
			.thenReturn(response);

		try {
			when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s, e, 3));
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", true, false);
			Assert.assertTrue(result);

		} catch (UserNeedAuthException e1) {
		}


		verify(sendHistoryTradeOnsService)
			.loopTradeEach2Fullinfo(eq(sellerNick), eq("123123123"), any(),eq(StringUtils.EMPTY), any(), eq(ignoreSameModified), eq("soldget"));

	}

	/**
	 * 直接入库不发送到fullinfo
	 * @throws Exception
	 */
	@Test
	public void sendApiOns7_1() throws Exception {
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName(CommonAppConstants.APP_TRADE);
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);

		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);

		soldGetRequest.setPageNo(1L);
		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setRefundStatus(null);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setTotalResults(0L);
		response.getTrades().clear();
		response.setHasNext(false);
		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick)).thenReturn(response);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(userService.getSellerInfoBySellerNick(sellerNick, "TAO", StringUtils.EMPTY)).thenReturn(userInfoBo);
		when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s,e,3));

		try {
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", false, false);
			Assert.assertTrue(result);

		} catch (UserNeedAuthException e1) {
		}


		verify(sendHistoryTradeOnsService, never())
			.loopTradeEach2Fullinfo(eq(sellerNick), eq("123123123"), any(),eq(StringUtils.EMPTY), any(), eq(ignoreSameModified), eq("soldget"));

		verify(sendHistoryTradeOnsService)
			.loopTradeEachSave2DB(eq(sellerNick), eq("123123123"), any(),eq(StringUtils.EMPTY), any(), eq(ignoreSameModified), eq(topSession), eq(false));
	}

	/**
	 * 库中modified比soldget老, ignoreSameModified=true
	 * @throws Exception
	 */
	@Test
	public void sendApiOns8() throws Exception{
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		comLoveRpcInnerprocessRequestHead.setAppName(CommonAppConstants.APP_TRADE);
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);

		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);
		soldGetRequest.setPageNo(1L);

		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setHasNext(true);

		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
//			.thenReturn(response);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		SoldGetRequest soldGetRequest2 = generateSoldGet();
		soldGetRequest2.setStartCreated(s);
		soldGetRequest2.setEndCreated(e);
		soldGetRequest2.setPageNo(2L);
		soldGetRequest2.setPageSize(0L);

		soldGetRequest2.setTopSession(topSession);
		soldGetRequest2.setOrderStatus(taoStatus);
		soldGetRequest2.setUseHasNext(true);

		SoldGetResponse response2 = generateTradeSoldGet();
		response2.setHasNext(true);

		SoldGetRequest soldGetRequest3 = generateSoldGet();
		soldGetRequest3.setStartCreated(s);
		soldGetRequest3.setEndCreated(e);
		soldGetRequest3.setPageNo(3L);
		soldGetRequest3.setPageSize(0L);

		soldGetRequest3.setTopSession(topSession);
		soldGetRequest3.setOrderStatus(taoStatus);
		soldGetRequest3.setUseHasNext(true);

		SoldGetResponse response3 = generateTradeSoldGet();
		response3.setHasNext(false);

		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
		//			.thenReturn(response);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest2, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response2);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest3, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response3);


		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
		//			.thenReturn(response);
		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(userService.getSellerInfoBySellerNick(sellerNick, "TAO", StringUtils.EMPTY)).thenReturn(userInfoBo);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setModified(DateUtils.addMinutes(response.getTrades().get(0).getModified(), -10));
		when(orderRepository.queryByTidGetAyTradeMain(anyString(), anyString(), anyString(), any())).thenReturn(ayTradeMain);
		try {
			when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s, e, 3));
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", true, false);

			Assert.assertFalse(result);

		} catch (UserNeedAuthException e1) {
		}

		verify(sendHistoryTradeOnsService, times(3))
			.loopTradeEach2Fullinfo(eq(sellerNick), eq("123123123"), any(), eq(StringUtils.EMPTY), any(), eq(ignoreSameModified), eq("soldget"));

	}

	/**
	 * 库中modified比soldget与其中一个一样, ignoreSameModified=true
	 * @throws Exception
	 */
	@Test
	public void sendApiOns9() throws Exception{
		boolean ignoreSameModified = true;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);

		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);
		soldGetRequest.setPageNo(1L);

		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setHasNext(false);

		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
		//			.thenReturn(response);

		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(userService.getSellerInfoBySellerNick(sellerNick, "TAO", StringUtils.EMPTY)).thenReturn(userInfoBo);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setModified(response.getTrades().get(0).getModified());
		when(orderRepository.queryByTidGetAyTradeMain(anyString(), anyString(), anyString(), any())).thenReturn(ayTradeMain);
		when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s,e,3));
		try {
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", true, false);
			Assert.assertFalse(result);

		} catch (UserNeedAuthException e1) {
		}

//		verify(rocketMqQueueHelper, times(2)).push(any(), any(), any(), any(DefaultMQProducer.class));
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead1 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead1.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead1.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead1.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest1 = new PullApiOrdersRequest();
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest1.setEndPoint(false);
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest1.getPullHistoryOrdersRequests().get(1)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto1 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto1.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto1.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest1),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead2 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead2.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead2.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead2.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest2 = new PullApiOrdersRequest();
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest2.setEndPoint(true);
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest2.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto2 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto2.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto2.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest2),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));



	}

	/**
	 * 库中modified比soldget老, ignoreSameModified=false
	 * @throws Exception
	 */
	@Test
	public void sendApiOns10() throws Exception{
		boolean ignoreSameModified = false;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

//		SoldGetRequest soldGetRequest = generateSoldGet();
//		soldGetRequest.setStartCreated(s);
//		soldGetRequest.setEndCreated(e);
//
//		SoldGetResponse response = generateTradeSoldGet();
////		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
////			.thenReturn(response);
//		when(tradeService.soldget(soldGetRequest, sellerNick, "TAO"))
//			.thenReturn(response);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);
		soldGetRequest.setPageNo(1L);

		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setHasNext(false);

		//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
		//			.thenReturn(response);

		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(userService.getSellerInfoBySellerNick(sellerNick, "TAO", StringUtils.EMPTY)).thenReturn(userInfoBo);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setModified(DateUtils.addMinutes(response.getTrades().get(0).getModified(), -10));
		when(orderRepository.queryByTidGetAyTradeMain(anyString(), anyString(), anyString(), any())).thenReturn(ayTradeMain);
		when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s,e,3));


		try {
//			boolean result = sendHistoryTradeOnsService.sendApiOns(proto, "123123123", sellerNick, s, e, taoStatus,
//				null, null, ignoreSameModified, 1, null);
//			Assert.assertFalse(result);

			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", true, false);
			Assert.assertFalse(result);

		} catch (UserNeedAuthException e1) {
		}
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead1 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead1.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead1.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead1.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest1 = new PullApiOrdersRequest();
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest1.setEndPoint(false);
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest1.getPullHistoryOrdersRequests().get(1)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto1 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto1.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto1.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest1),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead2 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead2.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead2.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead2.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest2 = new PullApiOrdersRequest();
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest2.setEndPoint(false);
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest2.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto2 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto2.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto2.setPullApiOrdersRequest(pullApiOrdersRequest1);

		//封装authrequest
		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest2),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead3 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead3.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead3.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead3.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest3 = new PullApiOrdersRequest();
		pullApiOrdersRequest3.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest3.setEndPoint(true);
		pullApiOrdersRequest3.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest3.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto3 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto3.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto3.setPullApiOrdersRequest(pullApiOrdersRequest1);

		//封装authrequest
		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest3),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("soldget"),
				eq("*"), eq(0));
	}


	/**
	 * 库中modified比soldget与其中一个一样, ignoreSameModified=false
	 *
	 * @throws Exception
	 */
	@Test
	public void sendApiOns11() throws Exception{
		boolean ignoreSameModified = false;
		String sellerNick = "中华人民共和国";
		LocalDateTime s = LocalDateTime.now();
		LocalDateTime e = LocalDateTime.now();
		String taoStatus = TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS;
		String topSession = "321321";
		PullSoldGetApiOrdersRequestProto proto = new PullSoldGetApiOrdersRequestProto();
		proto.setPullHistoryApiOrdersRequest(new PullSoldGetApiOrdersRequest());
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setPlatformId("TAO");
		proto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		when(userService.getAuthorization(eq(sellerNick), any(), eq(CommonPlatformConstants.PLATFORM_TAO), anyString())).thenReturn(topSession);

//		SoldGetRequest soldGetRequest = generateSoldGet();
//		soldGetRequest.setStartCreated(s);
//		soldGetRequest.setEndCreated(e);
//
//		SoldGetResponse response = generateTradeSoldGet();
//		when(tradeSoldGetService.spinSoldGet(soldGetRequest, sellerNick))
//			.thenReturn(response);

		SoldGetRequest soldGetRequest = generateSoldGet();
		soldGetRequest.setStartCreated(s);
		soldGetRequest.setEndCreated(e);
		soldGetRequest.setPageNo(1L);

		soldGetRequest.setTopSession(topSession);
		soldGetRequest.setOrderStatus(taoStatus);
		soldGetRequest.setUseHasNext(true);

		SoldGetResponse response = generateTradeSoldGet();
		response.setHasNext(false);

		when(tradeApiPlatformHandleService.soldGet(soldGetRequest, sellerNick, "TAO", StringUtils.EMPTY))
			.thenReturn(response);

		UserInfoBo userInfoBo = new UserInfoBo("123123123", sellerNick, "123123123", null, "TAO", 1, topSession,
			"xxxxxxxxxxxxxxx", LocalDateTime.now(), LocalDateTime.now());
		when(userService.getSellerInfoBySellerNick(sellerNick, "TAO", StringUtils.EMPTY)).thenReturn(userInfoBo);

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setModified(response.getTrades().get(0).getModified());
		when(orderRepository.queryByTidGetAyTradeMain(anyString(), anyString(), anyString(), any())).thenReturn(ayTradeMain);

		when(sendHistoryTradeOnsDynamicService.generateProgress(any(), any(), any(), anyString(), anyString())).thenReturn(getSoldGetProgresses(s,e,3));

		try {
//			boolean result = sendHistoryTradeOnsService.sendApiOns(proto, "123123123", sellerNick, s, e, taoStatus,
//				null, null, ignoreSameModified, 1, null);
//			Assert.assertFalse(result);
			boolean result = sendHistoryTradeOnsService.pullHistoryTrades(proto, "123123123", sellerNick, s, e, taoStatus,
				null, null, ignoreSameModified, 1, "soldget", true, false);
			Assert.assertFalse(result);
		} catch (UserNeedAuthException e1) {
		}

//		verify(rocketMqQueueHelper, times(3)).push(any(), any(), any(), any(DefaultMQProducer.class));
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead1 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead1.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead1.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead1.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest1 = new PullApiOrdersRequest();
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest1.setEndPoint(false);
		pullApiOrdersRequest1.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest1.getPullHistoryOrdersRequests().get(1)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto1 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto1.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto1.setPullApiOrdersRequest(pullApiOrdersRequest1);

		//封装authrequest

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest1),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead2 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead2.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead2.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead2.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest2 = new PullApiOrdersRequest();
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest2.setEndPoint(false);
		pullApiOrdersRequest2.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest2.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto2 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto2.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto2.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest2),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));

		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead3 = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead3.setSellerNick(userInfoBo.getSellerNick());
		comLoveRpcInnerprocessRequestHead3.setSellerId(userInfoBo.getSellerId());
		comLoveRpcInnerprocessRequestHead3.setPlatformId(CommonPlatformConstants.PLATFORM_TAO);

		PullApiOrdersRequest pullApiOrdersRequest3 = new PullApiOrdersRequest();
		pullApiOrdersRequest3.setPullHistoryOrdersRequests(generatePullHistoryOrdersRequest(response));
		pullApiOrdersRequest3.setEndPoint(true);
		pullApiOrdersRequest3.setPullHistoryOrdersRequests(Lists.newArrayList(pullApiOrdersRequest3.getPullHistoryOrdersRequests().get(0)));
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto3 = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto3.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead1);
		pullHistoryOrdersRequestProto3.setPullApiOrdersRequest(pullApiOrdersRequest1);

		verify(tradeSendOrderHandleService)
			.pushRequestFullInfoTopic(eq(pullApiOrdersRequest3),
				eq(userInfoBo.getSellerNick()),
				eq(userInfoBo.getSellerId()),
				eq(userInfoBo.getSupplierId()),
				eq(CommonPlatformConstants.PLATFORM_TAO),
				eq(StringUtils.EMPTY),
				eq("soldget"),
				//eq("soldget"+"-"+userInfoBo.getUserDbId().getDbId()),
				eq("*"), eq(0));
	}

	/**
	 * ignoreSameModified=true
	 */
	@Test
	public void filterInvalidTrade() {
		String sellerNick = "中华人民共和国";
		String sellerId = "sellerId";
		String platformId = CommonPlatformConstants.PLATFORM_TAO;
		String appName = CommonAppConstants.APP_TRADE;
		boolean ignoreSameModified = true;

		AyTrade trade1 = new AyTrade();
		trade1.setTidStr("tid1");
		trade1.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade1.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade2 = new AyTrade();
		trade2.setTidStr("tid2");
		trade2.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade2.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade3 = new AyTrade();
		trade3.setTidStr("tid3");
		trade3.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade3.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade4 = new AyTrade();
		trade4.setTidStr("tid4");
		trade4.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade4.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade5 = new AyTrade();
		trade5.setTidStr("tid5");
		trade5.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade5.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		// 时间一样, 忽略
		AyTradeMain main1 = new AyTradeMain();
		main1.setModified(trade1.getModified());
		main1.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main1.setTaoStatus(trade1.getStatus());

		// 时间小于, 忽略
		AyTradeMain main2 = new AyTradeMain();
		main2.setModified(DateUtils.addMinutes(trade2.getModified(), 1));
		main2.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main2.setTaoStatus(trade2.getStatus());

		// 时间一样, 加密方式不一样, 不忽略
		AyTradeMain main3 = new AyTradeMain();
		main3.setModified(trade3.getModified());
		main3.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_DECRYPTION_FAILURE);
		main3.setTaoStatus(trade3.getStatus());

		// 时间一样, 状态不一样, 不忽略
		AyTradeMain main4 = new AyTradeMain();
		main4.setModified(trade4.getModified());
		main4.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main4.setTaoStatus(trade4.getStatus() + 1);

		// 时间大于, 不忽略
		AyTradeMain main5 = new AyTradeMain();
		main5.setModified(DateUtils.addMinutes(trade5.getModified(), 1));
		main5.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main5.setTaoStatus(trade5.getStatus());


		when(orderRepository.queryByTidGetAyTradeMain(eq(trade1.getTidStr()), eq(platformId), eq(sellerId), eq(appName))).thenReturn(main1);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade2.getTidStr()), eq(platformId), eq(sellerId), eq(appName))).thenReturn(main2);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade3.getTidStr()), eq(platformId), eq(sellerId), eq(appName))).thenReturn(main3);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade4.getTidStr()), eq(platformId), eq(sellerId), eq(appName))).thenReturn(main4);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade5.getTidStr()), eq(platformId), eq(sellerId), eq(appName))).thenReturn(main5);
		///ayTradeMain.getEncryptionType(), ayTradeMain.getBuyerNick(), trade.getBuyerNick(), platformId, appName
		when(encryptionPlatformHandleService
			.isDecryptionFailed(eq(main1.getEncryptionType()), eq(main1.getBuyerNick()), eq(trade1.getBuyerNick()), eq(platformId), eq(appName))).thenReturn(false);
		when(encryptionPlatformHandleService
			.isDecryptionFailed(eq(main2.getEncryptionType()), eq(main2.getBuyerNick()), eq(trade2.getBuyerNick()), eq(platformId), eq(appName))).thenReturn(false);
		when(encryptionPlatformHandleService
			.isDecryptionFailed(eq(main3.getEncryptionType()), eq(main3.getBuyerNick()), eq(trade3.getBuyerNick()), eq(platformId), eq(appName))).thenReturn(true);
		List<AyTrade> trades = Lists.newArrayList(trade1, trade2, trade3, trade4, trade5);
		List<AyTrade> result = sendHistoryTradeOnsService.filterInvalidTrade(sellerNick, sellerId, platformId, appName, ignoreSameModified, trades);

		assertEquals(JSON.toJSON(Lists.newArrayList(trade3, trade4, trade5)), JSON.toJSON(result));

	}

	/**
	 * ignoreSameModified=false
	 */
	@Test
	public void filterInvalidTrade2() {
		String sellerNick = "中华人民共和国";
		String sellerId = "sellerId";
		String platformId = CommonAppConstants.APP_TRADE;
		boolean ignoreSameModified = false;

		AyTrade trade1 = new AyTrade();
		trade1.setTidStr("tid1");
		trade1.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade1.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade2 = new AyTrade();
		trade2.setTidStr("tid2");
		trade2.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade2.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		AyTrade trade3 = new AyTrade();
		trade3.setTidStr("tid3");
		trade3.setModified(DateUtil.parseDateString("2020-08-12 00:00:00"));
		trade3.setStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		// 时间一样, 不忽略
		AyTradeMain main1 = new AyTradeMain();
		main1.setModified(trade1.getModified());
		main1.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main1.setTaoStatus(trade1.getStatus());

		// 时间小于, 忽略
		AyTradeMain main2 = new AyTradeMain();
		main2.setModified(DateUtils.addMinutes(trade2.getModified(), 1));
		main2.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main2.setTaoStatus(trade2.getStatus());

		// 时间大于, 不忽略
		AyTradeMain main3 = new AyTradeMain();
		main3.setModified(DateUtils.addMinutes(trade3.getModified(), -1));
		main3.setEncryptionType(EncryptionTypeConstant.ENCRYPTION_LOVE);
		main3.setTaoStatus(trade3.getStatus());


		when(orderRepository.queryByTidGetAyTradeMain(eq(trade1.getTidStr()), eq(platformId), eq(sellerId), isNull())).thenReturn(main1);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade2.getTidStr()), eq(platformId), eq(sellerId), isNull())).thenReturn(main2);
		when(orderRepository.queryByTidGetAyTradeMain(eq(trade3.getTidStr()), eq(platformId), eq(sellerId), isNull())).thenReturn(main3);


		List<AyTrade> trades = Lists.newArrayList(trade1, trade2, trade3);
		List<AyTrade> result = sendHistoryTradeOnsService.filterInvalidTrade(sellerNick, sellerId, platformId, null, ignoreSameModified, trades);

		assertEquals(JSON.toJSON(Lists.newArrayList(trade1, trade3)), JSON.toJSON(result));

	}

	private List<PullHistoryOrdersRequest> generatePullHistoryOrdersRequest(
		SoldGetResponse tradesSoldGetResponse) {
		List<PullHistoryOrdersRequest> list = Lists.newArrayList();
		for (Trade trade : tradesSoldGetResponse.getTrades()) {
			PullHistoryOrdersRequest pullHistoryOrdersRequest = new PullHistoryOrdersRequest();
			pullHistoryOrdersRequest.setTid(String.valueOf(trade.getTid()));
			pullHistoryOrdersRequest.setModified(DateUtil.parseDate(trade.getModified()));
			list.add(pullHistoryOrdersRequest);
		}
		return list;
	}

	private SoldGetRequest generateSoldGet() {
		SoldGetRequest soldGetRequest = new SoldGetRequest();
		soldGetRequest.setTopSession("321321");
		soldGetRequest.setPageSize(taobaoSoldgetAppConfig.getPageSize());
		soldGetRequest.setPageNo(1L);
		soldGetRequest.setApiFileds(taobaoSoldgetAppConfig.getFileds());
		soldGetRequest.setOrderStatus(TaobaoStatusConstant.WAIT_SELLER_CONFIRM_GOODS);
		return soldGetRequest;
	}

	private SoldGetResponse generateTradeSoldGet() {
		List<AyTrade> tradeList = new ArrayList<>();
		tradeList.add(generateTrade());
		AyTrade trade = generateTrade();
		trade.setTid(2222222L);
		trade.getOrders().get(0).setOid(2222222L);
		trade.setModified(DateUtil.convertLocalDateTimetoDate(DateUtil.parseDate(trade.getModified()).plusSeconds(1)));
		tradeList.add(trade);
		SoldGetResponse tradesSoldGetResponse = new SoldGetResponse();
		tradesSoldGetResponse.setErrorCode(null);
		tradesSoldGetResponse.setHasNext(false);
		tradesSoldGetResponse.setTrades(tradeList);
		return tradesSoldGetResponse;
	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		trade.setModified(new Date());
		trade.setCreated(new Date());
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}

	protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated, int part) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		for (int i = 0; i < part; i++) {
			LocalDateTime startTime = endCreated.minusMonths(i + 1);
			if (startCreated != null && startCreated.isAfter(startTime)) {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
				break;
			} else if(i == part - 1){
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(null, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			} else {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			}
		}
		return soldGetProgressList;
	}

	protected List<SyncOrdersProgress> getPddSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated, int part) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		LocalDateTime startTime = endCreated.minusDays(1);
		for (int i = 0; i < part; i++) {
			if (startCreated != null && startCreated.isAfter(startTime)) {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated);
				soldGetProgressList.add(soldGetProgress);
				break;
			} else {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated);
				soldGetProgressList.add(soldGetProgress);
				endCreated = endCreated.minusDays(1);
				startTime = startTime.minusDays(1);
			}
		}
		return soldGetProgressList;
	}

}
