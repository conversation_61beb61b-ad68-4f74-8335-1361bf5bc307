package cn.loveapp.orders.consumer.common.merge;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.bson.Document;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.*;
import org.springframework.jdbc.core.JdbcTemplate;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.*;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.UpdateResult;
import com.mongodb.connection.ClusterConnectionMode;
import com.mongodb.connection.ConnectionPoolSettings;
import com.mongodb.connection.ServerSettings;
import com.mongodb.connection.SocketSettings;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.orders.common.config.mongo.MongoDBConfig;
import cn.loveapp.orders.common.constant.AyStatusConstant;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.JdpTbTrade;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;

/**
 * MergeTest
 *
 * <AUTHOR>
 * @date 2019-08-21
 */

//@Ignore
public class MergeTest {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MergeTest.class);

	private static JdbcTemplate pushRdsJdbcTemplate;

	private static DefaultMQProducer saveOrderProducer;

	private static RestHighLevelClient restHighLevelClient;

	private static MongoClient mongoClient;
	private static MongoDatabase mongoDatabase;

	private static String sellerNick = "自然卷kevin";
	private static String sellerId = "1641661506";
	private final static String TOPIC = "iysaverdsorders1";
	private final static String TMC_TOPIC = "iytmcfullinfoorders1";
	private final static String LOGISTICS = "中通快递";
	private final static String SELLER_MEMO = "SELLER_MEMO";
	private final static String BEIJING = "北京";
	private final static String GUANGZHOU = "广州";
	private final static String TID = "10000000000000000";
	private final static String BEIJING_MERGE_MD5 = "86A787A564994A5A9C712E22E0887E99";
	private final static String GUANGZHOU_MERGE_MD5 = "D9C367AA75B21498BC62EEE9E72D65F0";
	private final static String TRADE_ATTR = "TRADE_ATTR";
	private final static String TIMING_PROMISE = "TIMING_PROMISE";
	private final static String PROMISE_SERVICE = "PROMISE_SERVICE";
	private final static String BUYER_MESSAGE = "BUYER_MESSAGE";
	private final static String TIMING_ATTR = "{\\\"cutoffMinutes\\\":\\\"960\\\",\\\"collectTime\\\":\\\"2019-07-03 23:59:59\\\",\\\"deliveryTime\\\":\\\"2019-07-03 23:59:59\\\", \\\"signTime\\\":\\\"2019-07-04 23:59:00\\\",\\\"erpHold\\\":\\\"0\\\",\\\"cnService\\\":\\\"81\\\",\\\"esTime\\\":\\\"1\\\"}";

	private int autoInc = 0;

	private String TRADE_FULLINFO_GET_RESPONSE =
		"{\n"
			+ "    \"trade_fullinfo_get_response\": {\n"
			+ "        \"trade\": {\n"
			+ "            \"oaid\": \"oaid\",\n"
			+ "            \"step_trade_status\": \"FRONT_NOPAID_FINAL_NOPAID\",\n"
			+ "            \"seller_memo\": \"颜色分类:ACT5222MCK中号本-横线\",\n"
			+ "            \"buyer_message\": \"BUYER_MESSAGE\",\n"
			+ "            \"timing_promise\": \"TIMING_PROMISE\",\n"
			+ "            \"promise_service\": \"PROMISE_SERVICE\",\n"
			+ "            \"trade_attr\": \"TRADE_ATTR\",\n" + "            \"tid\": 10000000000000000,\n"
			+ "            \"tid_str\": \"10000000000000000\",\n" + "            \"status\": \"TAO_STATUS\",\n"
			+ "            \"type\": \"fixed\",\n" + "            \"seller_nick\": \"自然卷kevin\",\n"
			+ "            \"buyer_nick\": \"~HWUtlaTZs7TxpzJ5vDhBbw==~WzMsN4fW~1~~\",\n"
			+ "            \"created\": \"2030-08-03 12:40:27\",\n"
			+ "            \"modified\": \"2030-08-05 08:52:44\",\n" + "            \"adjust_fee\": \"0.00\",\n"
			+ "            \"alipay_no\": \"2019080322001134200573635436\",\n" + "            \"alipay_point\": 0,\n"
			+ "            \"available_confirm_fee\": \"36.00\",\n"
			+ "            \"buyer_alipay_no\": \"~mkQf9kzlu1FpjlTRKSnnTA==~1~\",\n"
			+ "            \"buyer_area\": \"北京北京IDC机房\",\n" + "            \"buyer_cod_fee\": \"0.00\",\n"
			+ "            \"buyer_email\": \"~QyQrXB2+j9hSq3ZBOVN8xbaum3XJLf3MIQM2UNqpw8o=~s8YcT+s1zJs4lwR8bVwvVSn5ibHTLtq/cZJ0k7LrBH8go5hoVQ49~1~~\",\n"
			+ "            \"buyer_obtain_point_fee\": 0,\n" + "            \"buyer_rate\": false,\n"
			+ "            \"cod_fee\": \"0.00\",\n" + "            \"cod_status\": \"NEW_CREATED\",\n"
			+ "            \"coupon_fee\": 0,\n" + "            \"commission_fee\": \"0.00\",\n"
			+ "            \"consign_time\": \"2019-08-05 08:52:44\",\n" + "            \"discount_fee\": \"0.00\",\n"
			+ "            \"has_post_fee\": true,\n" + "            \"has_yfx\": false,\n"
			+ "            \"is_3D\": false,\n" + "            \"is_brand_sale\": false,\n"
			+ "            \"is_daixiao\": false,\n" + "            \"is_force_wlb\": false,\n"
			+ "            \"is_sh_ship\": false,\n" + "            \"is_lgtype\": false,\n"
			+ "            \"is_part_consign\": false,\n" + "            \"is_wt\": false,\n"
			+ "            \"is_gift\": false,\n" + "            \"num\": 1,\n"
			+ "            \"num_iid\": ************,\n" + "            \"new_presell\": false,\n"
			+ "            \"nr_shop_guide_id\": \"\",\n" + "            \"nr_shop_guide_name\": \"\",\n"
			+ "            \"orders\": {\n" + "                "
			+ "					\"order\": [\n"

			+ "						{\n"
			+ "                        \"adjust_fee\": \"0.00\",\n" + "                        \"buyer_rate\": false,\n"
			+ "                        \"cid\": 50012677,\n"
			+ "                        \"consign_time\": \"2019-08-05 08:52:44\",\n"
			+ "                        \"discount_fee\": \"9.00\",\n"
			+ "                        \"divide_order_fee\": \"36.00\",\n"
			+ "                        \"invoice_no\": \"75166073192260\",\n"
			+ "                        \"is_daixiao\": false,\n" + "                        \"is_oversold\": false,\n"
			+ "                        \"logistics_company\": \"中通快递\",\n" + "                        \"num\": 1,\n"
			+ "                        \"num_iid\": ************,\n"
			+ "                        \"oid\": 10000000000000000,\n"
			+ "                        \"oid_str\": \"10000000000000000\",\n"
			+ "                        \"order_from\": \"WAP,WAP\",\n"
			+ "                        \"part_mjz_discount\": \"0.00\",\n"
			+ "                        \"payment\": \"36.00\",\n"
			+ "                        \"pic_path\": \"https://img.alicdn.com/bao/uploaded/i1/1641661506/TB24LMGklDH8KJjy1zeXXXjepXa_!!1641661506.jpg\",\n"
			+ "                        \"price\": \"45.00\",\n"
			+ "                        \"refund_status\": \"NO_REFUND\",\n"
			+ "                        \"seller_rate\": false,\n" + "                        \"seller_type\": \"C\",\n"
			+ "                        \"shipping_type\": \"express\",\n"
			+ "                        \"sku_id\": \"3715156251443\",\n"
			+ "                        \"outer_iid\": \"颜色分类:AaCT5111MCK中号本-横线\",\n"
			+ "                        \"outer_sku_id\": \"颜色分类:AaCT5222MCK中号本-横线\",\n"
			+ "                        \"sku_properties_name\": \"颜色分类:AaCT5333MCK中号本-横线\",\n"
			+ "                        \"snapshot_url\": \"p:10000000000000000_1\",\n"
			+ "                        \"status\": \"TAO_STATUS\",\n"
			+ "                        \"timeout_action_time\": \"2019-08-15 08:52:44\",\n"
			+ "                        \"title\": \"包邮 纯黑磨砂空白横线记事本 口袋本Aa7 笔记本定制日记本文具A5\",\n"
			+ "                        \"total_fee\": \"36.00\"\n" + "                    }\n"
			+ "						,{\n"
			+ "                        \"adjust_fee\": \"0.00\",\n" + "                        \"buyer_rate\": false,\n"
			+ "                        \"cid\": 50012678,\n"
			+ "                        \"consign_time\": \"2019-08-05 08:52:44\",\n"
			+ "                        \"discount_fee\": \"9.00\",\n"
			+ "                        \"divide_order_fee\": \"36.00\",\n"
			+ "                        \"invoice_no\": \"75166073192260\",\n"
			+ "                        \"is_daixiao\": false,\n" + "                        \"is_oversold\": false,\n"
			+ "                        \"logistics_company\": \"中通快递\",\n" + "                        \"num\": 1,\n"
			+ "                        \"num_iid\": ************,\n"
			+ "                        \"oid\": 11000000000000000,\n"
			+ "                        \"oid_str\": \"11000000000000000\",\n"
			+ "                        \"order_from\": \"WAP,WAP\",\n"
			+ "                        \"part_mjz_discount\": \"0.00\",\n"
			+ "                        \"payment\": \"36.00\",\n"
			+ "                        \"pic_path\": \"https://img.alicdn.com/bao/uploaded/i1/1641661506/TB24LMGklDH8KJjy1zeXXXjepXa_!!1641661506.jpg\",\n"
			+ "                        \"price\": \"45.00\",\n"
			+ "                        \"refund_status\": \"NO_REFUND\",\n"
			+ "                        \"seller_rate\": false,\n" + "                        \"seller_type\": \"C\",\n"
			+ "                        \"shipping_type\": \"express\",\n"
			+ "                        \"sku_id\": \"3715156251443\",\n"
			+ "                        \"outer_iid\": \"颜色分类:AaCT5111MCK中号本-横线\",\n"
			+ "                        \"outer_sku_id\": \"颜色分类:AaCT5222MCK中号本-横线\",\n"
			+ "                        \"sku_properties_name\": \"颜色分类:AaCT5333MCK中号本-横线\",\n"
			+ "                        \"snapshot_url\": \"p:10000000000000000_1\",\n"
			+ "                        \"status\": \"TAO_STATUS\",\n"
			+ "                        \"timeout_action_time\": \"2019-08-15 08:52:44\",\n"
			+ "                        \"title\": \"包邮 纯黑磨砂空白横线记事本 口袋本Aa7 笔记本定制日记本文具A5\",\n"
			+ "                        \"total_fee\": \"36.00\"\n" + "                    }\n"

			+ "					]\n"
			+ "            },\n"
			+ "            \"pay_time\": \"2019-08-03 23:35:38\",\n"
			+ "            \"payment\": \"63.00\",\n" + "            \"pcc_af\": 0,\n"
			+ "            \"pic_path\": \"https://img.alicdn.com/bao/uploaded/i1/1641661506/TB24LMGklDH8KJjy1zeXXXjepXa_!!1641661506.jpg\",\n"
			+ "            \"platform_subsidy_fee\": \"0.00\",\n" + "            \"point_fee\": 0,\n"
			+ "            \"post_fee\": \"0.00\",\n" + "            \"price\": \"45.00\",\n"
			+ "            \"promotion_details\": {\n" + "                \"promotion_detail\": [\n"
			+ "                    {\n" + "                        \"discount_fee\": \"0.00\",\n"
			+ "                        \"id\": 10000000000000000,\n"
			+ "                        \"promotion_desc\": \"满减促销:省0.00元\",\n"
			+ "                        \"promotion_id\": \"mzmjll-8643664346_75871368338\",\n"
			+ "                        \"promotion_name\": \"满减促销\"\n" + "                    },\n"
			+ "                    {\n" + "                        \"discount_fee\": \"9.00\",\n"
			+ "                        \"id\": 10000000000000000,\n"
			+ "                        \"promotion_desc\": \"优惠促销:省9.00元\",\n"
			+ "                        \"promotion_id\": \"MZDZ33760-7272592377_51119872216\",\n"
			+ "                        \"promotion_name\": \"优惠促销\"\n" + "                    }\n"
			+ "                ]\n" + "            },\n" + "            \"real_point_fee\": 0,\n"
			+ "            \"received_payment\": \"0.00\",\n"
			+ "            \"receiver_address\": \"望京街道方舟苑小区5号楼1202\",\n" + "            \"receiver_city\": \"北京市\",\n"
			+ "            \"receiver_country\": \"\",\n" + "            \"receiver_district\": \"朝阳区\",\n"
			+ "            \"receiver_mobile\": \"c77bfda61a0204d445185053e6a9a8fe~~~MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW5VVqGJCzQdoYOCXo7T7fXC\",\n"
			+ "            \"receiver_name\": \"5L2y6bmo6aO3\",\n" + "            \"receiver_state\": \"北京\",\n"
			+ "            \"receiver_town\": \"望京街道\",\n" + "            \"receiver_zip\": \"000000\",\n"
			+ "            \"seller_alipay_no\": \"***<EMAIL>\",\n" + "            \"seller_can_rate\": false,\n"
			+ "            \"seller_cod_fee\": \"0.00\",\n" + "            \"seller_email\": \"<EMAIL>\",\n"
			+ "            \"seller_flag\": 0,\n" + "            \"seller_mobile\": \"15205891251\",\n"
			+ "            \"seller_name\": \"宋文凯\",\n" + "            \"seller_phone\": \"0579-85386200\",\n"
			+ "            \"seller_rate\": false,\n" + "            \"service_tags\": {\n"
			+ "                \"logistics_tag\": [\n"
			+ "                    {\n"
			+ "                        \"logistic_service_tag_list\": {\n"
			+ "                            \"logistic_service_tag\": [\n"
			+ "                                {\n"
			+ "                                    \"service_tag\": \"origAreaId=110105;consignDate=24\",\n"
			+ "                                    \"service_type\": \"TB_CONSIGN_DATE\"\n"
			+ "                                },\n"
			+ "                                {\n"
			+ "                                    \"service_tag\": \"lgType=-4\",\n"
			+ "                                    \"service_type\": \"FAST\"\n"
			+ "                                }\n"
			+ "                            ]\n"
			+ "                        },\n"
			+ "                        \"order_id\": \"10000000000000000\"\n"
			+ "                    },\n"
			+ "                    {\n"
			+ "                        \"logistic_service_tag_list\": {\n"
			+ "                            \"logistic_service_tag\": [\n"
			+ "                                {\n"
			+ "                                    \"service_tag\": \"consignDate=24\",\n"
			+ "                                    \"service_type\": \"TB_CONSIGN_DATE\"\n"
			+ "                                }\n"
			+ "                            ]\n"
			+ "                        },\n"
			+ "                        \"order_id\": \"10000000000000000\"\n"
			+ "                    }\n"
			+ "                ]\n"
			+ "            },\n"
			+ "            \"service_type\": \"\",\n"
			+ "            \"shipping_type\": \"express\",\n"
			+ "            \"sid\": \"10000000000000000\",\n"
			+ "            \"snapshot_url\": \"p:10000000000000000_1\",\n"
			+ "            \"timeout_action_time\": \"2019-08-15 08:52:44\",\n"
			+ "            \"title\": \"newair新空气\",\n"
			+ "            \"total_fee\": \"45.00\",\n"
			+ "        		\"trade_attr\": \"{\\\"erpHold\\\":\\\"0\\\"}\",\n"
			+ "            \"trade_from\": \"WAP,WAP\",\n"
			+ "            \"you_xiang\": false\n"
			+ "        }\n"
			+ "    }\n"
			+ "}";

	private String TMC_REQUEST =
		"{\"com.love.rpc.innerprocess.request.head\":{\"sellerNick\":\"自然卷kevin\"},\"pull.orders.request\":{\"endPoint\":false,\"pullHistoryOrdersRequests\":[{\"modified\":\"2030-08-05T08:52:44\",\"tid\":\"10000000000000000\"}]},\"tmc.orders.request\":{\"modified\":\"2030-08-05T08:52:44\",\"oid\":\"10000000000000000\",\"sellerNick\":\"自然卷kevin\",\"tid\":\"10000000000000000\",\"tradeMemoModified\":{\"sellerMemo\":\"SELLER_MEMO\"}}}";

	@BeforeClass
	public static void init() throws Exception {
		saveOrderProducer = new DefaultMQProducer("CID_iysaverdsorders1");
		saveOrderProducer.setSendMsgTimeout(5000);
		saveOrderProducer.setNamesrvAddr("************:9876");
		saveOrderProducer.start();

		DruidDataSource pushRds = new DruidDataSource();
		pushRds.setUrl(
			"**************************************************************************************************************************************************");
		pushRds.setDriverClassName("com.mysql.jdbc.Driver");
		pushRds.setUsername("root");
		pushRds.setPassword("JbcZjTyl");
		pushRds.setMinIdle(0);
		pushRds.setInitialSize(0);
		pushRds.setMaxActive(5);
		pushRdsJdbcTemplate = new JdbcTemplate(pushRds);

		mongoClient = mongoClient();
		mongoDatabase = mongoClient.getDatabase("trade");

		RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost("************", 9200, "http"));
		CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
		credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "elastic"));
		restClientBuilder.setHttpClientConfigCallback(httpClientBuilder->httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
		restHighLevelClient = new RestHighLevelClient(restClientBuilder);
	}

	private static MongoClient mongoClient() {
		//mongodb.config.hosts = ************
		//mongodb.config.port = 27018
		//mongodb.config.account = aiyong
		//mongodb.config.password = aiyong
		//mongodb.config.dbName = trade
		//mongodb.config.minSize = 1
		//mongodb.config.maxSize = 1
		//mongodb.config.maxWaitTime = 60000
		//mongodb.config.maxConnectionLifeTime = 60000
		//mongodb.config.maxConnectionIdleTime = 60000
		//mongodb.config.heartbeatFrequency = 20000
		//mongodb.config.connectTimeout = 2500
		//mongodb.config.readTimeout = 5500
		//mongodb.config.retryWrites = 1
		//mongodb.config.writeConcern = W1
		MongoDBConfig mongoDBConfig = new MongoDBConfig();
		mongoDBConfig.setHosts(Lists.newArrayList("************"));
		mongoDBConfig.setPort(27018);
		mongoDBConfig.setAccount("aiyong");
		mongoDBConfig.setPassword("aiyong");
		mongoDBConfig.setDbName("trade");
		mongoDBConfig.setMinSize(1);
		mongoDBConfig.setMaxSize(1);
		mongoDBConfig.setMaxWaitTime(60000);
		mongoDBConfig.setMaxConnectionLifeTime(60000);
		mongoDBConfig.setMaxConnectionIdleTime(60000);
		mongoDBConfig.setHeartbeatFrequency(20000);
		mongoDBConfig.setConnectTimeout(2500);
		mongoDBConfig.setReadTimeout(5500);
		mongoDBConfig.setRetryWrites(1);
		mongoDBConfig.setWriteConcern("W1");

		String srvHost = mongoDBConfig.getSrvHost();
		List<String> hosts = mongoDBConfig.getHosts();
		List<ServerAddress> addressList = new ArrayList<>();
		for (int i = 0; i < hosts.size(); i++) {
			ServerAddress address = new ServerAddress(hosts.get(i), mongoDBConfig.getPort());
			addressList.add(address);
		}
		MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
			.credential(MongoCredential.createCredential(mongoDBConfig.getAccount(), mongoDBConfig.getDbName(), mongoDBConfig.getPassword().toCharArray()))
			.applyToClusterSettings(
				builder -> {
					if (!org.springframework.util.StringUtils.isEmpty(srvHost)) {
						builder.srvHost(srvHost);
					} else {
						builder.hosts(addressList);
					}
					builder.mode(ClusterConnectionMode.MULTIPLE)
						.serverSelectionTimeout(25000, TimeUnit.MILLISECONDS)
						.localThreshold(30, TimeUnit.MILLISECONDS);
				})
			.applyToSslSettings(builder -> builder.enabled(false))
			.readPreference(ReadPreference.primaryPreferred())
			.writeConcern(mongoDBConfig.getWriteConcern())
			.readConcern(ReadConcern.LOCAL)
			.retryWrites(mongoDBConfig.getRetryWrites())
			.compressorList(Collections
				.singletonList(MongoCompressor.createSnappyCompressor()))
			.applyToConnectionPoolSettings(new Block<ConnectionPoolSettings.Builder>() {

				/**
				 * Apply some logic to the value.
				 *
				 * @param builder the value to apply to
				 */
				@Override
				public void apply(ConnectionPoolSettings.Builder builder) {
					builder.minSize(mongoDBConfig.getMinSize())
						.maxSize(mongoDBConfig.getMaxSize())
						.maxWaitTime(mongoDBConfig.getMaxWaitTime(), TimeUnit.MILLISECONDS)
						.maxConnectionLifeTime(mongoDBConfig.getMaxConnectionLifeTime(), TimeUnit.MILLISECONDS)
						.maxConnectionIdleTime(mongoDBConfig.getMaxConnectionIdleTime(), TimeUnit.MILLISECONDS);
				}
			})
			.applyToServerSettings(new Block<ServerSettings.Builder>() {

				/**
				 * Apply some logic to the value.
				 *
				 * @param builder the value to apply to
				 */
				@Override
				public void apply(ServerSettings.Builder builder) {
					builder.heartbeatFrequency(mongoDBConfig.getHeartbeatFrequency(), TimeUnit.MILLISECONDS);
				}
			})
			.applyToSocketSettings(new Block<SocketSettings.Builder>() {

				/**
				 * Apply some logic to the value.
				 *
				 * @param builder the value to apply to
				 */
				@Override
				public void apply(SocketSettings.Builder builder) {
					builder.connectTimeout(mongoDBConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
						.readTimeout(mongoDBConfig.getReadTimeout(), TimeUnit.MILLISECONDS);
				}
			})
			.build();
		return MongoClients.create(mongoClientSettings);
	}

	@Before
	public void before() {
		LOGGER.logInfo("清理所有数据库数据");
		// 清理数据
		// 推送库
		pushRdsJdbcTemplate.update("delete from jdp_tb_trade where seller_nick=?", sellerNick);
		// 存单库
		mongoDatabase.getCollection("tc_order").deleteMany(Filters.eq("sellerId",sellerId));
		mongoDatabase.getCollection("tc_sub_order").deleteMany(Filters.eq("sellerId",sellerId));
		mongoDatabase.getCollection("tc_order_seller_id_index").deleteMany(Filters.eq("sellerId",sellerId));
		mongoDatabase.getCollection("tc_order_refund").deleteMany(Filters.eq("sellerId",sellerId));
		mongoDatabase.getCollection("tc_order_receiver_district_list").deleteMany(Filters.eq("sellerId",sellerId));
		// ES
		DeleteByQueryRequest request = new DeleteByQueryRequest(getIndex()).setRouting(sellerId);
		request.setQuery(new TermQueryBuilder("sellerNick", sellerNick));
		request.setConflicts("proceed");
		try {
			BulkByScrollResponse response = restHighLevelClient.deleteByQuery(request, RequestOptions.DEFAULT);
		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	private String getIndex() {
		return AyTradeSearchES.INDEX_NAME_PREFIX + Math.abs(sellerId.hashCode() % 5);
	}

	private JdpTbTrade createJdpTbTrade(String status, String city, String logistics, boolean timingPromise) {
		String tid = TID + (autoInc++);
		return createJdpTbTrade(tid, status, city, logistics, timingPromise);
	}

	private JdpTbTrade createJdpTbTrade(String tid, String status, String city, String logistics, boolean timingPromise) {
		JdpTbTrade jdpTbTrade = new JdpTbTrade();
		jdpTbTrade.setSellerNick(sellerNick);
		jdpTbTrade.setTid(Long.parseLong(tid));
		jdpTbTrade.setStatus(status);
		LocalDateTime time = LocalDateTime.parse("2019-08-05 08:52:44", DateUtil.FORMATTER_DATETIME);
		jdpTbTrade.setCreated(time);
		jdpTbTrade.setModified(LocalDateTime.now().withNano(0));
		jdpTbTrade.setJdpModified(time);
		jdpTbTrade.setJdpCreated(time);
		jdpTbTrade.setBuyerNick("~HWUtlaTZs7TxpzJ5vDhBbw==~WzMsN4fW~1~~");
		jdpTbTrade.setJdpHashcode("571524377");
		jdpTbTrade.setType("fix");
		String buyerMessage = StringUtils.repeat(String.valueOf(Math.random() * 10), 2);
		jdpTbTrade.setJdpResponse(
			TRADE_FULLINFO_GET_RESPONSE.replace(TID, tid).replace("TAO_STATUS", status).replace(LOGISTICS, logistics)
				.replace(BEIJING, city).replace(TRADE_ATTR, timingPromise ? TIMING_ATTR : "")
				.replace(TIMING_PROMISE, timingPromise ? "tmallPromise" : "")
				.replace(PROMISE_SERVICE, timingPromise ? "tmallpromise.arrival.timing" : "")
				.replace(BUYER_MESSAGE, buyerMessage)
		);

		return jdpTbTrade;
	}

	private int insertJdpTbTrade(JdpTbTrade jdpTbTrade) {
		int result = pushRdsJdbcTemplate.update(
			"insert into jdp_tb_trade (`tid`, `status`, `type`, `seller_nick`, `buyer_nick`, `created`, `modified`, `jdp_hashcode`, `jdp_response`, `jdp_created`, `jdp_modified`)"
				+ " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", jdpTbTrade.getTid(), jdpTbTrade.getStatus(),
			jdpTbTrade.getType(), jdpTbTrade.getSellerNick(), jdpTbTrade.getBuyerNick(), jdpTbTrade.getCreated(),
			jdpTbTrade.getModified(), jdpTbTrade.getJdpHashcode(), jdpTbTrade.getJdpResponse(),
			jdpTbTrade.getJdpCreated(), jdpTbTrade.getJdpModified());

		LOGGER.logInfo("插入jdp_tb_trade " + result);
		return result;
	}

	private JdpTbTrade updateJdpTbTrade(long tid, String status, String city, String logistics, boolean timingPromise) {
		JdpTbTrade jdpTbTrade = createJdpTbTrade(String.valueOf(tid), status, city, logistics, timingPromise);
		int result = pushRdsJdbcTemplate
			.update("update jdp_tb_trade set `status`=?, `jdp_response`=?, `modified`=?, `jdp_modified`=?  where tid=?", jdpTbTrade.getStatus(),
				jdpTbTrade.getJdpResponse(), jdpTbTrade.getModified(), jdpTbTrade.getJdpModified(), jdpTbTrade.getTid());

		LOGGER.logInfo("更新jdp_tb_trade " + result);
		return jdpTbTrade;
	}

	private Pair<Long, Long> getMergeSubCount() {
		long mCount = mongoDatabase.getCollection("tc_order").countDocuments(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("isCombine", true)));
		long eCount = querySearch(Lists.newArrayList(QueryBuilders.termQuery("isCombine", true)), null).size();
		return Pair.of(mCount, eCount);
	}

	private Pair<Long, Long> getNotMergeCount() {
		long mCount = mongoDatabase.getCollection("tc_order").countDocuments(Filters.and(Filters.eq("sellerId", sellerId), Filters.ne("isCombine", true), Filters.eq("mergeTradeStatus", null)));
		long eCount = querySearch(null, Lists.newArrayList(QueryBuilders.existsQuery("isCombine"), QueryBuilders.existsQuery("mergeTradeStatus"))).size();
		return Pair.of(mCount, eCount);
	}

	private Pair<Long, Long> getMergeMainCount() {
		long mCount = mongoDatabase.getCollection("tc_order").countDocuments(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("mergeTradeStatus", 1)));
		long eCount = querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), Lists.newArrayList(QueryBuilders.existsQuery("isDeleted"))).size();
		return Pair.of(mCount, eCount);
	}

	private Pair<Boolean, Boolean> getCombineByTid(JdpTbTrade jdpTbTrade) {
		Boolean m = mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString()))).first().getBoolean("isCombine");
		Boolean e = (Boolean)querySearch(Lists.newArrayList(QueryBuilders.termQuery("tid", jdpTbTrade.getTid().toString())), null).get(0).get("isCombine");
		return Pair.of(m, e);
	}

	private Pair<String, String> getMd5ByTid(JdpTbTrade jdpTbTrade) {
		String m = mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", String.valueOf(jdpTbTrade.getTid())))).first().getString("mergeMd5");
		String e = (String)querySearch(Lists.newArrayList(QueryBuilders.termQuery("tid", jdpTbTrade.getTid().toString())), null).get(0).get("mergeMd5");
		return Pair.of(m, e);
	}

	private Pair<Boolean, Boolean> getIsPrintCourier(JdpTbTrade jdpTbTrade) {
		boolean m = mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString()))).first()
			.getBoolean("isPrintCourier");

		Boolean e = (Boolean)querySearch(Lists.newArrayList(QueryBuilders.termQuery("tid", jdpTbTrade.getTid().toString())), null).get(0).get("isPrintCourier");
		return Pair.of(m, e);
	}

	private Pair<Map<String, Object>, Map<String, Object>> getMergeMainStatus() {
		Map<String, Object> m = mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("mergeTradeStatus", 1))).first();
		Map<String, Object> e = querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).get(0);
		return Pair.of(ImmutableMap.of("ayStatus", m.get("ayStatus"), "taoStatus", m.get("taoStatus")), ImmutableMap.of("ayStatus", e.get("ayStatus"), "taoStatus", e.get("taoStatus")));
	}

	private Set<String> getLogisticsCompanyFromSearch() {
		return querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).stream().map(o->(Collection<String>)o.get("logisticsCompany")).flatMap(Collection::stream).collect(
			Collectors.toSet());
	}

	private Set<String> getMergeMainSellerMemo() {
		return querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).stream().map(o->(Collection<String>)o.get("sellerMemo")).flatMap(Collection::stream).collect(
			Collectors.toSet());
	}

	private Pair<List<Boolean>,List<Boolean>> getMergeMainPrint() {
		List<Boolean> m = Lists.newArrayList();
		mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("mergeTradeStatus", 1))).cursor().forEachRemaining(document -> {m.add(document.getBoolean("isPrintCourier"));});

		List<Boolean> e = querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).stream().map(o->(Boolean)o.get("isPrintCourier")).collect(
			Collectors.toList());
		return Pair.of(m, e);
	}

	private Pair<List<String>, List<String>> getMergeMainTaoStatus() {
		List<String> m = Lists.newArrayList();
		mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("mergeTradeStatus", 1))).cursor().forEachRemaining(document -> {m.add(document.getString("taoStatus"));});

		List<String> e = querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).stream().map(o->(String)o.get("taoStatus")).collect(
			Collectors.toList());
		return Pair.of(m, e);
	}

	private Pair<List<String>, List<String>> getMergeMainMd5() {
		List<String> m = Lists.newArrayList();
		mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("mergeTradeStatus", 1))).cursor().forEachRemaining(document -> {m.add(document.getString("mergeMd5"));});

		List<String> e = querySearch(Lists.newArrayList(QueryBuilders.termQuery("mergeTradeStatus", 1)), null).stream().map(o->(String)o.get("mergeMd5")).collect(
			Collectors.toList());
		return Pair.of(m, e);
	}

	private List<Map<String, Object>> querySearch(List<QueryBuilder> mustQuerys, List<QueryBuilder> mustNotQuerys) {
		try {
			BoolQueryBuilder query = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("sellerId", sellerId));
			if(mustQuerys != null) {
				for (QueryBuilder queryBuilder : mustQuerys) {
					query.must(queryBuilder);
				}
			}
			if(mustNotQuerys != null) {
				for (QueryBuilder queryBuilder : mustNotQuerys) {
					query.mustNot(queryBuilder);
				}
			}

			SearchSourceBuilder source = new SearchSourceBuilder();
			source.size(20);
			source.sort(SortBuilders.fieldSort("created").order(SortOrder.DESC));
			source.query(query);

			SearchRequest request = new SearchRequest();
			request.indices(getIndex());
			request.routing(sellerId);
			request.source(source);

			SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
			return Arrays.stream(response.getHits().getHits()).map(hit->hit.getSourceAsMap()).collect(Collectors.toList());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return Lists.newArrayList();
	}

	private void updateEs(JdpTbTrade jdpTbTrade, Map<String, Object> map) throws Exception{
		UpdateRequest updateRequest = new UpdateRequest(getIndex(), CommonPlatformConstants.PLATFORM_TAO + jdpTbTrade.getTid());
		updateRequest.routing(sellerId);
		updateRequest.docAsUpsert(false);
		updateRequest.doc(map, XContentType.JSON);
		restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
	}

	private void updatePrint(JdpTbTrade jdpTbTrade, boolean print) throws Exception {
		UpdateResult result = mongoDatabase.getCollection("tc_order").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("isPrintCourier", print));

		result = mongoDatabase.getCollection("tc_order_seller_id_index").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("isPrintCourier", print));

		updateEs(jdpTbTrade, ImmutableMap.of("isPrintCourier", print));
	}

	private void updateIsLocked(JdpTbTrade jdpTbTrade, boolean isLocked) throws Exception{
		mongoDatabase.getCollection("tc_order").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("isLocked", isLocked));
		mongoDatabase.getCollection("tc_order_seller_id_index").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("isLocked", isLocked));
		updateEs(jdpTbTrade, ImmutableMap.of("isLocked", isLocked));
	}

	private void updateSendGoos(JdpTbTrade jdpTbTrade, String taoStatus, int ayStatus) throws Exception {
		mongoDatabase.getCollection("tc_order").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())),
			Updates.combine(Updates.set("taoStatus", taoStatus), Updates.set("ayStatus", ayStatus)));
		mongoDatabase.getCollection("tc_order_seller_id_index").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())),
			Updates.combine(Updates.set("taoStatus", taoStatus), Updates.set("ayStatus", ayStatus)));
		updateEs(jdpTbTrade, ImmutableMap.of("taoStatus", taoStatus, "ayStatus", ayStatus));
	}

	private void updateReceiver(JdpTbTrade jdpTbTrade, String mergeMd5) throws Exception {
		mongoDatabase.getCollection("tc_order").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("mergeMd5", mergeMd5));
		mongoDatabase.getCollection("tc_order_seller_id_index").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())), Updates.set("mergeMd5", mergeMd5));
		updateEs(jdpTbTrade, ImmutableMap.of("mergeMd5", mergeMd5));
	}

	private void updateSplit(JdpTbTrade jdpTbTrade) throws Exception {
		mongoDatabase.getCollection("tc_order").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())),
			Updates.combine(Updates.set("mergeTid", null), Updates.set("mergeAyTid", null), Updates.set("mergeTradeStatus", null), Updates.set("isCombine", null), Updates.set("isSplit", true)));
		mongoDatabase.getCollection("tc_order_seller_id_index").updateMany(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", jdpTbTrade.getTid().toString())),
			Updates.combine(Updates.set("mergeTid", null), Updates.set("mergeAyTid", null), Updates.set("mergeTradeStatus", null), Updates.set("isCombine", null), Updates.set("isSplit", true)));
		Map<String, Object> map = Maps.newHashMap();
		map.put("mergeTid", null);
		map.put("mergeAyTid", null);
		map.put("mergeTradeStatus", null);
		map.put("isSplit", true);
		updateEs(jdpTbTrade, map);
	}

	private Document getMongoByTid(JdpTbTrade jdpTbTrade) throws Exception {
		return mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", String.valueOf(jdpTbTrade.getTid())))).first();
	}

	private String getMergeTidByTid(JdpTbTrade jdpTbTrade) throws Exception {
		return mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", String.valueOf(jdpTbTrade.getTid())))).first()
			.getString("mergeTid");
	}

	private String getSellerMemoByTid(JdpTbTrade jdpTbTrade) {
		return mongoDatabase.getCollection("tc_order").find(Filters.and(Filters.eq("sellerId", sellerId), Filters.eq("tid", String.valueOf(jdpTbTrade.getTid())))).first()
			.getString("sellerMemo");
	}

	private JdpTbTrade insertAndPushJdpTbTrade(String status, String city, String logistics) throws Exception {
		return insertAndPushJdpTbTrade(status, city, logistics, false);
	}

	private JdpTbTrade insertAndPushJdpTbTrade(String status, String city, String logistics, boolean timingPromise) throws Exception {
		JdpTbTrade jdpTbTrade = createJdpTbTrade(status, city, logistics, timingPromise);
		insertJdpTbTrade(jdpTbTrade);
		pushRds(jdpTbTrade);
		return jdpTbTrade;
	}

	private JdpTbTrade updateAndPushJdpTbTrade(long tid, String status, String city, String logistics)
		throws Exception {
		return updateAndPushJdpTbTrade(tid, status, city, logistics, false);
	}

	private JdpTbTrade updateAndPushJdpTbTrade(long tid, String status, String city, String logistics, boolean timingPromise)
		throws Exception {
		JdpTbTrade jdpTbTrade = updateJdpTbTrade(tid, status, city, logistics, timingPromise);
		pushRds(jdpTbTrade);
		return jdpTbTrade;
	}

	private String pushRds(JdpTbTrade jdpTbTrade) throws Exception {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("modified", jdpTbTrade.getModified());
		jsonObject.put("sellerNick", jdpTbTrade.getSellerNick());
		jsonObject.put("tid", jdpTbTrade.getTid().toString());

		Message message = new Message(TOPIC, JSON.toJSONString(jsonObject).getBytes("utf-8"));
		String msgId = saveOrderProducer.send(message).getMsgId();
		LOGGER.logInfo("发送消息: " + TOPIC + " " + msgId);
		TimeUnit.SECONDS.sleep(20);
		return msgId;
	}

	private String pushTmc(long tid, String sellerMemo) throws Exception {
		String json = TMC_REQUEST.replace(TID, String.valueOf(tid)).replace(SELLER_MEMO, sellerMemo);
		Message message = new Message(TMC_TOPIC, json.getBytes("utf-8"));
		String msgId = saveOrderProducer.send(message).getMsgId();
		LOGGER.logInfo("发送消息: " + TMC_TOPIC + " " + msgId);
		TimeUnit.SECONDS.sleep(20);
		return msgId;
	}

	private void http(String controller, long tid, List<Long> tids) throws Exception {
		http(controller, tid, tids, null, null, null);
	}

	private void http(String controller, long tid, List<Long> tids, String newMd5, String oldMd5) throws Exception {
		http(controller, tid, tids, newMd5, oldMd5, null);
	}

	private void http(String controller, long tid, List<Long> tids, String mTid) throws Exception {
		http(controller, tid, tids, null, null, mTid);
	}

	private void http(String controller, long tid, List<Long> tids, String newMd5, String oldMd5, String mTid)
		throws Exception {
		LOGGER.logInfo("http 请求: " + controller);
		HashMap<String, String> params = Maps.newHashMap();
		params.put("sellerId", sellerId);
		params.put("tid", String.valueOf(tid));
		params.put("newMd5", newMd5);
		params.put("oldMd5", oldMd5);
		params.put("mTid", mTid);
		for (int i = 0; i < tids.size(); i++) {
			params.put("tids[" + i + "]", String.valueOf(tids.get(i)));
		}
		NetworkUtil.http("http://127.0.0.1:8080/test/" + controller, params, true, null, null, false, false, null);

		TimeUnit.SECONDS.sleep(10);
	}

	private <T> Pair<T,T> pair(T value){
		return Pair.of(value, value);
	}

	private <T> Pair<List<T>, List<T>> pairList(T ...value){
		List<T> list = Lists.newArrayList(value);
		return Pair.of(list, list);
	}

	private Pair<Map<String, Object>, Map<String, Object>> status(Integer ayStatus, String taoStatus){
		Map<String, Object> map = ImmutableMap.of("ayStatus", ayStatus, "taoStatus",  taoStatus);
		return Pair.of(map, map);
	}

	private Pair<Map<String, Object>, Map<String, Object>> status(List<Integer> ayStatus, List<String> taoStatus){
		Map<String, Object> map = ImmutableMap.of("ayStatus", ayStatus, "taoStatus",  taoStatus);
		return Pair.of(map, map);
	}

	/**
	 * receiverState 变更为空白
	 * @throws Exception
	 */
	@Test
	public void rdsChangeTest1() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(BEIJING, getMongoByTid(jdpTbTrade).getString("receiverState"));

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, StringUtils.EMPTY,
			LOGISTICS);

		Assert.assertEquals(StringUtils.EMPTY, getMongoByTid(jdpTbTrade).getString("receiverState"));
	}

	/**
	 * 新的待发货, 不会与已打印的待发货订单合单
	 */
	@Test
	public void rdsPushTest1() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		LOGGER.logInfo("设置订单已打印");
		updatePrint(jdpTbTrade, true);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
	}

	/**
	 * 测试合单信息刷新
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest1_2() throws Exception {
		String tid = TID + (autoInc++);
		JdpTbTrade jdpTbTrade =createJdpTbTrade(tid, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS, false);
		insertJdpTbTrade(jdpTbTrade);
		pushRds(jdpTbTrade);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		tid = TID + 0;
		jdpTbTrade =createJdpTbTrade(tid, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS, false);
		int i =pushRdsJdbcTemplate.update(
			"update jdp_tb_trade set modified = ? where tid=?", jdpTbTrade.getModified(), tid);
		pushRds(jdpTbTrade);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L),getMergeSubCount());
	}

	/**
	 * 新的待发货, 已打印, 不会其他待发货订单合单
	 */
	@Test
	public void rdsPushTest2() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		LOGGER.logInfo("设置订单已打印");
		updatePrint(jdpTbTrade, true);

		jdpTbTrade = updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING,
			LOGISTICS);

		Assert.assertEquals(pair(true), getIsPrintCourier(jdpTbTrade));

		Assert.assertEquals(pair(2L), getNotMergeCount());
		Assert.assertEquals(pair(0L), getMergeMainCount());
	}

	/**
	 * 新的待发货, 不会与部分发货的订单合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest3() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(2L), getNotMergeCount());
		Assert.assertEquals(pair(0L), getMergeMainCount());
	}

	/**
	 * 新的部分发货, 不会与待发货的订单合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest3_2() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(2L), getNotMergeCount());
		Assert.assertEquals(pair(0L), getMergeMainCount());
	}

	/**
	 * 新的待发货, 不会合并到部分发货的合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest4() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING,
			LOGISTICS);

		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade3));
	}

	/**
	 * 新的待发货, 与其他未合单待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest5() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		Set<String> searchResult = getLogisticsCompanyFromSearch();
		Assert.assertEquals(2, searchResult.size());

	}

	/**
	 * 新的待发货, 与其他未合单待发货合单, (有空格与没有空格的合单)
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest5_2() throws Exception {
		String originalString = TRADE_FULLINFO_GET_RESPONSE;
		TRADE_FULLINFO_GET_RESPONSE = originalString.replaceAll("望京街道方舟苑小区5号楼1202", "望京街 道方舟苑小区5号  楼1202");
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		TRADE_FULLINFO_GET_RESPONSE = originalString;
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		Set<String> searchResult = getLogisticsCompanyFromSearch();
		Assert.assertEquals(2, searchResult.size());

	}

	/**
	 * 新的待发货, 与其他未合单待发货合单, (空白城市合单)
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest5_3() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, StringUtils.EMPTY, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, StringUtils.EMPTY, LOGISTICS + 1);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		Set<String> searchResult = getLogisticsCompanyFromSearch();
		Assert.assertEquals(2, searchResult.size());

	}

	/**
	 * 新的待发货, 与已合单待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest6() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 2);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(3L), getMergeSubCount());

		Assert.assertEquals(3, getLogisticsCompanyFromSearch().size());
	}

	/**
	 * 老的待发货, 未合单, 修改收货地址, 与其他未合单待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest7() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

	}

	/**
	 * 老的待发货, 未合单, 修改收货地址, 与已合单待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest8() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(3L), getMergeSubCount());
	}

	/**
	 * 老的待发货, 未合单, 修改收货地址, 不会合并到部分发货的合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest9() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
	}

	/**
	 * 新的待发货, 与其他未合单待发货的锁定订单不合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest9_1() throws Exception {
		JdpTbTrade jdpTbTrade1 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateIsLocked(jdpTbTrade1,true);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		Assert.assertEquals(pair(0L), getMergeMainCount());
	}

	/**
	 * 待发货合单, 部分发货, 变为部分发货
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest10() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.SELLER_CONSIGNED_PART), getMergeMainStatus());
	}

	/**
	 * 待发货合单, 发货, 变为部分发货
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest11() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.SELLER_CONSIGNED_PART), getMergeMainStatus());
	}

	/**
	 * 待发货合单,全部发货, 变为已发货
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest12() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS), getMergeMainStatus());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);
		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING,
			LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(status(AyStatusConstant.SELLER_GOODS_SENDED, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS), getMergeMainStatus());

	}

	/**
	 * 3个子单的待发货合单, 子单地址变更, 自动拆出, 原合单更新
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest13() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 2);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(3L), getMergeSubCount());

		Set<String> logistics = getLogisticsCompanyFromSearch();

		Assert.assertEquals(3, logistics.size());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, GUANGZHOU,
			LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));

		logistics = getLogisticsCompanyFromSearch();

		Assert.assertEquals(2, logistics.size());

	}

	/**
	 * 2个子单的待发货合单, 子单地址变更, 自动拆出, 原合单取消
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest14() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, GUANGZHOU,
			LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());

		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));

	}

	/**
	 * 待发货合单, 子单地址变更, 自动拆出, 与其他待发货订单合单
	 *
	 * @throws Exception
	 */
	@Test
	public void
	rdsPushTest15() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS + 2);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade2));

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade3));
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));
	}

	/**
	 * 待发货合单, 子单地址变更, 自动拆出, 与现有待发货合单合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest16() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS + 2);

		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS + 3);

		Assert.assertEquals(pair(2L), getMergeMainCount());
		Assert.assertEquals(pair(4L), getMergeSubCount());

		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade2));

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(3L), getMergeSubCount());

		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade3));
		Assert.assertEquals(pair(true), getCombineByTid(jdpTbTrade4));
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));

	}

	/**
	 * 新的发货, 未合单, 仅锁定合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest17() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());

		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
	}

	/**
	 * 2个已发货不合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest18() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 2个部分发货不合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest19() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);
		insertAndPushJdpTbTrade(TaobaoStatusConstant.SELLER_CONSIGNED_PART, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 非待发货订单, 未合单订单, 无操作
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest20() throws Exception {
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);
		insertAndPushJdpTbTrade(TaobaoStatusConstant.TRADE_FINISHED, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 非待发货订单, 已合单, 更新合单信息
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest21() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);
		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.TRADE_FINISHED, BEIJING, LOGISTICS);

		Assert.assertEquals(status(AyStatusConstant.SELLER_GOODS_SENDED, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS), getMergeMainStatus());

		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.TRADE_FINISHED, BEIJING, LOGISTICS + 2);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());

		Set<String> logistics = getLogisticsCompanyFromSearch();;
		Assert.assertTrue(logistics.contains(LOGISTICS));
		Assert.assertTrue(logistics.contains(LOGISTICS + 2));
	}

	/**
	 * 非待发货订单, 已合单, 更新合单信息
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest22() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, BEIJING, LOGISTICS);
		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.TRADE_FINISHED, BEIJING, LOGISTICS);

		Assert.assertEquals(status(AyStatusConstant.SELLER_GOODS_SENDED, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS), getMergeMainStatus());

		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.TRADE_FINISHED, GUANGZHOU, LOGISTICS + 2);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));
	}

	/**
	 * 已拆单的待发货不再合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest23() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSplit(jdpTbTrade);
		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 新的待发货, 不会与其他未合时效订单待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest24() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS, true);

		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS + 1);

		Assert.assertEquals(pair(0L), getMergeMainCount());

		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 新的待发货时效订单, 不会与其他待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest25() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS + 1, true);

		Assert.assertEquals(pair(0L), getMergeMainCount());

		Assert.assertEquals(pair(0L), getMergeSubCount());

	}

	/**
	 * 已合单订单, 关闭订单自动拆出
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest26() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade3 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(3L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.TRADE_CLOSED_BY_TAOBAO, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(2L), getMergeSubCount());

	}

	/**
	 * 已合单订单, 关闭订单自动拆出, 刷新原合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest27() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade3 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(3L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.TRADE_CLOSED, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(2L), getMergeSubCount());

	}


	/**
	 * PAID_FORBID_CONSIGN的订单, 不合单, 变为 WAIT_SELLER_SEND_GOODS 后再合单
	 *
	 * @throws Exception
	 */
	@Test
	public void rdsPushTest28() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.PAID_FORBID_CONSIGN, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade2 = insertAndPushJdpTbTrade(TaobaoStatusConstant.PAID_FORBID_CONSIGN, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(0L), getMergeMainCount());

		Assert.assertEquals(pair(0L), getMergeSubCount());

		updateAndPushJdpTbTrade(jdpTbTrade.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateAndPushJdpTbTrade(jdpTbTrade2.getTid(), TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		Assert.assertEquals(pair(1L), getMergeMainCount());

		Assert.assertEquals(pair(2L), getMergeSubCount());
	}

	/**
	 * 非待发货订单, 未合单订单, 无操作
	 *
	 * @throws Exception
	 */
	@Test
	public void tmcPushTest() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);

		pushTmc(jdpTbTrade.getTid(), SELLER_MEMO + 1);

		Assert.assertEquals(SELLER_MEMO + 1, getSellerMemoByTid(jdpTbTrade));
		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));
	}

	/**
	 * 已合单, 更新合单信息
	 *
	 * @throws Exception
	 */
	@Test
	public void tmcPushTest2() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		pushTmc(jdpTbTrade.getTid(), SELLER_MEMO + 1);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertTrue(getMergeMainSellerMemo().contains(ElasticsearchUtil.splitAlphanumeric(SELLER_MEMO + 1)));
	}

	/**
	 * 非待发货订单, 未合单订单, 无操作
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest() throws Exception {
		JdpTbTrade jdpTbTrade = insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_BUYER_PAY, BEIJING, LOGISTICS);

		http("print", jdpTbTrade.getTid(), Collections.singletonList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 非待发货订单, 未合单订单, 无操作
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest2() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		http("print", jdpTbTrade.getTid(), Collections.singletonList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 已合单, 打印, 没有缺少tid,更新合单打印
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest3() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);
		updatePrint(jdpTbTrade2, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pairList(true), getMergeMainPrint());
	}

	/**
	 * 已合单, 打印一个, 合单打印状态应该为false
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest4() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pairList(null), getMergeMainPrint());
	}

	/**
	 * 2个已合单, 打印, 只有一个tid, 取消合单
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest5() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 3个已合单, 打印, 缺少一个tid, 拆出多余的tid, 更新原合单打印
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest6() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);
		updatePrint(jdpTbTrade2, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		TimeUnit.SECONDS.sleep(5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade3));
		Assert.assertEquals(pairList(true), getMergeMainPrint());
	}

	/**
	 * 3个已合单, 打印, 只有一个tid, 自动拆出这个tid, 不更新原合单打印
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest7() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pairList((Boolean)null), getMergeMainPrint());
	}

	/**
	 * 4个已合单, 打印, 缺少2个tid, 拆出多余的2个tid组成新的合单, 更新原合单打印
	 *
	 * @throws Exception
	 */
	@Test
	public void printTest8() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updatePrint(jdpTbTrade, true);
		updatePrint(jdpTbTrade2, true);

		http("print", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(2L), getMergeMainCount());
		Assert.assertEquals(pair(4L), getMergeSubCount());
		Assert.assertEquals(pairList(true, null), getMergeMainPrint());
	}

	/**
	 * 已合单, 发货, 没有缺少tid,更新合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);
		updateSendGoos(jdpTbTrade2, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS,
			AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(status(AyStatusConstant.SELLER_GOODS_SENDED, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS),  getMergeMainTaoStatus());
	}

	/**
	 * 已合单, 发货, 没有缺少tid, 单笔发货, 更新合单状态为部分发货
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest2() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.SELLER_CONSIGNED_PART), getMergeMainTaoStatus());
	}

	/**
	 * 已合单, 发货, 没有缺少tid, 单笔部分发货, 更新合单状态为部分发货
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest3() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);


		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.SELLER_CONSIGNED_PART, AyStatusConstant.WAIT_SELLER_SEND_GOODS);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.SELLER_CONSIGNED_PART), getMergeMainTaoStatus());
	}

	/**
	 * 2个已合单, 发货, 只有一个tid, 取消合单
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest4() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 3个已合单, 发货, 缺少一个tid, 拆出多余的tid, 更新原合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest5() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);
		updateSendGoos(jdpTbTrade2, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS,
			AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		TimeUnit.SECONDS.sleep(5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade3));
		Assert.assertEquals(status(AyStatusConstant.SELLER_GOODS_SENDED, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS), getMergeMainTaoStatus());
	}

	/**
	 * 3个已合单, 发货, 只有一个tid, 自动拆出这个tid, 不更新原合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest6() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()));

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(status(AyStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS) , getMergeMainTaoStatus());
	}

	/**
	 * 4个已合单, 发货, 缺少2个tid, 拆出多余的2个tid组成新的合单, 更新原合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void sendGoodTest7() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSendGoos(jdpTbTrade, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS, AyStatusConstant.SELLER_GOODS_SENDED);
		updateSendGoos(jdpTbTrade2, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS,
			AyStatusConstant.SELLER_GOODS_SENDED);

		http("sendGoods", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()));

		Assert.assertEquals(pair(2L), getMergeMainCount());
		Assert.assertEquals(pair(4L), getMergeSubCount());
		Assert.assertEquals(status(Lists.newArrayList(AyStatusConstant.WAIT_SELLER_SEND_GOODS, AyStatusConstant.SELLER_GOODS_SENDED),
			Lists.newArrayList(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS)), getMergeMainTaoStatus());
	}

	/**
	 * 未合单, 修改收货信息, 与其他待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()), BEIJING_MERGE_MD5,
			GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pairList(GUANGZHOU_MERGE_MD5), getMergeMainMd5());
	}

	/**
	 * 已合单, 修改收货信息, 没有缺少tid,更新合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest2() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);
		updateReceiver(jdpTbTrade2, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()),
			BEIJING_MERGE_MD5, GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pairList(GUANGZHOU_MERGE_MD5), getMergeMainMd5());
	}

	/**
	 * 2个已合单, 修改收货信息, 只有一个tid, 取消合单
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest3() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()), BEIJING_MERGE_MD5,
			GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 2个已合单, 修改收货信息, 只有一个tid, 取消合单, 与新的待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest4() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);
		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()), BEIJING_MERGE_MD5,
			GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pairList(GUANGZHOU_MERGE_MD5), getMergeMainMd5());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade2));
	}

	/**
	 * 3个已合单, 修改收货信息, 缺少一个tid, 拆出多余的tid, 更新原合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest5() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);
		updateReceiver(jdpTbTrade2, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()),
			BEIJING_MERGE_MD5, GUANGZHOU_MERGE_MD5);

		TimeUnit.SECONDS.sleep(5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade3));
		Assert.assertEquals(pairList(GUANGZHOU_MERGE_MD5), getMergeMainMd5());
	}

	/**
	 * 3个已合单, 修改收货信息, 只有一个tid, 自动拆出这个tid, 不更新原合单状态, 与新的待发货合单
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest6() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid()), BEIJING_MERGE_MD5,
			GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
		Assert.assertEquals(pair(null), getCombineByTid(jdpTbTrade));
		Assert.assertEquals(pairList(BEIJING_MERGE_MD5), getMergeMainMd5());
	}

	/**
	 * 4个已合单, 修改收货信息, 缺少2个tid, 拆出多余的2个tid组成新的合单, 更新原合单状态
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest7() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);
		updateReceiver(jdpTbTrade2, GUANGZHOU_MERGE_MD5);

		http("receiver", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()),
			BEIJING_MERGE_MD5, GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(2L), getMergeMainCount());
		Assert.assertEquals(pair(4L), getMergeSubCount());
		Assert.assertEquals(pairList(GUANGZHOU_MERGE_MD5, BEIJING_MERGE_MD5), getMergeMainMd5());
	}

	/**
	 * 已拆单的待发货不再合单
	 *
	 * @throws Exception
	 */
	@Test
	public void receiverTest8() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		updateSplit(jdpTbTrade);
		updateReceiver(jdpTbTrade, GUANGZHOU_MERGE_MD5);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 已拆单的待发货不再合单
	 *
	 * @throws Exception
	 */
	@Test
	public void invalidMergeTest8() throws Exception {
		JdpTbTrade jdpTbTrade1 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade5 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);

		JdpTbTrade jdpTbTrade6 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, GUANGZHOU, LOGISTICS);


		Thread.sleep(30000);

		Assert.assertEquals(pair(2L), getMergeMainCount());
		Assert.assertEquals(pair(4L), getMergeSubCount());
		Assert.assertEquals(2, getNotMergeCount());
	}

	/**
	 * 3个已合单, 全部拆单
	 *
	 * @throws Exception
	 */
	@Test
	public void splitTest() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		String mTid = getMergeTidByTid(jdpTbTrade);

		updateSplit(jdpTbTrade);
		updateSplit(jdpTbTrade2);
		updateSplit(jdpTbTrade3);

		http("split", jdpTbTrade.getTid(),
			Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid(), jdpTbTrade3.getTid()), mTid);

		Assert.assertEquals(pair(0L), getMergeMainCount());
		Assert.assertEquals(pair(0L), getMergeSubCount());
	}

	/**
	 * 4个已合单, 两个拆单, 剩余两个保留原合单
	 *
	 * @throws Exception
	 */
	@Test
	public void splitTest2() throws Exception {
		JdpTbTrade jdpTbTrade =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade2 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		JdpTbTrade jdpTbTrade3 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);
		JdpTbTrade jdpTbTrade4 =
			insertAndPushJdpTbTrade(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS, BEIJING, LOGISTICS);

		String mTid = getMergeTidByTid(jdpTbTrade);

		updateSplit(jdpTbTrade);
		updateSplit(jdpTbTrade2);

		http("split", jdpTbTrade.getTid(), Lists.newArrayList(jdpTbTrade.getTid(), jdpTbTrade2.getTid()), mTid);

		Assert.assertEquals(pair(1L), getMergeMainCount());
		Assert.assertEquals(pair(2L), getMergeSubCount());
	}
}
