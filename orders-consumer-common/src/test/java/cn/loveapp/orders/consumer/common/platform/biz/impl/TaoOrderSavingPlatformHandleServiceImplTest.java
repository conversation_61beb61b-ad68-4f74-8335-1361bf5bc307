package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.dao.trade.AyStatusCodeConfigDao;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.platform.biz.OrderMergePlatformHandleService;
import cn.loveapp.orders.common.service.impl.AyStatusCodeConfigServiceImpl;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = { MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, AyStatusCodeConfigServiceImpl.class,
		TaoOrderSavingPlatformHandleServiceImpl.class
	})
public class TaoOrderSavingPlatformHandleServiceImplTest {
	@Autowired
	private TaoOrderSavingPlatformHandleServiceImpl savingPlatformHandleService;

	@MockBean
	protected OrderMergePlatformHandleService orderMergePlatformHandleService;

	@MockBean
	protected AyStatusCodeConfigDao ayStatusCodeConfigDao;

	@Test
	public void appendToTradeMain() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		AyTrade trade = new AyTrade();
		trade.setOaid("oaid");

		savingPlatformHandleService.appendToTradeMain(ayTradeMain, null, null, null, trade, null, null);

		assertEquals("oaid", ayTradeMain.getOaid());
	}
}
