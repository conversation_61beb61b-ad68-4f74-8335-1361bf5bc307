package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.constant.TimingPromiseConstant;
import cn.loveapp.orders.common.dao.es.CommonAyTradeSearchESDao;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.MathUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.TradeHandleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.api.domain.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = {TradeSearchESHandleServiceImpl.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class})
public class TradeSearchESHandleServiceImplTest {

	@SpyBean
	private TradeSearchESHandleServiceImpl eSHandleService;

	@MockBean
	private CommonAyTradeSearchESDao ayTradeSearchDao;

	@MockBean
	private AyStatusCodeConfigService statusCodeConfigService;

	@MockBean
	private TradeHandleService tradeHandleService;

	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@Before
	public void setup(){
		when(orderSavingPlatformHandleService.generateMergeMd5(any(TradeBo.class), any(), any(), any())).thenReturn("MD5");
	}

	@Test
	public void handle1() {
		Random random = new Random();
		int ayStatus = random.nextInt(9);
		when(statusCodeConfigService.getPlatformStatusAyStatus(anyString())).thenReturn(String.valueOf(ayStatus));

		int tag = random.nextInt(6);
		when(tradeHandleService.tradeStatusConvertTag(anyString(), anyInt())).thenReturn(tag);

		String sellerId = "sellerId";
		String sellerNick = "sellerNick";
		String buyerNick = "buyerNick";
		String corpId = "corpId";
		String tid = "tid";
		String ayTid = "ayTid";
		String storeId = "storeId";
		String receiverName = "receiverName";
		String receiverPhone = "receiverPhone";
		String receiverMobile = "receiverMobile";
		String receiverAddress = "receiverAddress";
		Date created = new Date();

		OriginDecryptData originDecryptData = new OriginDecryptData();
		originDecryptData.setBuyerNick(buyerNick);
		originDecryptData.setReceiverName(receiverName);
		originDecryptData.setReceiverPhone(receiverPhone);
		originDecryptData.setReceiverMobile(receiverMobile);
		originDecryptData.setReceiverAddress(receiverAddress);

		Trade trade = createTrade();
		trade.setSellerNick(sellerNick);
		trade.setTidStr(tid);
		trade.setBuyerNick(buyerNick+1);
		trade.setReceiverName(receiverName+1);
		trade.setReceiverPhone(receiverPhone+1);
		trade.setReceiverMobile(receiverMobile+1);
		trade.setReceiverAddress(receiverAddress+1);

		AyTrade ayTrade = new AyTrade(trade);

		TradeBo tradeBo = new TradeBo();
		tradeBo.setSellerId(sellerId);
		tradeBo.setTid(tid);
		tradeBo.setSellerId(sellerId);
		tradeBo.setStoreId(storeId);
		tradeBo.setOriginDecryptData(originDecryptData);
		tradeBo.setTrade(ayTrade);
		tradeBo.setMergeMd5("MD5");

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(tag);
		tradeHandleBo.setCorpId(corpId);
		tradeHandleBo.setSellerId(sellerId);
		tradeHandleBo.setListId(991);
		tradeHandleBo.setSellerNick(sellerNick);
		tradeHandleBo.setTid(tid);
		tradeHandleBo.setCreated(created);
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setTrade(ayTrade);

		doAnswer(
			invocation -> {
				Object[] args = invocation.getArguments();
				AyTradeSearchES es = ((AyTradeSearchES)args[2]);
				es.setReceiverName(receiverName);
				es.setReceiverMobile(receiverMobile);
				es.setReceiverPhone(receiverPhone);
				es.setReceiver(
					OrderUtil.generateReceiverForSearch(receiverPhone, receiverMobile, receiverAddress,
						trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(),
						trade.getReceiverCountry(), trade.getReceiverTown(), receiverName)
				);
				return null;
			}
		).when(orderSavingPlatformHandleService).appendToTradeSearch(any(), any(), any(), any(), any(), any(), any());

		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);

		AyTradeSearchES es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);

		Assert.assertEquals(ayStatus, es.getAyOrderType().get(0).intValue());
		Assert.assertEquals(ayStatus, es.getAyStatus().intValue());
		Assert.assertEquals(ayTid, es.getAyTid());
		Assert.assertEquals(ayTrade.getBuyerRate(), es.getBuyerRate());
		Assert.assertEquals(ayTrade.getBuyerMessage(), es.getBuyerMessage().get(0));
		Assert.assertEquals(buyerNick, es.getBuyerNick());
		Assert.assertEquals(corpId, es.getCorpId());
		Assert.assertEquals(ayTrade.getConsignTime(), es.getConsignTime());
		Assert.assertEquals(ayTrade.getCreated(), es.getCreated());
		Assert.assertEquals(ayTrade.getDeliveryTime(), DateUtil.DATE_PARSER.format(es.getDeliveryTime()));
		Assert.assertEquals(ayTrade.getEndTime(), es.getEndTime());
		Assert.assertNull(es.getErrorStatus());
		Assert.assertEquals(ayTrade.getEsTime(), es.getEsTime().toString());
		Assert.assertNull(es.getGmtCreate());
		Assert.assertNull(es.getGmtModified());
		Assert.assertEquals(ayTrade.getOrders().stream().mapToLong(Order::getNum).sum(), es.getGoodsNum().longValue());
		Assert.assertEquals(storeId + tid, es.getId());
		Assert.assertNull(es.getIsCombine());
		Assert.assertNull(es.getIsError());
		Assert.assertNull(es.getIsManual());
		Assert.assertNull(es.getIsSplit());
//		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getInvoiceNo).collect(Collectors.toSet()),
//			Sets.newHashSet(es.getInvoiceNo()));
		Assert.assertEquals(ayTrade.getIsO2oPassport(), es.getIsO2oPassport());
		Assert.assertNull(es.getIsPrintCourier());
		Assert.assertNull(es.getIsPrintItemizedstmt());
		Assert.assertNull(es.getIsPrintWaybill());
		Assert.assertNull(es.getIsCreditPay());
		Assert.assertNull(es.getIsEditItem());
		Assert.assertNull(es.getIsLocked());
		Assert.assertNull(es.getIsModified());
//		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getLogisticsCompany).collect(Collectors.toSet()),
//			Sets.newHashSet(es.getLogisticsCompany()));
		Assert.assertNull(es.getMergeAyTid());
		Assert.assertNull(es.getMergeTid());
		Assert.assertNull(es.getMergeTradeStatus());
		Assert.assertEquals(ayTrade.getModified(), es.getModified());
		Assert.assertEquals(ayTrade.getNewPresell(), es.getNewPresell());
		Assert.assertEquals(
			ayTrade.getOrders().stream().map(Order::getNumIid).map(MathUtil::parseLong).collect(Collectors.toSet()),
			Sets.newHashSet(es.getNumIid()));
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getOidStr).collect(Collectors.toSet()),
			Sets.newHashSet(es.getOid()));
		Assert.assertEquals(tag, es.getOrderTag().intValue());
		Assert.assertEquals(ayTrade.getType(), es.getOrderType().get(0));
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getOuterIid).collect(Collectors.toSet()),
			Sets.newHashSet(es.getOuterIid()));
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getOuterSkuId).collect(Collectors.toSet()),
			Sets.newHashSet(es.getOuterSkuId()));
		Assert.assertEquals(NumberUtils.toDouble(ayTrade.getPayment()), es.getPayment().doubleValue(), 100);
		Assert.assertEquals(ayTrade.getPayTime(), es.getPayTime());
		Assert.assertNull(es.getRefundTime());

		String recevier = StringUtils
			.joinWith("|", originDecryptData.getReceiverPhone(), originDecryptData.getReceiverMobile(), originDecryptData.getReceiverAddress(),
				ayTrade.getReceiverState(), ayTrade.getReceiverCity(), ayTrade.getReceiverDistrict(),
				ayTrade.getReceiverCountry(), ayTrade.getReceiverTown(), originDecryptData.getReceiverName());
		Assert.assertEquals(originDecryptData.getReceiverName(), es.getReceiverName());
		Assert.assertEquals(originDecryptData.getReceiverMobile(), es.getReceiverMobile());
		Assert.assertEquals(originDecryptData.getReceiverPhone(), es.getReceiverPhone());

		Assert.assertEquals(recevier, es.getReceiver());
		Assert.assertEquals(sellerNick, es.getSellerNick());
		Assert.assertEquals(sellerId, es.getSellerId());
		Assert.assertEquals(storeId, es.getStoreId());
		Assert.assertEquals(ayTrade.getSellerRate(), es.getSellerRate());
        Assert.assertTrue(es.getSkuNum().contains(ayTrade.getOrders().size()));
		Assert.assertEquals(ayTrade.getSellerFlag().intValue(), es.getSellerFlag().get(0).intValue());
		Assert.assertEquals(ayTrade.getSellerMemo(), es.getSellerMemo().get(0));
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getSkuPropertiesName).collect(Collectors.toSet()),
			Sets.newHashSet(es.getSkuName()));
		Assert.assertEquals(ayTrade.getStepTradeStatus(), es.getStepTradeStatus().get(0));
		Assert.assertEquals(ayTrade.getTidStr(), es.getTid());
		Assert.assertEquals(ayTrade.getStatus(), es.getTaoStatus());
		Assert.assertEquals(ayTrade.getTimingPromise(), es.getTimingPromise());
		Assert.assertEquals(ayTrade.getTimeoutActionTime(), es.getTimeoutActionTime());
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getTitle).collect(Collectors.toSet()),
			Sets.newHashSet(es.getTitle()));

		Assert.assertTrue(es.getIsInvoice());
		Assert.assertTrue(es.getIsRefund());
		Assert.assertEquals(ayTrade.getOrders().stream().map(Order::getRefundStatus).collect(Collectors.toSet()),
			Sets.newHashSet(es.getRefundStatus()));

		Assert.assertEquals("MD5", es.getMergeMd5());
	}

	/**
	 * 不需要发票
	 */
	@Test
	public void handle2() {
		Random random = new Random();
		int ayStatus = random.nextInt(9);
		when(statusCodeConfigService.getPlatformStatusAyStatus(anyString())).thenReturn(String.valueOf(ayStatus));

		int tag = random.nextInt(6);
		when(tradeHandleService.tradeStatusConvertTag(anyString(), anyInt())).thenReturn(tag);

		String sellerId = "sellerId";
		String sellerNick = "sellerNick";
		String buyerNick = "buyerNick";
		String corpId = "corpId";
		String tid = "tid";
		String ayTid = "ayTid";
		String storeId = "storeId";
		String receiverName = "receiverName";
		String receiverPhone = "receiverPhone";
		String receiverMobile = "receiverMobile";
		String receiverAddress = "receiverAddress";
		Date created = new Date();

		OriginDecryptData originDecryptData = new OriginDecryptData();
		originDecryptData.setBuyerNick(buyerNick);
		originDecryptData.setReceiverName(receiverName);
		originDecryptData.setReceiverPhone(receiverPhone);
		originDecryptData.setReceiverMobile(receiverMobile);
		originDecryptData.setReceiverAddress(receiverAddress);

		Trade trade = createTrade();
		trade.setSellerNick(sellerNick);
		trade.setBuyerNick(buyerNick);
		trade.setTidStr(tid);
		trade.setBuyerNick(buyerNick);
		trade.setReceiverName(receiverName);
		trade.setReceiverPhone(receiverPhone);
		trade.setReceiverMobile(receiverMobile);
		trade.setReceiverAddress(receiverAddress);

		AyTrade ayTrade = new AyTrade(trade);

		TradeBo tradeBo = new TradeBo();
		tradeBo.setSellerId(sellerId);
		tradeBo.setTid(tid);
		tradeBo.setSellerId(sellerId);
		tradeBo.setStoreId(storeId);
		tradeBo.setOriginDecryptData(originDecryptData);
		tradeBo.setTrade(ayTrade);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(tag);
		tradeHandleBo.setCorpId(corpId);
		tradeHandleBo.setSellerId(sellerId);
		tradeHandleBo.setListId(991);
		tradeHandleBo.setSellerNick(sellerNick);
		tradeHandleBo.setTid(tid);
		tradeHandleBo.setCreated(created);
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setTrade(ayTrade);
		ayTrade.setInvoiceKind(null);
		ayTrade.setInvoiceType(null);

		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);

		AyTradeSearchES es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);

		Assert.assertFalse(es.getIsInvoice());
	}

	/**
	 * 没有退款
	 */
	@Test
	public void handle3() {
		Random random = new Random();
		int ayStatus = random.nextInt(9);
		when(statusCodeConfigService.getPlatformStatusAyStatus(anyString())).thenReturn(String.valueOf(ayStatus));

		int tag = random.nextInt(6);
		when(tradeHandleService.tradeStatusConvertTag(anyString(), anyInt())).thenReturn(tag);

		String sellerId = "sellerId";
		String sellerNick = "sellerNick";
		String buyerNick = "buyerNick";
		String corpId = "corpId";
		String tid = "tid";
		String ayTid = "ayTid";
		String storeId = "storeId";
		String receiverName = "receiverName";
		String receiverPhone = "receiverPhone";
		String receiverMobile = "receiverMobile";
		Date created = new Date();

		OriginDecryptData originDecryptData = new OriginDecryptData();
		originDecryptData.setBuyerNick(buyerNick);
		originDecryptData.setReceiverName(receiverName);
		originDecryptData.setReceiverPhone(receiverPhone);
		originDecryptData.setReceiverMobile(receiverMobile);

		Trade trade = createTrade();
		trade.setSellerNick(sellerNick);
		trade.setBuyerNick(buyerNick);
		trade.setTidStr(tid);
		trade.setBuyerNick(buyerNick);
		trade.setReceiverName(receiverName);
		trade.setReceiverPhone(receiverPhone);
		trade.setReceiverMobile(receiverMobile);

		AyTrade ayTrade = new AyTrade(trade);

		TradeBo tradeBo = new TradeBo();
		tradeBo.setSellerId(sellerId);
		tradeBo.setTid(tid);
		tradeBo.setSellerId(sellerId);
		tradeBo.setStoreId(storeId);
		tradeBo.setOriginDecryptData(originDecryptData);
		tradeBo.setTrade(ayTrade);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(tag);
		tradeHandleBo.setCorpId(corpId);
		tradeHandleBo.setSellerId(sellerId);
		tradeHandleBo.setListId(991);
		tradeHandleBo.setSellerNick(sellerNick);
		tradeHandleBo.setTid(tid);
		tradeHandleBo.setCreated(created);
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setTrade(ayTrade);

		ayTrade.getOrders().forEach(a->a.setRefundStatus(TaobaoStatusConstant.NO_REFUND));
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		AyTradeSearchES es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertFalse(es.getIsRefund());
		Assert.assertEquals(Lists.newArrayList(TaobaoStatusConstant.NO_REFUND), es.getRefundStatus());

		ayTrade.getOrders().forEach(a->a.setRefundStatus(TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE));
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertTrue(es.getIsRefund());
		Assert.assertEquals(Lists.newArrayList(TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE), es.getRefundStatus());

		ayTrade.getOrders().get(0).setRefundStatus(TaobaoStatusConstant.NO_REFUND);
		ayTrade.getOrders().get(1).setRefundStatus(TaobaoStatusConstant.NO_REFUND);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertFalse(es.getIsRefund());
		Assert.assertEquals(Lists.newArrayList(TaobaoStatusConstant.NO_REFUND), es.getRefundStatus());

		ayTrade.getOrders().get(0).setRefundStatus(TaobaoStatusConstant.NO_REFUND);
		ayTrade.getOrders().get(1).setRefundStatus(TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertTrue(es.getIsRefund());
		Assert.assertEquals(Lists.newArrayList(TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE), es.getRefundStatus());


		ayTrade.getOrders().get(0).setRefundStatus(TaobaoStatusConstant.REFUND_WAIT_SELLER_CONFIRM_GOODS);
		ayTrade.getOrders().get(1).setRefundStatus(TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertTrue(es.getIsRefund());
		Assert.assertEquals(Lists.newArrayList(Sets.newHashSet(TaobaoStatusConstant.REFUND_WAIT_SELLER_CONFIRM_GOODS, TaobaoStatusConstant.REFUND_WAIT_SELLER_AGREE)), es.getRefundStatus());
	}

	/**
	 * 时效订单
	 */
	@Test
	public void handle4() {
		Random random = new Random();
		int ayStatus = random.nextInt(9);
		when(statusCodeConfigService.getPlatformStatusAyStatus(anyString())).thenReturn(String.valueOf(ayStatus));

		int tag = random.nextInt(6);
		when(tradeHandleService.tradeStatusConvertTag(anyString(), anyInt())).thenReturn(tag);

		String sellerId = "sellerId";
		String sellerNick = "sellerNick";
		String buyerNick = "buyerNick";
		String corpId = "corpId";
		String tid = "tid";
		String ayTid = "ayTid";
		String storeId = "storeId";
		String receiverName = "receiverName";
		String receiverPhone = "receiverPhone";
		String receiverMobile = "receiverMobile";
		Date created = new Date();

		OriginDecryptData originDecryptData = new OriginDecryptData();
		originDecryptData.setBuyerNick(buyerNick);
		originDecryptData.setReceiverName(receiverName);
		originDecryptData.setReceiverPhone(receiverPhone);
		originDecryptData.setReceiverMobile(receiverMobile);

		Trade trade = createTrade();
		trade.setSellerNick(sellerNick);
		trade.setBuyerNick(buyerNick);
		trade.setTidStr(tid);
		trade.setBuyerNick(buyerNick);
		trade.setReceiverName(receiverName);
		trade.setReceiverPhone(receiverPhone);
		trade.setReceiverMobile(receiverMobile);

		AyTrade ayTrade = new AyTrade(trade);

		TradeBo tradeBo = new TradeBo();
		tradeBo.setSellerId(sellerId);
		tradeBo.setTid(tid);
		tradeBo.setSellerId(sellerId);
		tradeBo.setStoreId(storeId);
		tradeBo.setOriginDecryptData(originDecryptData);
		tradeBo.setTrade(ayTrade);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setOrderTag(tag);
		tradeHandleBo.setCorpId(corpId);
		tradeHandleBo.setSellerId(sellerId);
		tradeHandleBo.setListId(991);
		tradeHandleBo.setSellerNick(sellerNick);
		tradeHandleBo.setTid(tid);
		tradeHandleBo.setCreated(created);
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setTrade(ayTrade);

		// 时效类型: "tmallpromise.arrival.timing"
		ayTrade.setPromiseService(TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);

		AyTradeSearchES es = tradeBo.getAyTradeSearchES();
		Assert.assertNotNull(es);
		Assert.assertEquals(TimingPromiseConstant.ARRIVAL_TYPE_VALUE, es.getPromiseServiceType().intValue());

		// 时效类型: "tmallpromise.consign.timing"
		ayTrade.setPromiseService(TimingPromiseConstant.CONSIGN_TMALLPROMISE_NAME);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertEquals(TimingPromiseConstant.CONSIGN_TYPE_VALUE, es.getPromiseServiceType().intValue());

		// 时效类型: "tmallpromise.arrival.timing,tmallpromise.consign.timing"
		ayTrade.setPromiseService(TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME + "," + TimingPromiseConstant.CONSIGN_TMALLPROMISE_NAME);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);
		es = tradeBo.getAyTradeSearchES();
		Assert.assertEquals(TimingPromiseConstant.ARRIVAL_CONSIGN_TYPE_VALUE, es.getPromiseServiceType().intValue());
	}

	/**
	 * cid 和 OriginalTitle
	 */
	@Test
	public void handle45() {
		String sellerId = "sellerId";
		String sellerNick = "sellerNick";
		String corpId = "corpId";
		String tid = "tid";
		String ayTid = "ayTid";
		String storeId = "storeId";

		Trade trade = createTrade();
		trade.setSellerNick(sellerNick);
		trade.setTidStr(tid);

		List<Order> orders = Lists.newArrayList();
		Order order1 = new Order();
		order1.setOid(1L);
		order1.setCid(111L);
		order1.setTitle("title test 111");

		Order order2 = new Order();
		order2.setOid(2L);
		order2.setCid(222L);
		order2.setTitle("title test 222");

		orders.add(order1);
		orders.add(order2);
		trade.setOrders(orders);

		AyTrade ayTrade = new AyTrade(trade);

		TradeBo tradeBo = new TradeBo();
		tradeBo.setSellerId(sellerId);
		tradeBo.setTid(tid);
		tradeBo.setSellerId(sellerId);
		tradeBo.setStoreId(storeId);
		tradeBo.setTrade(ayTrade);

		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setCorpId(corpId);
		tradeHandleBo.setSellerId(sellerId);
		tradeHandleBo.setListId(991);
		tradeHandleBo.setSellerNick(sellerNick);
		tradeHandleBo.setTid(tid);
		tradeHandleBo.setStoreId(tradeBo.getStoreId());
		tradeHandleBo.setTrade(ayTrade);

		// 时效类型: "tmallpromise.arrival.timing"
		ayTrade.setPromiseService(TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME);
		eSHandleService.handle(tradeHandleBo, tradeBo, ayTid);

		AyTradeSearchES es = tradeBo.getAyTradeSearchES();


		Assert.assertNotNull(es);
		Assert.assertEquals(Sets.newHashSet(order1.getCid(), order2.getCid()), Sets.newHashSet(es.getCid()));
		Assert.assertEquals(Sets.newHashSet(order1.getTitle(), order2.getTitle()), Sets.newHashSet(es.getOriginalTitle()));
	}

	/**
	 * OrderBatchType.RDS 推送, SellerRate\BuyerRate 为true
	 */
	@Test
	public void update1() {
		AyTradeSearchES ayTradeSearch = spy(AyTradeSearchES.class);
		ayTradeSearch.setId("111");
		ayTradeSearch.setSellerRate(true);
		ayTradeSearch.setBuyerRate(true);
		when(ayTradeSearchDao.updateByIdWithNotNull(ArgumentMatchers
				.argThat(a -> !BooleanUtils.isTrue(a.getSellerRate()) || !BooleanUtils.isTrue(a.getBuyerRate()))))
			.thenThrow(new IllegalArgumentException());
		eSHandleService.update(ayTradeSearch, OrderBatchType.RDS);
		verify(ayTradeSearchDao).updateByIdWithNotNull(ArgumentMatchers
				.argThat(a -> a.getId().equals("111")));

		Assert.assertTrue(ayTradeSearch.getSellerRate());
		Assert.assertTrue(ayTradeSearch.getBuyerRate());
		Assert.assertEquals("111", ayTradeSearch.getId());
	}

	/**
	 * OrderBatchType.RDS 推送, SellerRate\BuyerRate 为false
	 */
	@Test
	public void update2() {
		AyTradeSearchES ayTradeSearch = spy(AyTradeSearchES.class);
		ayTradeSearch.setId("111");
		ayTradeSearch.setSellerRate(false);
		ayTradeSearch.setBuyerRate(false);
		when(ayTradeSearchDao
			.updateByIdWithNotNull(ArgumentMatchers.argThat(a -> a.getSellerRate() != null || a.getBuyerRate() != null)))
			.thenThrow(new IllegalArgumentException());
		eSHandleService.update(ayTradeSearch, OrderBatchType.RDS);
		verify(ayTradeSearchDao).updateByIdWithNotNull(ArgumentMatchers
				.argThat(a -> a.getId().equals("111")));

		Assert.assertFalse(ayTradeSearch.getSellerRate());
		Assert.assertFalse(ayTradeSearch.getBuyerRate());
		Assert.assertEquals("111", ayTradeSearch.getId());
	}

	/**
	 * OrderBatchType.API 推送
	 */
	@Test
	public void update3() {
		AyTradeSearchES ayTradeSearch = spy(AyTradeSearchES.class);
		ayTradeSearch.setId("111");
		ayTradeSearch.setSellerRate(false);
		ayTradeSearch.setBuyerRate(false);
		when(ayTradeSearchDao
			.updateByIdWithNotNull(ArgumentMatchers.argThat(a -> a.getSellerRate() || a.getBuyerRate())))
			.thenThrow(new IllegalArgumentException());
		eSHandleService.update(ayTradeSearch, OrderBatchType.API);
		verify(ayTradeSearchDao).updateByIdWithNotNull(ArgumentMatchers
				.argThat(a -> a.getId().equals("111")));

		Assert.assertFalse(ayTradeSearch.getSellerRate());
		Assert.assertFalse(ayTradeSearch.getBuyerRate());
		Assert.assertEquals("111", ayTradeSearch.getId());
	}

	/**
	 * isInsert = true
	 */
	@Test
	public void putTradeData1() {
		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		eSHandleService.putTradeData(ayTradeSearchES, true, OrderBatchType.RDS);
		verify(eSHandleService, never()).updateInsert(any(), any());
		verify(eSHandleService).insert(eq(ayTradeSearchES));
	}

	/**
	 * isInsert = false
	 */
	@Test
	public void putTradeData2() {
		doNothing().when(eSHandleService).updateInsert(any(), any());
		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		eSHandleService.putTradeData(ayTradeSearchES, false, OrderBatchType.RDS);
		verify(eSHandleService).updateInsert(eq(ayTradeSearchES), eq(OrderBatchType.RDS));
		verify(eSHandleService, never()).insert(any());
	}

	@Test
	public void insert() {
		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setId("id");
		eSHandleService.insert(ayTradeSearchES);
		verify(ayTradeSearchDao).save(ArgumentMatchers
			.argThat(a -> a.getId().equals("id")));
	}

	/**
	 * update成功
	 */
	@Test
	public void updateInsert1() {
		doReturn(1).when(eSHandleService).update(any(AyTradeSearchES.class), any(OrderBatchType.class));
		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setId("id");
		eSHandleService.updateInsert(ayTradeSearchES, OrderBatchType.RDS);
		verify(eSHandleService).update(eq(ayTradeSearchES), eq(OrderBatchType.RDS));
		verify(eSHandleService, never()).insert(any());
	}

	/**
	 * update失败
	 */
	@Test
	public void updateInsert2() {
		doReturn(0).when(eSHandleService).update(any(AyTradeSearchES.class), any(OrderBatchType.class));
		AyTradeSearchES ayTradeSearchES = new AyTradeSearchES();
		ayTradeSearchES.setId("id");
		eSHandleService.updateInsert(ayTradeSearchES, OrderBatchType.RDS);
		verify(eSHandleService).update(eq(ayTradeSearchES), eq(OrderBatchType.RDS));
		verify(eSHandleService).insert(eq(ayTradeSearchES));
	}

	private Trade createTrade() {
		Trade trade = new Trade();
		initObject(trade);

		Order order1 = new Order();
		Order order2 = new Order();
		initObject(order1);
		initObject(order2);
		trade.setOrders(Lists.newArrayList(order1, order2));

		LogisticsInfo logisticsInfo1 = new LogisticsInfo();
		LogisticsInfo logisticsInfo2 = new LogisticsInfo();
		initObject(logisticsInfo1);
		initObject(logisticsInfo2);
		trade.setLogisticsInfos(Lists.newArrayList(logisticsInfo1, logisticsInfo2));

		PromotionDetail promotionDetail1 = new PromotionDetail();
		PromotionDetail promotionDetail2 = new PromotionDetail();
		initObject(promotionDetail1);
		initObject(promotionDetail2);
		trade.setPromotionDetails(Lists.newArrayList(promotionDetail1, promotionDetail2));

		ServiceOrder serviceOrder1 = new ServiceOrder();
		ServiceOrder serviceOrder2 = new ServiceOrder();
		initObject(serviceOrder1);
		initObject(serviceOrder2);
		trade.setServiceOrders(Lists.newArrayList(serviceOrder1, serviceOrder2));

		LogisticsTag logisticsTag1 = new LogisticsTag();
		LogisticsTag logisticsTag2 = new LogisticsTag();
		initObject(logisticsTag1);
		initObject(logisticsTag2);
		trade.setServiceTags(Lists.newArrayList(logisticsTag1, logisticsTag2));

		trade.setDeliveryTime("2222-12-22 22:22:22");
		trade.setEsTime("1");
		return trade;
	}

	private void initObject(Object target) {
		Class<?> actualEditable = target.getClass();

		PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);

		Random random = new Random();
		for (PropertyDescriptor targetPd : targetPds) {
			Method writeMethod = targetPd.getWriteMethod();
			if (writeMethod != null) {
				try {
					if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
						writeMethod.setAccessible(true);
					}
					Object value = null;
					if (targetPd.getPropertyType() == Integer.class) {
						value = random.nextInt();
					} else if (targetPd.getPropertyType() == Long.class) {
						value = 10000L + random.nextInt();
					} else if (targetPd.getPropertyType() == Double.class) {
						value = 0.999 + random.nextInt();
					} else if (targetPd.getPropertyType() == Boolean.class) {
						value = true;
					} else if (targetPd.getPropertyType() == String.class) {
						value = targetPd.getName() + random.nextInt(10);
					} else if (targetPd.getPropertyType() == Date.class) {
						value = new Date();
					} else if (targetPd.getPropertyType() == LocalDateTime.class) {
						value = LocalDateTime.now();
					}
					writeMethod.invoke(target, value);
				} catch (Throwable ex) {
					throw new FatalBeanException(
						"Could not copy property '" + targetPd.getName() + "' from source to target", ex);
				}
			}
		}
	}
}
