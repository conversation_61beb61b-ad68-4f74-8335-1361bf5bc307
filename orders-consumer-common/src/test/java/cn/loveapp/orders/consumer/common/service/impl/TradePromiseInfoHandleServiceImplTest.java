package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.orders.common.api.entity.AyTradePromiseInfo;
import cn.loveapp.orders.common.constant.EsFields;
import cn.loveapp.orders.common.constant.OrderPromiseInfoConstant;
import cn.loveapp.orders.common.dao.es.CommonAyTradeSearchESDao;
import cn.loveapp.orders.common.dao.mongo.OrderPromiseInfoRepository;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.redis.OrderSaveLockRedisDao;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrderPromiseInfo;
import cn.loveapp.orders.common.service.TradeMergeOrderService;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.consumer.common.bo.TradePromiseInfoHandleBo;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
		classes = {TradePromiseInfoHandleServiceImpl.class}
		)
public class TradePromiseInfoHandleServiceImplTest {

	@SpyBean
	private TradePromiseInfoHandleServiceImpl tradePromiseInfoHandleService;

	@MockBean
	private CommonAyTradeSearchESDao ayTradeSearchESDao;

	@MockBean
	private OrderPromiseInfoRepository orderPromiseInfoRepository;

	@MockBean
	private OrderRepository orderRepository;

	@MockBean
	private OrderSaveLockRedisDao orderSaveLockRedisDao;

	@MockBean
	private TradeMergeOrderService tradeMergeOrderService;

	/**
	 * 库里面不存在当天推送的数据，直接入库
	 */
	@Test
	public void putTradeDataTest1() {
		TradePromiseInfoHandleBo handleBo = generateHandleBo();

		TcOrderPromiseInfo oldPromiseInfo = new TcOrderPromiseInfo();
		oldPromiseInfo.setPromiseId(handleBo.getPromiseInfo().getPromiseId() - 1);

		List<TcOrderPromiseInfo> oldPromiseInfoList = Collections.singletonList(oldPromiseInfo);

		TcOrderPromiseInfo newPromiseInfo = TcOrderPromiseInfo
				.of(handleBo.getSellerNick(), handleBo.getSellerId(), handleBo.getStoreId(), handleBo.getAppName(), handleBo.getTid());
		newPromiseInfo.additionalInit(handleBo.getPromiseInfo());

		when(orderPromiseInfoRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), eq(handleBo.getAppName()))).thenReturn(oldPromiseInfoList);

		tradePromiseInfoHandleService.putTradeData(handleBo);

		verify(tradePromiseInfoHandleService).insertUpdate(eq(newPromiseInfo));
	}

	/**
	 * 推送了旧的modified时间
	 */
	@Test
	public void putTradeDataTest2() {
		TradePromiseInfoHandleBo handleBo = generateHandleBo();

		TcOrderPromiseInfo oldPromiseInfo = new TcOrderPromiseInfo();
		oldPromiseInfo.setPromiseId(handleBo.getPromiseInfo().getPromiseId());
		LocalDateTime putModified = DateUtil.parseString(handleBo.getPromiseInfo().getUpdatedAt());
		oldPromiseInfo.setModified(DateUtil.convertLocalDateTimetoDate(putModified.plusHours(1)));

		List<TcOrderPromiseInfo> oldPromiseInfoList = Collections.singletonList(oldPromiseInfo);

		TcOrderPromiseInfo newPromiseInfo = TcOrderPromiseInfo
				.of(handleBo.getSellerNick(), handleBo.getSellerId(), handleBo.getStoreId(), handleBo.getAppName(), handleBo.getTid());
		newPromiseInfo.additionalInit(handleBo.getPromiseInfo());

		when(orderPromiseInfoRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), eq(handleBo.getAppName()))).thenReturn(oldPromiseInfoList);

		tradePromiseInfoHandleService.putTradeData(handleBo);

		verify(tradePromiseInfoHandleService, never()).insertUpdate(eq(newPromiseInfo));
		verify(tradePromiseInfoHandleService, never()).updateInsert(eq(newPromiseInfo));
		verify(tradePromiseInfoHandleService, never()).updateTradeSearch(eq(newPromiseInfo), eq(oldPromiseInfoList));
	}

	private TradePromiseInfoHandleBo generateHandleBo() {
		TradePromiseInfoHandleBo handleBo = new TradePromiseInfoHandleBo();
		handleBo.setSellerId("sellerId");
		handleBo.setSellerNick("sellerNick");
		handleBo.setTid("tid");
		handleBo.setStoreId("storeId");
		handleBo.setAppName("appName");

		AyTradePromiseInfo ayTradePromiseInfo = new AyTradePromiseInfo();
		ayTradePromiseInfo.setUpdatedAt("2021-01-11 00:00:00");
		ayTradePromiseInfo.setPromiseId(112233L);
		handleBo.setPromiseInfo(ayTradePromiseInfo);
		return handleBo;
	}

	/**
	 * 非删除 指定物流
	 */
	@Test
	public void updateTradeSearchTest1() {
		Integer newPromiseType = OrderPromiseInfoConstant.PROMISE_TYPE_APPOINT_LOGISTICS;
		String promiseLogisticsCode = "ZJS";

		TcOrderPromiseInfo newPromiseInfo = newTcOrderPromiseInfo();
		newPromiseInfo.setIsDeleted(false);
		newPromiseInfo.setPromiseType(newPromiseType);
		newPromiseInfo.setPromiseLogisticsCode(promiseLogisticsCode);


		tradePromiseInfoHandleService.updateTradeSearch(newPromiseInfo, generateOldPromiseInfoList());

		AyTradeSearchES searchES = newAyTradeSearchES(newPromiseInfo);
		searchES.setPromiseLogisticsCode(promiseLogisticsCode);
		searchES.setPromiseType(generateNewPromiseTypeList(newPromiseInfo, generateOldPromiseInfoList()));

		verify(ayTradeSearchESDao).updateByIdWithNull(eq(searchES), eq(Arrays.asList(EsFields.promiseType, EsFields.promiseLogisticsCode)));

	}

	/**
	 * 删除 指定物流
	 */
	@Test
	public void updateTradeSearchTest2() {
		Integer newPromiseType = OrderPromiseInfoConstant.PROMISE_TYPE_APPOINT_LOGISTICS;
		String promiseLogisticsCode = "ZJS";

		TcOrderPromiseInfo newPromiseInfo = newTcOrderPromiseInfo();
		newPromiseInfo.setIsDeleted(true);
		newPromiseInfo.setPromiseType(newPromiseType);
		newPromiseInfo.setPromiseLogisticsCode(promiseLogisticsCode);


		tradePromiseInfoHandleService.updateTradeSearch(newPromiseInfo, generateOldPromiseInfoList());

		AyTradeSearchES searchES = newAyTradeSearchES(newPromiseInfo);
		searchES.setPromiseLogisticsCode(null);
		searchES.setPromiseType(generateNewPromiseTypeList(newPromiseInfo, generateOldPromiseInfoList()));

		verify(ayTradeSearchESDao).updateByIdWithNull(eq(searchES), eq(Arrays.asList(EsFields.promiseType, EsFields.promiseLogisticsCode)));

	}

	/**
	 * 非删除 优先发货
	 */
	@Test
	public void updateTradeSearchTest3() {
		Integer newPromiseType = OrderPromiseInfoConstant.PROMISE_TYPE_SEND_PREVIOUS;
		Date promiseDeliveryTime = DateUtil.parseDateString("2021-1-11 00:00:00");

		TcOrderPromiseInfo newPromiseInfo = newTcOrderPromiseInfo();
		newPromiseInfo.setIsDeleted(false);
		newPromiseInfo.setPromiseType(newPromiseType);
		newPromiseInfo.setPromiseDeliveryTime(promiseDeliveryTime);


		tradePromiseInfoHandleService.updateTradeSearch(newPromiseInfo, generateOldPromiseInfoList());

		AyTradeSearchES searchES = newAyTradeSearchES(newPromiseInfo);
		searchES.setPromiseDeliveryTime(promiseDeliveryTime);
		searchES.setPromiseType(generateNewPromiseTypeList(newPromiseInfo, generateOldPromiseInfoList()));

		verify(ayTradeSearchESDao).updateByIdWithNull(eq(searchES), eq(Arrays.asList(EsFields.promiseType, EsFields.promiseDeliveryTime)));

	}

	/**
	 * 删除 优先发货
	 */
	@Test
	public void updateTradeSearchTest4() {
		Integer newPromiseType = OrderPromiseInfoConstant.PROMISE_TYPE_SEND_PREVIOUS;
		Date promiseDeliveryTime = DateUtil.parseDateString("2021-1-11 00:00:00");

		TcOrderPromiseInfo newPromiseInfo = newTcOrderPromiseInfo();
		newPromiseInfo.setIsDeleted(true);
		newPromiseInfo.setPromiseType(newPromiseType);
		newPromiseInfo.setPromiseDeliveryTime(promiseDeliveryTime);


		tradePromiseInfoHandleService.updateTradeSearch(newPromiseInfo, generateOldPromiseInfoList());

		AyTradeSearchES searchES = newAyTradeSearchES(newPromiseInfo);
		searchES.setPromiseDeliveryTime(null);
		searchES.setPromiseType(generateNewPromiseTypeList(newPromiseInfo, generateOldPromiseInfoList()));

		verify(ayTradeSearchESDao).updateByIdWithNull(eq(searchES), eq(Arrays.asList(EsFields.promiseType, EsFields.promiseDeliveryTime)));

	}


	private List<TcOrderPromiseInfo> generateOldPromiseInfoList(){
		TcOrderPromiseInfo promiseInfo1 = newTcOrderPromiseInfo();
		promiseInfo1.setIsDeleted(false);
		promiseInfo1.setPromiseType(OrderPromiseInfoConstant.PROMISE_TYPE_APPOINT_LOGISTICS);

		TcOrderPromiseInfo promiseInfo2 = newTcOrderPromiseInfo();
		promiseInfo2.setIsDeleted(false);
		promiseInfo2.setPromiseType(OrderPromiseInfoConstant.PROMISE_TYPE_SEND_PREVIOUS);

		return Arrays.asList(promiseInfo1, promiseInfo2);
	}

	private TcOrderPromiseInfo newTcOrderPromiseInfo() {
		TcOrderPromiseInfo promiseInfo = new TcOrderPromiseInfo();
		promiseInfo.setSellerNick("sellerNick");
		promiseInfo.setSellerId("sellerId");
		promiseInfo.setStoreId("storeId");
		promiseInfo.setAppName("appName");
		promiseInfo.setTid("tid");
		return promiseInfo;
	}

	private AyTradeSearchES newAyTradeSearchES(TcOrderPromiseInfo newPromiseInfo) {
		String sellerNick = newPromiseInfo.getSellerNick();
		String sellerId = newPromiseInfo.getSellerId();
		String storeId = newPromiseInfo.getStoreId();
		String tid = newPromiseInfo.getTid();
		String appName = newPromiseInfo.getAppName();

		AyTradeSearchES searchES = new AyTradeSearchES();
		searchES.setTid(tid);
		searchES.setStoreId(storeId);
		searchES.setSellerId(sellerId);
		searchES.setAppName(appName);
		return searchES;
	}

	private ArrayList<Integer> generateNewPromiseTypeList(TcOrderPromiseInfo newPromiseInfo, List<TcOrderPromiseInfo> oldPromiseInfoList) {
		Set<Integer> promiseTypeList = oldPromiseInfoList.stream()
				.filter(item -> BooleanUtils.isNotTrue(item.getIsDeleted())).map(TcOrderPromiseInfo::getPromiseType).collect(Collectors.toSet());

		Integer promiseType = newPromiseInfo.getPromiseType();

		if (BooleanUtils.isTrue(newPromiseInfo.getIsDeleted())) {
			promiseTypeList.remove(promiseType);
		}
		return ElasticsearchUtil.toList(promiseTypeList);
	}
}