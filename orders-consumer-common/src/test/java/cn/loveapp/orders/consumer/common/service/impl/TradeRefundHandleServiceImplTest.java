package cn.loveapp.orders.consumer.common.service.impl;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.*;

import java.time.LocalDateTime;
import java.util.Collections;

import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import com.taobao.api.domain.Refund;
import com.taobao.api.internal.util.TaobaoUtils;
import com.taobao.api.response.RefundGetResponse;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.orders.common.constant.TaobaoRefundPhaseConstant;
import cn.loveapp.orders.common.convert.CommonConvertMapper;
import cn.loveapp.orders.common.dao.es.CommonAyTradeSearchESDao;
import cn.loveapp.orders.common.dao.mongo.OrderRefundRepository;
import cn.loveapp.orders.common.dao.redis.OrderSaveLockRedisDao;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrderRefund;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.consumer.common.bo.TradeRefundHandleBo;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {TradeRefundHandleServiceImpl.class})
public class TradeRefundHandleServiceImplTest {

	@MockBean
	private OrderRefundRepository orderRefundRepository;

	@MockBean
	private CommonAyTradeSearchESDao ayTradeSearchESDao;

	@SpyBean
	private TradeRefundHandleServiceImpl tradeRefundHandleServiceImpl;

	@MockBean
	private OrderSaveLockRedisDao orderSaveLockRedisDao;

	@BeforeEach
	void setUp() {

	}

	/**
	 * 更新退款信息入库, 该笔订单还没有入库 , 直接插入退款表
	 */
	@Test
	public void putTradeDataTest1() {
		TradeRefundHandleBo handleBo = generateHandleBo();
		tradeRefundHandleServiceImpl.putTradeData(handleBo, LocalDateTime.now(), false);
		when(orderRefundRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), isNull())).thenReturn(null);

		TcOrderRefund tcOrderRefund = TcOrderRefund.of(handleBo.getSellerNick(), handleBo.getSellerId(), handleBo.getStoreId(), handleBo.getAppName(), handleBo.getTid(), handleBo.getOid(), handleBo.getRefund().getRefundId());
        tcOrderRefund.additionalInit(handleBo.getRefund(), CommonPlatformConstants.PLATFORM_TAO);

		verify(tradeRefundHandleServiceImpl, times(1)).insertUpdate(eq(tcOrderRefund));
		verify(tradeRefundHandleServiceImpl, never()).updateInsert(any());
	}

	/**
	 * 更新退款信息入库, 库里面modified大于当天推送的modified，直接忽略
	 */
	@Test
	public void putTradeDataTest2() {
		TradeRefundHandleBo handleBo = generateHandleBo();

		LocalDateTime modifiedLater = DateUtil.parseDate(handleBo.getRefund().getModified()).plusHours(1);

		TcOrderRefund lastTcOrderRefund = new TcOrderRefund();
		lastTcOrderRefund.setOid(handleBo.getOid());
		lastTcOrderRefund.setModified(DateUtil.convertLocalDateTimetoDate(modifiedLater));

		handleBo.setAppName("appName");
		when(orderRefundRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), eq(handleBo.getAppName()))).thenReturn(Collections.singletonList(lastTcOrderRefund));
		tradeRefundHandleServiceImpl.putTradeData(handleBo, LocalDateTime.now(), false);
		verify(tradeRefundHandleServiceImpl, never()).insertUpdate(any());
		verify(tradeRefundHandleServiceImpl, never()).updateInsert(any());
	}

	/**
	 * 更新退款信息入库, 库里面存在退款信息且modified <= 当前modified
	 */
	@Test
	public void putTradeDataTest3() {
		TradeRefundHandleBo handleBo = generateHandleBo();

		LocalDateTime modifiedAfter = DateUtil.parseDate(handleBo.getRefund().getModified()).minusHours(1);

		TcOrderRefund lastTcOrderRefund = new TcOrderRefund();
		lastTcOrderRefund.setOid(handleBo.getOid());
		lastTcOrderRefund.setModified(DateUtil.convertLocalDateTimetoDate(modifiedAfter));

		when(orderRefundRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), eq(handleBo.getAppName()))).thenReturn(Collections.singletonList(lastTcOrderRefund));
		tradeRefundHandleServiceImpl.putTradeData(handleBo, LocalDateTime.now(), false);
		TcOrderRefund tcOrderRefund = TcOrderRefund.of(handleBo.getSellerNick(), handleBo.getSellerId(), handleBo.getStoreId(), handleBo.getAppName(), handleBo.getTid(), handleBo.getOid(), handleBo.getRefund().getRefundId());
        tcOrderRefund.additionalInit(handleBo.getRefund(), CommonPlatformConstants.PLATFORM_TAO);
		verify(tradeRefundHandleServiceImpl).updateInsert(eq(tcOrderRefund));
	}

	@Test
	public void putTradeDataTest4() {
		TradeRefundHandleBo handleBo = generateHandleBo();
		// 售后退款
		handleBo.getRefund().setRefundPhase(TaobaoRefundPhaseConstant.AFTER_SALE);

		LocalDateTime modifiedAfter = DateUtil.parseDate(handleBo.getRefund().getModified()).minusHours(1);

		TcOrderRefund lastTcOrderRefund = new TcOrderRefund();
		lastTcOrderRefund.setOid(handleBo.getOid());
		lastTcOrderRefund.setModified(DateUtil.convertLocalDateTimetoDate(modifiedAfter));

		when(orderRefundRepository.queryByTid(eq(handleBo.getTid()), eq(handleBo.getSellerId()), eq(handleBo.getStoreId()), eq(handleBo.getAppName()))).thenReturn(Collections.singletonList(lastTcOrderRefund));
		tradeRefundHandleServiceImpl.putTradeData(handleBo, LocalDateTime.now(), false);
		TcOrderRefund tcOrderRefund = TcOrderRefund.of(handleBo.getSellerNick(), handleBo.getSellerId(), handleBo.getStoreId(), handleBo.getAppName(), handleBo.getTid(), handleBo.getOid(), handleBo.getRefund().getRefundId());
        tcOrderRefund.additionalInit(handleBo.getRefund(), CommonPlatformConstants.PLATFORM_TAO);
		verify(tradeRefundHandleServiceImpl).updateInsert(eq(tcOrderRefund));

		AyTradeSearchES searchES = new AyTradeSearchES();
		searchES.setTid(handleBo.getTid());
		searchES.setStoreId(handleBo.getStoreId());
		searchES.setSellerId(handleBo.getSellerId());
		searchES.setRefundPhase(ElasticsearchUtil.toList(Collections.singletonList(TaobaoRefundPhaseConstant.AFTER_SALE)));
		searchES.setAfterSaleRefundStatus(ElasticsearchUtil.toList(Collections.singletonList(handleBo.getRefund().getStatus())));
		searchES.setRefundTime(tcOrderRefund.getCreated());
		searchES.setRefundInvoiceNo(ElasticsearchUtil.toList(Collections.singletonList(tcOrderRefund.getSid())));
		searchES.setRefundLogisticsCompany(ElasticsearchUtil.toList(Collections.singletonList(tcOrderRefund.getCompanyName())));
		searchES.setRefundServiceType(ElasticsearchUtil.toList(Collections.singletonList(OrderUtil.getRefundServiceType(tcOrderRefund))));

		verify(ayTradeSearchESDao).updateByIdWithNotNull(eq(searchES));
	}

	private TradeRefundHandleBo generateHandleBo() {
		TradeRefundHandleBo handleBo = new TradeRefundHandleBo();
		handleBo.setTid("1159772897615871945");
		handleBo.setOid("1159772897615871945");
		handleBo.setSellerId("3936370796");
		handleBo.setStoreId("TAO");

		String jdpResponse = "{\"refund_get_response\":{\"refund\":{\"refund_id\":\"74257665347621104\",\"status\":\"SUCCESS\",\"seller_nick\":\"赵东昊的测试店铺\",\"buyer_nick\":\"~Mtyp55FHc4V1di3fq6iWUg==~FCj6cMiYd7nrPbUObNTebCHQpUsZ~1~~\",\"tid\":1124321089401620411,\"oid\":1124321089401620411,\"created\":\"2020-07-23 16:15:23\",\"modified\":\"2020-08-02 16:20:04\",\"address\":\"关关， 13523893929， 上海上海市宝山区   新二路55号裙楼3楼， 000000\",\"advance_status\":0,\"alipay_no\":\"2020071722001181131424439057\",\"attribute\":\";reason:401461;bizCode:taobao.general.refund;leavesCat:50019776;apply_reason_text:其他;itemBuyAmount:1;seller_batch:true;logisticsCompanyName:宅急送;sku:*************|尺寸#3B40X40cm#3A颜色分类#3B天蓝色#3A材质#3B泰麂绒;sgr:1;bgmtc:2020-07-17 11#3B08#3B11;sellerDoRefundNick:赵东昊的测试店铺;shop_name:爱用交易测试店铺;ttid:h5;sync:0;abnormal_dispute_status:0;rp3:1;seller_agreed_refund_fee:1;stopAgree:0;disputeTradeStatus:4;isVirtual:0;EXmrf:1;refundFrom:2;enfunddetail:1;gaia:2;ee_trace_id:2103ce9715963564036722297d08e8;pay_lock:timeout;lastOrder:0;tod:*********;newRefund:rp2;logisticsOrderCode:ZJS000091316481;intentReturnGoodsType:RETURN_BY_SELF;opRole:daemon;refundPostFee:0;prepaidFailure:TAOBAO_CREDIT_SCORE_NOT_MEET;apply_init_refund_fee:1;apply_text_id:401461;userCredit:0;sdkCode:ali.china.taobao;interceptStatus:0;restartForXiaoer:1;rootCat:50025004;tos:5;ol_tf:1;ability:1;appName:refundplatform2;payMode:alipay;workflowName:return_and_refund;rightsSuspend:1;seller_audit:0;itemPrice:1;\",\"company_name\":\"安能物流\",\"cs_status\":1,\"desc\":\"\",\"good_return_time\":\"2020-07-23 16:18:18\",\"good_status\":\"BUYER_RETURNED_GOODS\",\"has_good_return\":true,\"num\":1,\"num_iid\":618780453838,\"operation_contraint\":\"null\",\"order_status\":\"TRADE_CLOSED\",\"outer_id\":\"还是睡觉手机\",\"payment\":\"0.00\",\"price\":\"0.01\",\"reason\":\"其他\",\"refund_fee\":\"0.01\",\"refund_phase\":\"onsale\",\"refund_version\":1595492123911,\"shipping_type\":\"express\",\"sid\":\"*********\",\"sku\":\"*************|尺寸:40X40cm;颜色分类:天蓝色;材质:泰麂绒\",\"title\":\"自动化测试专用商品1\",\"total_fee\":\"0.01\"}}}";

		try {
			Refund refund = TaobaoUtils.parseResponse(
					jdpResponse, RefundGetResponse.class).getRefund();
			handleBo.setRefund(CommonConvertMapper.INSTANCE.toAyRefund(refund));
		} catch (Exception e) {
			fail();
		}
		return handleBo;
	}

}
