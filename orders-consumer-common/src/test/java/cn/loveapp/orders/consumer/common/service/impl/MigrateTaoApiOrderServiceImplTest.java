package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.convert.CommonConvertMapper;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.service.impl.PrintServiceImpl;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeRefundHandleBo;
import cn.loveapp.orders.consumer.common.dto.AyTradeBatchFullinfoPutDto;
import cn.loveapp.orders.consumer.common.service.MigrateTradeHandleService;
import cn.loveapp.orders.common.service.PrintService;
import cn.loveapp.orders.consumer.common.service.TradeRefundHandleService;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Refund;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.BDDMockito.*;


/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 15:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, MigrateTaoApiOrderServiceImpl.class,
		PrintServiceImpl.class, MigrateTradeHandleServiceImpl.class
	})
public class MigrateTaoApiOrderServiceImplTest {

	@MockBean
	private PrintService printService;

	@MockBean
	private MigrateTradeHandleService migrateTradeHandleService;

	@MockBean
	private UserProductionInfoExtService userProductionInfoExtService;

	@SpyBean
	private MigrateTaoApiOrderServiceImpl migrateTaoApiOrderService;

	@MockBean
	private TradeRefundHandleService tradeRefundHandleService;

	@Test
	public void batchFullInfoOrderPutRecords() {
		List<AyTrade> tradeList = new ArrayList<>();
		AyTradeBatchFullinfoPutDto ayTradeBatchFullinfoPutDto = new AyTradeBatchFullinfoPutDto(tradeList, "321321", "111111", "中华人民共和国",
			true, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false, false, any());
		migrateTaoApiOrderService.batchFullInfoOrderPutRecords(ayTradeBatchFullinfoPutDto);

        verify(migrateTaoApiOrderService, never()).pullApiOnlineOrderRecords(any(), eq("321321"), eq("111111"),
            eq("中华人民共和国"), any(), anyBoolean(), any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), any());
	}

//	@Test
//	public void batchFullInfoOrderPutRecords1() {
//		AyTrade trade = generateTrade();
//		List<AyTrade> tradeList = new ArrayList<>();
//		tradeList.add(trade);
//		trade.setSellerNick("中华人民共和国");
//
//		when(printService.queryByNickOne("中华人民共和国")).thenReturn("on");
//
//		TradeBo tradeBo = new TradeBo();
//		when(migrateTradeHandleService
//			.createTradeBo(any(AyTrade.class), anyString(), any(), anyString(), anyBoolean(), any(), any(), eq(true)))
//			.thenReturn(tradeBo);
//
//		migrateTaoApiOrderService.batchFullInfoOrderPutRecords(tradeList, "321321", "111111", "中华人民共和国",
//			true, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, true);
//
////		verify(migrateTradeHandleService).sendBatchOrders(eq(tradeBo));
//		verify(userProductionInfoExtService, never()).putPullStatus(anyString(), anyInt());
//	}

	@Test
	public void batchFullInfoOrderPutRecords2() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		List<AyTrade> tradeList = new ArrayList<>();
		tradeList.add(trade);

		TradeBo tradeBo = new TradeBo();
		when(migrateTradeHandleService
			.createTradeBo(any(AyTrade.class), anyString(), anyString(), any(), anyString(), anyBoolean(), any(), any()))
			.thenReturn(tradeBo);

		AyTradeBatchFullinfoPutDto ayTradeBatchFullinfoPutDto = new AyTradeBatchFullinfoPutDto(tradeList, "321321", "111111", "中华人民共和国",
			false, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false, false, any());
		migrateTaoApiOrderService.batchFullInfoOrderPutRecords(ayTradeBatchFullinfoPutDto);

		verify(migrateTradeHandleService).batchOrdersInfo(eq(tradeBo), eq("中华人民共和国"), any(), eq(OrderBatchType.API), eq(false));
		verify(userProductionInfoExtService, never()).putPullStatus(anyString(), anyInt(), anyString(), anyString());
	}

	@Test
	public void batchFullInfoOrderPutRecords3() {
		AyTrade trade = generateTrade();
		trade.setSellerNick("中华人民共和国");
		List<AyTrade> tradeList = new ArrayList<>();
		tradeList.add(trade);

		TradeBo tradeBo = new TradeBo();
		when(migrateTradeHandleService
			.createTradeBo(any(AyTrade.class), anyString(), any(), anyString(), anyString(), anyBoolean(), any(), any()))
			.thenReturn(tradeBo);
		AyTradeBatchFullinfoPutDto ayTradeBatchFullinfoPutDto = new AyTradeBatchFullinfoPutDto(tradeList, "321321", "111111","中华人民共和国",
			true, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false, false, any());
		migrateTaoApiOrderService.batchFullInfoOrderPutRecords(ayTradeBatchFullinfoPutDto);

		verify(migrateTradeHandleService).batchOrdersInfo(eq(tradeBo), eq("中华人民共和国"), any(), eq(OrderBatchType.API), eq(false));
		verify(userProductionInfoExtService).putPullStatus(eq("中华人民共和国"), eq(UserProductionInfoExt.DB_DONE), eq(CommonPlatformConstants.PLATFORM_TAO), anyString());
	}

	@Test
	public void batchFullInfoOrderPutRecords4() {
		AyTrade trade = generateTrade();
		List<AyTrade> tradeList = new ArrayList<>();
		tradeList.add(trade);

		TradeBo tradeBo = new TradeBo();
		when(migrateTradeHandleService
			.createTradeBo(any(AyTrade.class), anyString(), anyString(), anyString(), anyString(), anyBoolean(), any(), any()))
			.thenReturn(tradeBo);

		AyTradeBatchFullinfoPutDto ayTradeBatchFullinfoPutDto = new AyTradeBatchFullinfoPutDto(tradeList, "321321", "111111", "中华人民共和国",
			false, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE, false, false, any());
		migrateTaoApiOrderService.batchFullInfoOrderPutRecords(ayTradeBatchFullinfoPutDto);

		verify(migrateTradeHandleService, never()).batchOrdersInfo(any(TradeBo.class), anyString(), any(), any(OrderBatchType.class), anyBoolean());
	}

//	@Test
//	public void batchTmcOrderPutRecords() {
//		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
//		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
//		TmcOrdersRequest tmcOrdersRequest = new TmcOrdersRequest("中华人民共和国", "321321", "111111", LocalDateTime.now(), tradeMemoModified, tradeRated);
//		migrateTaoApiOrderService.batchTmcOrderPutRecords(tmcOrdersRequest, "111111");
//		verify(migrateTradeHandleService).sendBatchOrders(eq(tmcOrdersRequest));
//	}

	@Test
	public void batchTmcOrderPutRecords() {
		TmcOrdersRequest.TradeMemoModified tradeMemoModified = new TmcOrdersRequest.TradeMemoModified();
		TmcOrdersRequest.TradeRated tradeRated = new TmcOrdersRequest.TradeRated();
		TmcOrdersRequest tmcOrdersRequest = new TmcOrdersRequest("中华人民共和国", "321321", "111111", LocalDateTime.now(), tradeMemoModified, tradeRated, null);

		migrateTaoApiOrderService.batchTmcOrderPutRecords(tmcOrdersRequest, "111111", false, CommonAppConstants.APP_TRADE);

        verify(migrateTradeHandleService).batchTmcOrdersChangeInfo(eq(tmcOrdersRequest), eq("111111"), eq(false), CommonPlatformConstants.PLATFORM_TAO, null);
	}

	@Test
	public void refundGetPutRecordsTest1() throws Exception {
		String tid = "76900065023874519";
		String oid = "76900065023874519";

		Refund refund = new Refund();
		refund.setTid(Long.valueOf(tid));
		refund.setOid(Long.valueOf(oid));
		String sellerNick = "赵东昊的测试店铺";
		String sellerId = "123456";
		String platformId = CommonPlatformConstants.PLATFORM_TAO;
		String appName = CommonAppConstants.APP_TRADE;

		TradeRefundHandleBo handleBo = new TradeRefundHandleBo();
		handleBo.setRefund(CommonConvertMapper.INSTANCE.toAyRefund(refund));
		handleBo.setSellerNick(sellerNick);
		handleBo.setSellerId(sellerId);
		handleBo.setStoreId(platformId);
		handleBo.setTid(tid);
		handleBo.setOid(oid);
		handleBo.setAppName(CommonAppConstants.APP_TRADE);

        migrateTaoApiOrderService.refundGetPutRecords(CommonConvertMapper.INSTANCE.toAyRefund(refund), sellerNick,
            sellerId, platformId, appName, false, false);

		verify(tradeRefundHandleService).putTradeData(eq(handleBo), null, eq(false));
	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}
}
