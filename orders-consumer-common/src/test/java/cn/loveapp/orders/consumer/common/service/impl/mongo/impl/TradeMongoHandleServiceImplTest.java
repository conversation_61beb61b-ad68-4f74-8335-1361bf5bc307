package cn.loveapp.orders.consumer.common.service.impl.mongo.impl;

import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.impl.MigrateRdsOrderServiceImplTest;
import com.google.common.collect.Maps;
import com.taobao.api.domain.Order;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.when;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/5/18
 * @time: 2:20 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {MigrateRdsOrderServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, TradeMongoHandleServiceImpl.class, OrderRepository.class
	})
public class TradeMongoHandleServiceImplTest {

 	@MockBean
	private OrderRepository orderRepository;

 	@MockBean
	private AyStatusCodeConfigService ayStatusCodeConfigService;

	@SpyBean
	private TradeMongoHandleServiceImpl tradeHandleService;

	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@Before
	public void setup(){
		when(orderSavingPlatformHandleService.generateMergeMd5(any(TradeBo.class), any(), any(), any())).thenReturn("MD5");
	}

	@Test
	public void handle() {
		TradeBo tradeBo = new TradeBo();
		tradeBo.setMergeMd5("MD5");
		tradeBo.setSellerId("123123");
		tradeBo.setTid("123123123123123");
		tradeBo.setStoreId("TAO");
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setCorpId("123123");
		tradeHandleBo.setSellerNick("赵东昊的测试店铺");
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setAyTid("123123");
		tradeMain.setTid("123123");
		tradeBo.setAyTradeMain(tradeMain);
		AyTrade trade = new AyTrade();
		trade.setTid(123123L);
		trade.setTidStr("123123");
		trade.setStatus("WAIT_SEND_GOODS");
		Order o = new Order();
		o.setOid(123123L);
		o.setOidStr("123123");
		trade.setOrders(Arrays.asList(o));
		tradeBo.setTrade(trade);
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		tradeHandleService.handle(tradeHandleBo, tradeBo, "123123", maps);
		Assert.assertNotNull(tradeBo.getTcOrder());
		Assert.assertEquals("MD5", tradeBo.getTcOrder().getMergeMd5());
	}

	@Test
	public void update() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setBuyerRate(true);
		ayTradeMain.setSellerRate(true);
		when(orderRepository.updateByTid(argThat(a -> a.getSellerRate() != true || a.getBuyerRate() != true), any()))
			.thenThrow(new IllegalArgumentException());
		tradeHandleService.update(ayTradeMain, OrderBatchType.API);

		assertTrue(ayTradeMain.getBuyerRate());
		assertTrue(ayTradeMain.getSellerRate());
	}

	@Test
	public void update2() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setBuyerRate(false);
		ayTradeMain.setSellerRate(false);

		when(orderRepository.updateByTid(argThat(a -> a.getSellerRate() != false || a.getBuyerRate() != false), any()))
			.thenThrow(new IllegalArgumentException());
		tradeHandleService.update(ayTradeMain, OrderBatchType.API);

		assertFalse(ayTradeMain.getBuyerRate());
		assertFalse(ayTradeMain.getSellerRate());
	}

	@Test
	public void update3() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setBuyerRate(false);
		ayTradeMain.setSellerRate(false);

		when(orderRepository.updateByTid(argThat(a -> a.getSellerRate() != null || a.getBuyerRate() != null), any()))
			.thenThrow(new IllegalArgumentException());
		tradeHandleService.update(ayTradeMain, OrderBatchType.RDS);

		assertFalse(ayTradeMain.getBuyerRate());
		assertFalse(ayTradeMain.getSellerRate());
	}

	@Test
	public void update4() {
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setBuyerRate(true);
		ayTradeMain.setSellerRate(true);

		when(orderRepository.updateByTid(argThat(a -> a.getSellerRate() != true || a.getBuyerRate() != true), any()))
			.thenThrow(new IllegalArgumentException());
		tradeHandleService.update(ayTradeMain, OrderBatchType.RDS);
		assertTrue(ayTradeMain.getBuyerRate());
		assertTrue(ayTradeMain.getSellerRate());
	}
}
