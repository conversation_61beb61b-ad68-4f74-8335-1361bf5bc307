package cn.loveapp.orders.consumer.common.dao.order;

import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.orders.common.proto.PullApiOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequestProto;
import cn.loveapp.orders.common.proto.head.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.orders.common.utils.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.taobao.api.domain.Trade;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Ignore
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
public class TradeListDaoTest {

	@Value("${orders.taobao.taobaoReportServerUrl}")
	private String taobaoReportServerUrl;


	@Test
	public void testLocalDateTime() {
		//2 转换一下
		LocalDateTime nowSyncLocalDateTime = LocalDateTime.now();
		LocalDateTime l = nowSyncLocalDateTime.plusMinutes(-10);
		//转成date对象
		ZoneId zoneId = ZoneId.systemDefault();
		ZonedDateTime zdtNow = nowSyncLocalDateTime.atZone(zoneId);
		System.out.println(zdtNow);
	}

	@Test
	public void testTimeStamp() {
		LocalDateTime d = LocalDateTime.now();
		System.out.println(d.toEpochSecond(ZoneOffset.of("+8")));
	}

	@Test
	public void testProfile() {
		System.out.println(taobaoReportServerUrl);
	}

	@Test
	public void testWhile() {
		int i = 3;
		while (i >= 0) {
			System.out.println(i);
			i--;
		}
	}

	@Test
	public void testStringInt() {
		String s = "xxxx";
		int i = 0;
		System.out.println(s + i);
	}

	@Test
	public void testJSON() {
		//封装协议头
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setSellerNick("xxxxx");
		//封装协议体
		List<PullHistoryOrdersRequest> pullHistoryOrdersRequests = new ArrayList<>();
		PullHistoryOrdersRequest pullHistoryOrdersRequest = new PullHistoryOrdersRequest();
		pullHistoryOrdersRequest.setModified(LocalDateTime.now());
		pullHistoryOrdersRequest.setTid("xxxx");
		pullHistoryOrdersRequests.add(pullHistoryOrdersRequest);
		PullApiOrdersRequest pullApiOrdersRequest = new PullApiOrdersRequest();
		pullApiOrdersRequest.setPullHistoryOrdersRequests(pullHistoryOrdersRequests);
		//封装协议包
		PullHistoryOrdersRequestProto pullHistoryOrdersRequestProto = new PullHistoryOrdersRequestProto();
		pullHistoryOrdersRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		pullHistoryOrdersRequestProto.setPullApiOrdersRequest(pullApiOrdersRequest);
		String s = JSON.toJSONString(pullHistoryOrdersRequestProto);
		PullHistoryOrdersRequestProto p = JSON.parseObject(s, PullHistoryOrdersRequestProto.class);
		System.out.println();
	}


	@Test
	public void testAsync() {
		List<CompletableFuture> completableFutures = ImmutableList.of(
			CompletableFuture.runAsync(() -> {
				//1处理支付信息
				System.out.println("支付信息");
			}),
			CompletableFuture.runAsync(() -> {
				//2处理发票信息
				System.out.println("发票信息");
			}),
			CompletableFuture.runAsync(() -> {
				//3处理物流信息
				System.out.println("物流信息");
			}),
			CompletableFuture.runAsync(() -> {
				//4处理扩展信息
				System.out.println("扩展信息");
			}),
			CompletableFuture.runAsync(() -> {
				//5处理搜索信息
				System.out.println("搜索信息");
			}),
			CompletableFuture.runAsync(() -> {
				//6处理子单信息
				System.out.println("子单信息");
				try{
					Thread.sleep(5000);
				} catch (Exception e){}
			})
		);
		CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[6])).join();
		System.out.println("异步写入完成");
	}

	@Test
	public void testMillSencods() {
		System.out.println(DateUtil.convertLocalDateTimetoStringMillSencond());
		System.out.println(60 / 3);
		List<Integer> collection = new ArrayList<>();
		for (int i = 0; i < 60; i++)
			collection.add(i);
		collection.stream().skip(0).limit(20).forEach(System.out::println);
	}

	@Test
	public void testJSONS() {
		String json = "{\"trade_fullinfo_get_customization_response\":{\"trade\":{\"adjust_fee\":\"0.00\",\"buyer_cod_fee\":\"0.00\",\"buyer_nick\":\"t_1499352974219_0763\",\"buyer_rate\":false,\"coupon_fee\":0,\"created\":\"2018-11-28 16:46:28\",\"delay_create_delivery\":1,\"discount_fee\":\"0.00\",\"end_time\":\"2018-11-29 16:46:47\",\"is_sh_ship\":false,\"modified\":\"2018-11-29 16:46:46\",\"num\":1,\"num_iid\":580743792822,\"ofp_hold\":1,\"orders\":{\"order\":[{\"adjust_fee\":\"0.00\",\"buyer_rate\":false,\"cid\":162116,\"discount_fee\":\"19.00\",\"divide_order_fee\":\"79.00\",\"end_time\":\"2018-11-29 16:46:47\",\"is_daixiao\":false,\"is_oversold\":false,\"num\":1,\"num_iid\":580743792822,\"oid\":277819904493197919,\"oid_str\":\"277819904493197919\",\"order_from\":\"WAP,WAP\",\"payment\":\"79.00\",\"pic_path\":\"https:\\/\\/img.alicdn.com\\/bao\\/uploaded\\/i3\\/55621898\\/O1CN01BGppzs1PtKCkUZEMZ_!!0-item_pic.jpg\",\"price\":\"98.00\",\"refund_id\":\"16084032551191979\",\"refund_status\":\"SUCCESS\",\"seller_rate\":false,\"seller_type\":\"C\",\"sku_id\":\"4046873447258\",\"sku_properties_name\":\"颜色分类:黑色;尺码:均码\",\"snapshot_url\":\"o:277819904493197919_1\",\"status\":\"PAID_FORBID_CONSIGN\",\"title\":\"2018秋冬新品仙气木耳百褶花边网纱花瓣袖半高立领宽松打底衫\",\"total_fee\":\"79.00\"}]},\"pay_time\":\"2018-11-28 16:46:36\",\"payment\":\"79.00\",\"pic_path\":\"https:\\/\\/img.alicdn.com\\/bao\\/uploaded\\/i3\\/55621898\\/O1CN01BGppzs1PtKCkUZEMZ_!!0-item_pic.jpg\",\"platform_subsidy_fee\":\"0.00\",\"post_fee\":\"0.00\",\"price\":\"98.00\",\"promotion_details\":{\"promotion_detail\":[{\"discount_fee\":\"19.00\",\"id\":277819904493197919,\"promotion_desc\":\"拼团优惠:省19.00元\",\"promotion_id\":\"shareGroup-580743792822_580743792822\",\"promotion_name\":\"拼团优惠\"}]},\"received_payment\":\"0.00\",\"receiver_address\":\"龙祥街***********09\",\"receiver_city\":\"汕头市\",\"receiver_country\":\"\",\"receiver_district\":\"龙湖区\",\"receiver_mobile\":\"136****1655\",\"receiver_name\":\"*头\",\"receiver_state\":\"广东省\",\"receiver_town\":\"****\",\"receiver_zip\":\"000000\",\"seller_flag\":0,\"seller_nick\":\"zhouwenpin1985\",\"seller_rate\":false,\"service_tags\":{\"logistics_tag\":[{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"origAreaId=440507;consignDate=168\",\"service_type\":\"TB_CONSIGN_DATE\"},{\"service_tag\":\"lgType=-4\",\"service_type\":\"FAST\"}]},\"order_id\":\"277819904493197919\"},{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"consignDate=168\",\"service_type\":\"TB_CONSIGN_DATE\"}]},\"order_id\":\"277819904493197919\"}]},\"service_type\":\"\",\"shipping_type\":\"express\",\"status\":\"PAID_FORBID_CONSIGN\",\"tid\":277819904493197919,\"tid_str\":\"277819904493197919\",\"title\":\"草莓棉花糖淑女搭配\",\"top_hold\":1,\"total_fee\":\"98.00\",\"trade_from\":\"WAP,WAP\",\"type\":\"fixed\"},\"request_id\":\"kvbq641qe2mp\"}}";
		JSONObject tradeJsonResponse = JSON.parseObject(json).getJSONObject("trade_fullinfo_get_customization_response");
		String tidStr = tradeJsonResponse.getJSONObject("trade").getString("tid");
		Trade trade = JSON.parseObject(JSON.toJSONString(tradeJsonResponse.getJSONObject("trade")), Trade.class);
		System.out.println(JSON.toJSONString(trade));
	}

	@Test
	public void testAes() {
		AesUtil aes = AesUtil.getInstance();
		try {
//			String a = aes.encryptForPhone("13896013454","a0NzR0xIWlE3OVVRaXZUUVVvY0FadnRldwAAAAAAAAA=");
//			String sb = aes.aesDecryptForSession("MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4YsM6wn7pwpQkDsL+iAS7I",
//				"a0NzR0xIWlE3OVVRaXZUUVVvY0FadnRldwAAAAAAAAA=");
			String key = "a0NzR0xIWlE3OVVRaXZUUVVvY0FadnRldwAAAAAAAAA=";
			String plainText = AesUtil.getInstance().aesDecryptForSession("MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4YsM6wn7pwpQkDsL+iAS7I",
				key);
			System.out.println(plainText);
		}catch (Exception e)
		{
			System.out.println(e);
		}
	}

	@Test
	public void diff() {
//		String s = "{\"notify_trade\":{\"post_fee\":\"0.00\",\"iid\":585285305424,\"oid\":260935790894113711,\"type\":\"guarantee_trade\",\"tid\":260935790894113711,\"nick\":\"viyayaya\",\"buyer_nick\":\"666天勇\",\"user_id\":\"1056865090\",\"topic\":\"trade\",\"modified\":\"2019-01-04 09:21:45\",\"payment\":\"39.90\",\"tao_status\":\"TRADE_CLOSED\",\"seller_nick\":\"viyayaya\",\"status\":\"TradeClose\"}}";
//		OrderDelayRequestProto o = JSON.parseObject(s, OrderDelayRequestProto.class);
//		System.out.println(s);
		List<Integer> i = IntStream.range(0, 100000).boxed().collect(Collectors.toList());
		System.out.println(LocalDateTime.now());
		System.out.println(i.size());
		System.out.println(LocalDateTime.now());

//		OrderDelayRequest orderDelayRequest = new OrderDelayRequest();
//		orderDelayRequest.setModified(LocalDateTime.now());
//		orderDelayRequest.setNick("xxx");
//		orderDelayRequest.setSellerNick("xxx");
//		orderDelayRequest.setTid(111L);
//		orderDelayRequest.setUserId("xxx");
//		orderDelayRequest.setTopic("trade");
//		OrderDelayRequestProto orderDelayRequestProto = new OrderDelayRequestProto();
//		orderDelayRequestProto.setOrderDelayRequest(orderDelayRequest);
//		System.out.println(JSON.toJSONString(orderDelayRequestProto));
//		System.out.println(RuleUtil.getPartitionId("199089109"));
//		System.out.println(ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDateTime.now().plusSeconds(30L)));
	}


}

