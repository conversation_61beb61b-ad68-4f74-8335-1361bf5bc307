package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.api.response.BatchFullInfoResponse;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.rocketmq.RocketMQOrdersPretestConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoHistoryAppConfig;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.rds.JdpTbTradeDao;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequestProto;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.common.service.TaobaoAuthService;
import cn.loveapp.orders.common.service.TaobaoFullInfoService;
import cn.loveapp.orders.common.service.impl.TaobaoAuthServiceImpl;
import cn.loveapp.orders.common.service.impl.TaobaoFullInfoServiceImpl;
import cn.loveapp.orders.common.service.impl.TradeSendOrderHandlerServiceImpl;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.consumer.common.service.MigrateTradeHandleService;
import com.taobao.api.domain.Order;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.*;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 16:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {TradeDelayOrderHandlerServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, TaobaoFullInfoServiceImpl.class, TaobaoAuthServiceImpl.class,
		MigrateTradeHandleServiceImpl.class, JdpTbTradeDao.class,
		TradeSendOrderHandlerServiceImpl.class, RocketMQTaobaoHistoryAppConfig.class, RocketMQOrdersPretestConfig.class},
	properties = {
		"orders.taobao.ons.history.topic=topic",
		"orders.taobao.ons.history.tag=tag",
		"orders.pretest.users=测试用户",
		"orders.pretest.fullinfo.topic=test_topic"
	})
public class TradeDelayOrderHandlerServiceImplTest {

	@Autowired
	private RocketMQTaobaoHistoryAppConfig rocketMQTaobaoHistoryAppConfig;

	@Autowired
	private RocketMQOrdersPretestConfig pretestConfig;

	@MockBean
	private TaobaoFullInfoService taobaoFullInfoService;

	@MockBean
	private TaobaoAuthService taobaoAuthService;

	@MockBean
	private MigrateTradeHandleService migrateTradeHandleService;

	@MockBean
	private JdpTbTradeDao jdpTbTradeDao;


	@MockBean
	private DefaultMQProducer ordersPullApiHistoryOnsProducer;

	@MockBean
	private RocketMqQueueHelper rocketMqQueueHelper;

	@MockBean
	private AyStatusCodeConfigService ayStatusCodeConfigService;

	@SpyBean
	private TradeSendOrderHandlerServiceImpl tradeDelayOrderHandlerService;

	@MockBean
	private OrderRepository orderRepository;

	@Before
	public void setUp(){
	}

	@Test
	public void delayPushOrder() {
		doReturn("").when(rocketMqQueueHelper).push(anyString(), anyString(), any(), any(DefaultMQProducer.class));
		tradeDelayOrderHandlerService
			.pushTmcFullinfo("11111111111", "11111111111", "11111111111", LocalDateTime.now(),
				null, null, null, null, 0, CommonAppConstants.APP_TRADE);
		verify(rocketMqQueueHelper, never()).push(anyString(), anyString(), any(), any(DefaultMQProducer.class));
	}

	@Test
	public void delayPushOrder1() {
		doReturn("").when(rocketMqQueueHelper).push(anyString(), anyString(), any(), any(DefaultMQProducer.class));

		tradeDelayOrderHandlerService
			.pushTmcFullinfo("11111111111", "11111111111", "11111111111", LocalDateTime.now(),
				null, null, null, null, 0, CommonAppConstants.APP_TRADE);
		verify(rocketMqQueueHelper, never()).push(anyString(), anyString(), any(), any(DefaultMQProducer.class));
	}

	@Test
	public void delayPushOrder2() {
		doReturn("").when(rocketMqQueueHelper).push(anyString(), anyString(), any(), any(DefaultMQProducer.class));

		String tid = "11111111111";
		String sellerId = "222222222";
		String sellerNick = "33333333";
		String platformId = "TAO";
		UserDbId userDbId = new UserDbId(sellerNick, 1, 1, sellerId, sellerId, platformId, null);
		LocalDateTime modifid = LocalDateTime.now();
		tradeDelayOrderHandlerService
			.pushTmcFullinfo(tid, sellerNick, sellerId, modifid, userDbId, null, null, null, 4, CommonAppConstants.APP_TRADE);
		verify(rocketMqQueueHelper).push(eq(rocketMQTaobaoHistoryAppConfig.getTopic() + "1"), eq("*"), argThat(x->{
			PullHistoryOrdersRequestProto proto = (PullHistoryOrdersRequestProto)x;
			PullHistoryOrdersRequest request = proto.getPullApiOrdersRequest().getPullHistoryOrdersRequests().get(0);
			return request.getTid().equals(tid) && request.getModified().equals(modifid);
		}), any(DefaultMQProducer.class), eq(4));
	}

	@Test
	public void delayPushOrder3() {
		String tid = "11111111111";
		String sellerId = "222222222";
		String sellerNick = "33333333";
		String platformId = "TAO";
		UserDbId userDbId = new UserDbId(sellerNick, 1, 1, sellerId, sellerId, platformId, null);
		LocalDateTime modifid = LocalDateTime.now();

		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest(sellerNick, tid, "oid", modifid, null, null, null);

		tradeDelayOrderHandlerService
			.pushTmcFullinfo(tid, sellerNick, sellerId, modifid, userDbId, tmcOrdersRequest, null, null, 0, CommonAppConstants.APP_TRADE);

		verify(rocketMqQueueHelper).push(eq(rocketMQTaobaoHistoryAppConfig.getTopic() + "1"), eq("*"), argThat(x->{
			PullHistoryOrdersRequestProto proto = (PullHistoryOrdersRequestProto)x;
			PullHistoryOrdersRequest request = proto.getPullApiOrdersRequest().getPullHistoryOrdersRequests().get(0);
			return request.getTid().equals(tid) && request.getModified().equals(modifid) && proto.getTmcOrdersRequest()
				.equals(tmcOrdersRequest);
		}), any(DefaultMQProducer.class), eq(0));
	}

	@Test
	public void delayPushOrder4() {
		String tid = "11111111111";
		String sellerId = "222222222";
		String sellerNick = "测试用户";
		String platformId = "TAO";
		UserDbId userDbId = new UserDbId(sellerNick, 1, 1, sellerId, sellerId, platformId, null);
		LocalDateTime modifid = LocalDateTime.now();

		TmcOrdersRequest tmcOrdersRequest =
			new TmcOrdersRequest(sellerNick, tid, "oid", modifid, null, null, null);

		tradeDelayOrderHandlerService
			.pushTmcFullinfo(tid, sellerNick, sellerId, modifid, userDbId, tmcOrdersRequest, null, null, 0, CommonAppConstants.APP_TRADE);

		verify(rocketMqQueueHelper).push(eq(pretestConfig.getFullinfoTopic()), eq("*"), argThat(x->{
			PullHistoryOrdersRequestProto proto = (PullHistoryOrdersRequestProto)x;
			PullHistoryOrdersRequest request = proto.getPullApiOrdersRequest().getPullHistoryOrdersRequests().get(0);
			return request.getTid().equals(tid) && request.getModified().equals(modifid) && proto.getTmcOrdersRequest()
				.equals(tmcOrdersRequest);
		}), any(DefaultMQProducer.class), eq(0));
	}

	private BatchFullInfoResponse generateFullinfo() {
		List<AyTrade> tradeList = new ArrayList<>();
		tradeList.add(generateTrade());
		BatchFullInfoResponse batchFullInfoResponse = new BatchFullInfoResponse();
		batchFullInfoResponse.setTrades(tradeList);
		batchFullInfoResponse.setTopSession("321321");
		return batchFullInfoResponse;
	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}

}
