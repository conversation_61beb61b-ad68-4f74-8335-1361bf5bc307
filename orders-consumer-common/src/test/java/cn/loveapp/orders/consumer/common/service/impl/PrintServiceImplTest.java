package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.orders.common.dao.print.PrintsetDao;
import cn.loveapp.orders.common.entity.Printset;
import cn.loveapp.orders.common.service.impl.PrintServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import static org.mockito.BDDMockito.*;


/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 15:28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {PrintServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, PrintsetDao.class, PrintServiceImpl.class
	})
public class PrintServiceImplTest {

	@SpyBean
	private PrintServiceImpl printService;

	@MockBean
	private PrintsetDao printsetDao;

	@Test
	public void queryByNickOne() {
		String sellerNick = "xxxyyy";
        when(printsetDao.queryHpflagByNick(sellerNick, anyString(), anyString())).thenReturn(null);
		boolean hpflag = printService.queryByNickOne(sellerNick, anyString(), anyString() );
		Assert.assertEquals(hpflag, null);
	}

	@Test
	public void queryByNickOne1() {
		String sellerNick = "xxxyyy";
		Printset printset = new Printset();
		printset.setHpflag("on");
        when(printsetDao.queryHpflagByNick(sellerNick, anyString(), anyString())).thenReturn(printset);
		boolean hpflag = printService.queryByNickOne(sellerNick, anyString(), anyString() );
		Assert.assertEquals(hpflag, "on");
	}
}
