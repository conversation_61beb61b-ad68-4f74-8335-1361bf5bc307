package cn.loveapp.orders.consumer.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * TestDbConfig
 *
 * <AUTHOR>
 * @date 2018/10/26
 */
@TestConfiguration
public class TestDbConfig {
	@Bean(name = "directJdbcTemplate")
	@Autowired
	public JdbcTemplate directJdbcTemplate1(@Qualifier("ordersDataSource") DataSource dsMaster) {
		return new JdbcTemplate(dsMaster);
	}

}
