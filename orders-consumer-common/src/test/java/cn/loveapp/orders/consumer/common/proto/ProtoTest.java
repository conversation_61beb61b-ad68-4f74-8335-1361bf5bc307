package cn.loveapp.orders.consumer.common.proto;

import com.alibaba.fastjson.JSON;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @program: orders-services-group
 * @description: ProtoTest
 * @author: Jason
 * @create: 2018-12-06 20:27
 **/
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
public class ProtoTest {

	@Test
	public void testJsonProto() {
		PullOrdersRequestProto pullOrdersRequestProto = new PullOrdersRequestProto();
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		PullOrdersRequest pullOrdersRequest = new PullOrdersRequest();
		comLoveRpcInnerprocessRequestHead.setRequestId("xxxx");
		comLoveRpcInnerprocessRequestHead.setSellerNick("xxx");
		comLoveRpcInnerprocessRequestHead.setTid("xxxx");
		pullOrdersRequest.setPageNo(1);
		pullOrdersRequest.setSellerNick("xxxxxx");
		List<PullOrdersRequest> l = new ArrayList<>();
		l.add(pullOrdersRequest);
		l.add(pullOrdersRequest);
		pullOrdersRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		pullOrdersRequestProto.setPullOrdersRequest(l);
		System.out.println(JSON.toJSONString(pullOrdersRequestProto));
	}

}
