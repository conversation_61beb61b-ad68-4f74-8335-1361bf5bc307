package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.rocketmq.RocketMQOrdersPretestConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoHistoryAppConfig;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dto.OrderModifiedCheckResult;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.proto.PullApiOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequest;
import cn.loveapp.orders.common.proto.PullHistoryOrdersRequestProto;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.common.service.impl.TradeSendOrderHandlerServiceImpl;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
	classes = {MetricsAutoConfiguration.class, CompositeMeterRegistryAutoConfiguration.class
	})
public class TradeSendOrderHandlerServiceImplTest {

	@MockBean
	private RocketMQTaobaoHistoryAppConfig rocketMQTaobaoHistoryAppConfig;

	@MockBean
	private RocketMQOrdersPretestConfig rocketMQOrdersPretestConfig;

	@MockBean
	private DefaultMQProducer ordersPullApiHistoryOnsProducer;

	@MockBean
	private RocketMqQueueHelper rocketMqQueueHelper;

	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@SpyBean
	private TradeSendOrderHandlerServiceImpl tradeSendOrderHandlerService;

	@MockBean
	private OrderRepository orderRepository;

	@MockBean
	private AyStatusCodeConfigService statusCodeConfigService;

    @Test
    public void pushTmcFullinfo() {
		tradeSendOrderHandlerService.pushTmcFullinfo("tid", "sellerNick", "sellerId", LocalDateTime.now(),
			new UserDbId("sellerNick", 1, 1, "sellerId", "corpId", "storeId", null), null, "taoStatus", "status", 4, CommonAppConstants.APP_TRADE);
		verify(rocketMqQueueHelper).push(any(), any(), argThat(argument->
			((PullHistoryOrdersRequestProto)argument).getPullApiOrdersRequest().isCheckModified()
		), eq(ordersPullApiHistoryOnsProducer), eq(4));
    }

    @Test
    public void pushRequestFullInfoTopic() {
		UserDbId userDbId = new UserDbId("sellerNick", 1, 1, "sellerId", "corpId", "storeId", null);
		PullApiOrdersRequest pullApiOrdersRequest = new PullApiOrdersRequest();
		tradeSendOrderHandlerService.pushRequestFullInfoTopic(pullApiOrdersRequest, userDbId.getSellerNick(),
			userDbId.getSellerId(), userDbId.getSupplierId(), "platformId", "appName", "inputTopic", "inputTag"
			, 4);
		verify(rocketMqQueueHelper).push(eq("inputTopic"), eq("inputTag"), argThat(argument->
			!((PullHistoryOrdersRequestProto)argument).getPullApiOrdersRequest().isCheckModified()
		), eq(ordersPullApiHistoryOnsProducer), eq(4));
    }

	/**
	 * 库中无值
	 */
	@Test
	public void isObsoleteOrderRequest() throws Exception{
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";

		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();

        when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(null);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertFalse(result);
	}

	/**
	 * modified比库中新
	 */
	@Test
	public void isModifiedNeedPush2() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";

		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified().minusHours(1)));
		when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService.isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());
		Assert.assertFalse(result);
	}

	/**
	 * modified 与库中一样, 并且taoStatus与库中不一样
	 */
	@Test
	public void isModifiedNeedPush3() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";
		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setAyStatus(1);
		ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified()));
		ayTradeMain.setTaoStatus(request.getTaoStatus() + "1");

		when(statusCodeConfigService.getPlatformStatusAyStatus(any())).thenReturn("4");
		when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertFalse(result);
	}

	/**
	 * modified 与库中一样, 并且taoStatus为空
	 */
	@Test
	public void isModifiedNeedPush4() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";
		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();
		String tid = request.getTid();

		AyTradeMain ayTradeMain = new AyTradeMain();
        ayTradeMain.setAyStatus(1);
        ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified()));
        ayTradeMain.setTaoStatus(request.getTaoStatus());

        when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertTrue(result);
	}

	/**
	 * modified 与库中一样, 并且taoStatus与库中不一样
	 */
	@Test
	public void isModifiedNeedPush5() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";
		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();
		String tid = request.getTid();

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setAyStatus(1);
		ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified()));
		ayTradeMain.setTaoStatus(request.getTaoStatus() + 1);

        when(statusCodeConfigService.getPlatformStatusAyStatus(any())).thenReturn("7");
        when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertFalse(result);
	}



	/**
	 * modified 与库中一样, 并且taoStatus与库中一样
	 */
	@Test
	public void isModifiedNeedPush6() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";
		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();
		String tid = request.getTid();

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setAyStatus(1);
		ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified()));
		ayTradeMain.setTaoStatus(request.getTaoStatus());

		when(statusCodeConfigService.getPlatformStatusAyStatus(any())).thenReturn("1");
		when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, true, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertTrue(result);
	}

	/**
	 * modified 与库中一样, 并且taoStatus与库中一样, 但是不忽略相同时间
	 */
	@Test
	public void isModifiedNeedPush7() {
		String sellerId = "1111";
		String sellerNick = "木偶鱼zxy";
		String platformId = "TAO";
		String appName = "trade";
		PullHistoryOrdersRequest request = generatePullHistoryOrdersRequest();
		String tid = request.getTid();

		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setAyStatus(1);
		ayTradeMain.setModified(DateUtil.convertLocalDateTimetoDate(request.getModified()));
		ayTradeMain.setTaoStatus(request.getTaoStatus());

		when(statusCodeConfigService.getPlatformStatusAyStatus(any())).thenReturn("1");
		when(orderRepository.queryByTidGetAyTradeMain(any(), any(), any(), any())).thenReturn(ayTradeMain);
        OrderModifiedCheckResult orderModifiedCheckResult = tradeSendOrderHandlerService
            .isObsoleteOrderRequest(request, false, sellerNick, sellerId, platformId, appName);
        boolean result = BooleanUtils.isTrue(orderModifiedCheckResult.getIsObsoleteOrderRequest());

        Assert.assertFalse(result);
	}

	private PullHistoryOrdersRequest generatePullHistoryOrdersRequest() {
		//封装协议体
		PullHistoryOrdersRequest pullHistoryOrdersRequest = new PullHistoryOrdersRequest();
		pullHistoryOrdersRequest.setModified(LocalDateTime.now());
		pullHistoryOrdersRequest.setTid("1111111");
		pullHistoryOrdersRequest.setTaoStatus(TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS);

		return pullHistoryOrdersRequest;
	}
}
