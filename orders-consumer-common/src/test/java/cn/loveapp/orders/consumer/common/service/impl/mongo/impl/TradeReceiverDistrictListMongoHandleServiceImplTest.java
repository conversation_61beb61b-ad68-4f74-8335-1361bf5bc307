package cn.loveapp.orders.consumer.common.service.impl.mongo.impl;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.config.trade.OrderReceiverDistrictConfig;
import cn.loveapp.orders.common.config.trade.OrderReceiverDistrictConfig.OrderReceiverDistrict;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.dao.mongo.OrderReceiverDistrictListRepository;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.mongo.TcOrderReceiverDistrictList;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.bo.TradeReceiverDistrictListMongoBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.impl.MigrateRdsOrderServiceImplTest;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.api.domain.Order;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/5/18
 * @time: 3:20 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {MigrateRdsOrderServiceImplTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, TradeReceiverDistrictListMongoHandleServiceImpl.class, OrderReceiverDistrictConfig.class,
		AyStatusCodeConfigService.class, OrderReceiverDistrictListRepository.class
	})
public class TradeReceiverDistrictListMongoHandleServiceImplTest {

	@SpyBean
	private TradeReceiverDistrictListMongoHandleServiceImpl tradeReceiverDistrictListHandleService;

	@MockBean
	private OrderReceiverDistrictConfig orderReceiverDistrictConfig;

	@MockBean
	private AyStatusCodeConfigService ayStatusCodeConfigService;
	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@MockBean
	private OrderReceiverDistrictListRepository orderReceiverDistrictListRepository;
	private List<TcOrderReceiverDistrictList> receiverDistrictLists;

	private List<String> deleteIds = Lists.newArrayList("1","2","3");

	@Before
	public void setUp(){
		Set<OrderReceiverDistrict> receiverDistricts = Sets.newHashSet();
		receiverDistricts.add(new OrderReceiverDistrictConfig.OrderReceiverDistrict(0, "江苏省"));
		receiverDistricts.add(new OrderReceiverDistrictConfig.OrderReceiverDistrict(1, "江浙沪"));
		when(orderReceiverDistrictConfig
			.findOrderReceiverDistrict(any(), any(), any(), any(), any())).thenReturn(receiverDistricts);

		receiverDistrictLists = Lists.newArrayList();

		TradeHandleBo tradeHandleBo = generateTradeHandleBo();
		TcOrderReceiverDistrictList a1 = new TcOrderReceiverDistrictList();
		a1.setTaoStatus("TRADE_FINISHED");
		a1.setAyStatus(0);
		a1.setReceiverDistrict("江苏省");
		a1.setAyReceiverDistrictNumber(0);
		a1.setAyTid("TDE111111");
		receiverDistrictLists.add(a1);

		TcOrderReceiverDistrictList a2 = new TcOrderReceiverDistrictList();
		a2.setTaoStatus("TRADE_FINISHED");
		a2.setAyStatus(0);
		a2.setReceiverDistrict("江浙沪");
		a2.setAyReceiverDistrictNumber(1);
		a2.setAyTid("TDE111111");
		receiverDistrictLists.add(a2);

		when(orderReceiverDistrictListRepository.queryIdsByTidAndSellerIdAndStoreId(any(), any(), any(), any())).thenReturn(deleteIds);
	}

	@Test
	public void handle() {
		TradeBo tradeBo = generateTradeBo();
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		tradeReceiverDistrictListHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111", maps);
		TradeReceiverDistrictListMongoBo bo = tradeBo.getTradeReceiverDistrictListMongoBo();

		List<TcOrderReceiverDistrictList>  ayTradeReceiverDistrictLists = bo.getAyTradeReceiverDistrictList();
		Assert.assertFalse(bo.isOnlyUpdateStatus());
		Assert.assertNull(bo.getNeedDelTradeReceiverDistrictListIds());
		Assert.assertEquals(2, ayTradeReceiverDistrictLists.size());

		ayTradeReceiverDistrictLists.sort(Comparator.comparingInt(TcOrderReceiverDistrictList::getAyReceiverDistrictNumber));

		Assert.assertEquals("TAO2609382540576636280", ayTradeReceiverDistrictLists.get(0).getId());
	}

	/**
	 * 订单状态/收货地址都没变化
	 */
	@Test
	public void handle2() {
		TradeBo tradeBo = generateTradeBo();
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setMergeMd5("1");
		ayTradeMain.setTaoStatus("A");
		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setMergeMd5("1");
		lastAyTradeMain.setTaoStatus("A");

		tradeBo.setAyTradeMain(ayTradeMain);
		tradeBo.setLastAyTradeMain(lastAyTradeMain);
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		doAnswer(
			invocation -> {
				Object[] args = invocation.getArguments();
				TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo = ((TradeReceiverDistrictListMongoBo)args[2]);
				tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(true);
				tradeReceiverDistrictListMongoBo.setNeedDelTradeReceiverDistrictListIds(null);
				return null;
			}
		).when(orderSavingPlatformHandleService).updatingStatusAndReceiverStatus(any(), any(), any(), any(), any(), any());
		tradeReceiverDistrictListHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111", maps);
		TradeReceiverDistrictListMongoBo bo = tradeBo.getTradeReceiverDistrictListMongoBo();

		Assert.assertTrue(bo.isOnlyUpdateStatus());
		Assert.assertNull(bo.getNeedDelTradeReceiverDistrictListIds());

		List<TcOrderReceiverDistrictList>  ayTradeReceiverDistrictLists = bo.getAyTradeReceiverDistrictList();
		ayTradeReceiverDistrictLists.sort(Comparator.comparingInt(TcOrderReceiverDistrictList::getAyReceiverDistrictNumber));

		Assert.assertEquals("TAO2609382540576636280", ayTradeReceiverDistrictLists.get(0).getId());
	}

	/**
	 * 订单状态变化
	 */
	@Test
	public void handle3() {
		TradeBo tradeBo = generateTradeBo();
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setMergeMd5("1");
		ayTradeMain.setTaoStatus("A");
		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setMergeMd5("1");
		lastAyTradeMain.setTaoStatus("B");

		tradeBo.setAyTradeMain(ayTradeMain);
		tradeBo.setLastAyTradeMain(lastAyTradeMain);
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		doAnswer(
			invocation -> {
				Object[] args = invocation.getArguments();
				TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo = ((TradeReceiverDistrictListMongoBo)args[2]);
				tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(true);
				tradeReceiverDistrictListMongoBo.setNeedDelTradeReceiverDistrictListIds(null);
				return null;
			}
		).when(orderSavingPlatformHandleService).updatingStatusAndReceiverStatus(any(), any(), any(), any(), any(), any());
		tradeReceiverDistrictListHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111", maps);
		TradeReceiverDistrictListMongoBo bo = tradeBo.getTradeReceiverDistrictListMongoBo();

		Assert.assertTrue(bo.isOnlyUpdateStatus());
		Assert.assertNull(bo.getNeedDelTradeReceiverDistrictListIds());

		List<TcOrderReceiverDistrictList>  ayTradeReceiverDistrictLists = bo.getAyTradeReceiverDistrictList();
		ayTradeReceiverDistrictLists.sort(Comparator.comparingInt(TcOrderReceiverDistrictList::getAyReceiverDistrictNumber));

		Assert.assertEquals("TAO2609382540576636280", ayTradeReceiverDistrictLists.get(0).getId());
	}

	/**
	 * 订单收货地址变化
	 */
	@Test
	public void handle4() {
		TradeBo tradeBo = generateTradeBo();
		AyTradeMain ayTradeMain = new AyTradeMain();
		ayTradeMain.setMergeMd5("1");
		ayTradeMain.setTaoStatus("A");
		AyTradeMain lastAyTradeMain = new AyTradeMain();
		lastAyTradeMain.setMergeMd5("2");
		lastAyTradeMain.setTaoStatus("A");

		tradeBo.setAyTradeMain(ayTradeMain);
		tradeBo.setLastAyTradeMain(lastAyTradeMain);
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		doAnswer(
			invocation -> {
				Object[] args = invocation.getArguments();
				TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo = ((TradeReceiverDistrictListMongoBo)args[2]);
				tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(false);
				return null;
			}
		).when(orderSavingPlatformHandleService).updatingStatusAndReceiverStatus(any(), any(), any(), any(), any(), any());
		tradeReceiverDistrictListHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111", maps);
		TradeReceiverDistrictListMongoBo bo = tradeBo.getTradeReceiverDistrictListMongoBo();

		Assert.assertFalse(bo.isOnlyUpdateStatus());

		List<TcOrderReceiverDistrictList>  ayTradeReceiverDistrictLists = bo.getAyTradeReceiverDistrictList();
		ayTradeReceiverDistrictLists.sort(Comparator.comparingInt(TcOrderReceiverDistrictList::getAyReceiverDistrictNumber));

		Assert.assertEquals("TAO2609382540576636280", ayTradeReceiverDistrictLists.get(0).getId());
	}

	/**
	 * corpId为null
	 */
	@Test
	public void handle5() {
		TradeBo tradeBo = generateTradeBo();
		TradeHandleBo tradeHandleBo = generateTradeHandleBo();
		tradeHandleBo.setCorpId(null);
		receiverDistrictLists.get(0).setCorpId(null);
		receiverDistrictLists.get(1).setCorpId(null);
		HashMap<String, String> maps = Maps.newHashMapWithExpectedSize(1);
		maps.put("123123", "123123123");
		tradeReceiverDistrictListHandleService.handle(tradeHandleBo, tradeBo,
			"TDE111111", maps);
		TradeReceiverDistrictListMongoBo bo = tradeBo.getTradeReceiverDistrictListMongoBo();

		List<TcOrderReceiverDistrictList>  ayTradeReceiverDistrictLists = bo.getAyTradeReceiverDistrictList();
		Assert.assertFalse(bo.isOnlyUpdateStatus());
		Assert.assertNull(bo.getNeedDelTradeReceiverDistrictListIds());
		Assert.assertEquals(2, ayTradeReceiverDistrictLists.size());

		ayTradeReceiverDistrictLists.sort(Comparator.comparingInt(TcOrderReceiverDistrictList::getAyReceiverDistrictNumber));

		Assert.assertEquals("TAO2609382540576636280", ayTradeReceiverDistrictLists.get(0).getId());
	}



	@Test
	public void putTradeData() {
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListBo = new TradeReceiverDistrictListMongoBo();
		tradeReceiverDistrictListBo.setAyTradeReceiverDistrictList(receiverDistrictLists);
		tradeReceiverDistrictListBo.setHasTradeReceiverChanged(Boolean.FALSE);
		tradeReceiverDistrictListHandleService.putTradeData(tradeReceiverDistrictListBo, "11111111111",
			"TAO", "1056865090", true, OrderBatchType.RDS);
		verify(orderReceiverDistrictListRepository, times(1)).bulkWrite(any());
		verify(orderReceiverDistrictListRepository, never()).deleteBySellerIdAndTidAndStoreId(anyString(), anyString(), anyString(), any());
		verify(orderReceiverDistrictListRepository, never()).updateTaoStatusAndAyStatusById(any());

	}

	@Test
	public void putTradeData1() {
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListBo = new TradeReceiverDistrictListMongoBo();
		tradeReceiverDistrictListBo.setAyTradeReceiverDistrictList(receiverDistrictLists);
		tradeReceiverDistrictListBo.setOnlyUpdateStatus(true);
		tradeReceiverDistrictListHandleService.putTradeData(tradeReceiverDistrictListBo, "11111111111",
			"TAO", "1056865090", true, OrderBatchType.RDS);

		verify(orderReceiverDistrictListRepository).updateTaoStatusAndAyStatusById(any());
		verify(orderReceiverDistrictListRepository, never()).deleteBySellerIdAndTidAndStoreId(anyString(), anyString(), anyString(), any());
		verify(orderReceiverDistrictListRepository, never()).insertAll(any());
	}

	@Test
	public void putTradeData2() {
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListBo = new TradeReceiverDistrictListMongoBo();
		tradeReceiverDistrictListBo.setAyTradeReceiverDistrictList(receiverDistrictLists);
		tradeReceiverDistrictListBo.setNeedDelTradeReceiverDistrictListIds(deleteIds);
		tradeReceiverDistrictListBo.setHasTradeReceiverChanged(true);
		tradeReceiverDistrictListHandleService.putTradeData(tradeReceiverDistrictListBo, "11111111111",
			"TAO", "1056865090", true, OrderBatchType.RDS);

		verify(orderReceiverDistrictListRepository).deleteBySellerIdAndTidAndStoreId(anyString(), anyString(), anyString(), isNull());
		verify(orderReceiverDistrictListRepository, times(1)).bulkWrite(any());
		verify(orderReceiverDistrictListRepository, never()).updateTaoStatusAndAyStatusById(any());
	}

	@Test
	public void putTradeData3() {
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListBo = new TradeReceiverDistrictListMongoBo();
		tradeReceiverDistrictListBo.setAyTradeReceiverDistrictList(receiverDistrictLists);
		receiverDistrictLists.get(0).setAppName("appName");
		tradeReceiverDistrictListBo.setNeedDelTradeReceiverDistrictListIds(deleteIds);
		tradeReceiverDistrictListBo.setHasTradeReceiverChanged(true);
		tradeReceiverDistrictListHandleService.putTradeData(tradeReceiverDistrictListBo, "11111111111",
			"TAO", "1056865090", true, OrderBatchType.RDS);

		verify(orderReceiverDistrictListRepository).deleteBySellerIdAndTidAndStoreId(anyString(), anyString(), anyString(), eq("appName"));
		verify(orderReceiverDistrictListRepository, times(1)).bulkWrite(any());
		verify(orderReceiverDistrictListRepository, never()).updateTaoStatusAndAyStatusById(any());
	}

	@Test
	public void putTradeData4() {
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListBo = new TradeReceiverDistrictListMongoBo();
		tradeReceiverDistrictListHandleService.putTradeData(tradeReceiverDistrictListBo, "11111111111",
			"TAO", "1056865090", true, OrderBatchType.RDS);

		verify(orderReceiverDistrictListRepository, never()).deleteBySellerIdAndTidAndStoreId(anyString(), anyString(), anyString(), any());
		verify(orderReceiverDistrictListRepository, never()).insertAll(any());
		verify(orderReceiverDistrictListRepository, never()).updateTaoStatusAndAyStatusById(any());
	}



	private TradeHandleBo generateTradeHandleBo() {
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setTid("11111111111");
		tradeHandleBo.setTrade(generateTrade());
		return tradeHandleBo;
	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}

	private TradeBo generateTradeBo() {
		String s = "{\n"
			+ "    \"AyTradePayment\": [\n"
			+ "        {\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"buyerEmail\": \"\",\n"
			+ "            \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "            \"codStatus\": \"NEW_CREATED\",\n"
			+ "            \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"hasYfx\": false,\n"
			+ "            \"invoiceNo\": \"804102410967024363\",\n"
			+ "            \"listId\": 90,\n"
			+ "            \"logisticsCompany\": \"圆通速递\",\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"receiverAddress\": \"江苏省鹰潭市锦江镇东北街88号\",\n"
			+ "            \"receiverCity\": \"鹰潭市\",\n"
			+ "            \"receiverCountry\": \"\",\n"
			+ "            \"receiverDistrict\": \"其它区\",\n"
			+ "            \"receiverMobile\": \"021e1ea77bd91aaa0fc4d01a943a654e~~~MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4xoxkWIKLMHbw8GpBmQCPQ\",\n"
			+ "            \"receiverName\": \"5ZHB55DN54+m\",\n"
			+ "            \"receiverState\": \"江苏省\",\n"
			+ "            \"receiverZip\": \"335000\",\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"serviceTags\": \"[{\\\"logisticServiceTagList\\\":[{\\\"serviceTag\\\":\\\"origAreaId=360682;consignDate=720\\\",\\\"serviceType\\\":\\\"TB_CONSIGN_DATE\\\"},{\\\"serviceTag\\\":\\\"lgType=-4\\\",\\\"serviceType\\\":\\\"FAST\\\"}],\\\"orderId\\\":\\\"260938254057663628\\\"},{\\\"logisticServiceTagList\\\":[{\\\"serviceTag\\\":\\\"consignDate=720\\\",\\\"serviceType\\\":\\\"TB_CONSIGN_DATE\\\"}],\\\"orderId\\\":\\\"260938254057663628\\\"}]\",\n"
			+ "            \"shippingType\": \"express\",\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"yfxFee\": 0\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"ayTradeExtTaobao\": {\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"listId\": 90,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"tid\": \"260938254057663628\"\n"
			+ "    },\n"
			+ "    \"ayTradeInvoiceIdinfo\": {\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"isInvoice\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"tid\": \"260938254057663628\"\n"
			+ "    },\n"
			+ "    \"ayTradeMain\": {\n"
			+ "        \"ayOrderType\": 1,\n"
			+ "        \"ayStatus\": 5,\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"created\": \"2019-01-03 22:02:30\",\n"
			+ "        \"encryptionType\": 1,\n"
			+ "        \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "        \"goodsNum\": 1,\n"
			+ "        \"isError\": false,\n"
			+ "        \"isO2oPassport\": false,\n"
			+ "        \"isRefund\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"mergeMd5\": \"7ACB31FECAA104483AFF385EF25B0E73\",\n"
			+ "        \"modified\": \"2019-01-25 21:29:52\",\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"orderTag\": 0,\n"
			+ "        \"orderType\": \"fixed\",\n"
			+ "        \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "        \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"shippingType\": \"express\",\n"
			+ "        \"skuNum\": 1,\n"
			+ "        \"stepTradeStatus\": \"NEW_CREATED\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": \"260938254057663628\",\n"
			+ "        \"tradeFrom\": \"WAP,WAP\"\n"
			+ "    },\n"
			+ "    \"ayTradePayment\": [\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"alipayPoint\": 0,\n"
			+ "            \"availableConfirmFee\": 0,\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "            \"buyerCodFee\": 0,\n"
			+ "            \"buyerObtainPointFee\": 0,\n"
			+ "            \"codFee\": 0,\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"couponFee\": 0,\n"
			+ "            \"creditCardFee\": 0,\n"
			+ "            \"discountFee\": 0,\n"
			+ "            \"hasPostFee\": true,\n"
			+ "            \"isCreditPay\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"oid\": \"0\",\n"
			+ "            \"orderTaxFee\": 0,\n"
			+ "            \"paidCouponFee\": 0,\n"
			+ "            \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"platformSubsidyFee\": 0,\n"
			+ "            \"pointFee\": 0,\n"
			+ "            \"postFee\": 0,\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"promotionDetails\": \"[{\\\"discountFee\\\":\\\"20.00\\\",\\\"id\\\":260938254057663628,\\\"promotionDesc\\\":\\\"粉丝专属价:省20.00元\\\",\\\"promotionId\\\":\\\"MZDZ33760-7623776011_56609136268\\\",\\\"promotionName\\\":\\\"粉丝专属价\\\"}]\",\n"
			+ "            \"realPointFee\": 0,\n"
			+ "            \"sellerCodFee\": 0,\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"stepPaidFee\": 0,\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"totalFee\": 59.9\n"
			+ "        },\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"alipayPoint\": 0,\n"
			+ "            \"availableConfirmFee\": 0,\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "            \"buyerCodFee\": 0,\n"
			+ "            \"buyerObtainPointFee\": 0,\n"
			+ "            \"codFee\": 0,\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"couponFee\": 0,\n"
			+ "            \"creditCardFee\": 0,\n"
			+ "            \"discountFee\": 0,\n"
			+ "            \"hasPostFee\": true,\n"
			+ "            \"isCreditPay\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"orderTaxFee\": 0,\n"
			+ "            \"paidCouponFee\": 0,\n"
			+ "            \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"platformSubsidyFee\": 0,\n"
			+ "            \"pointFee\": 0,\n"
			+ "            \"postFee\": 0,\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"promotionDetails\": \"[{\\\"discountFee\\\":\\\"20.00\\\",\\\"id\\\":260938254057663628,\\\"promotionDesc\\\":\\\"粉丝专属价:省20.00元\\\",\\\"promotionId\\\":\\\"MZDZ33760-7623776011_56609136268\\\",\\\"promotionName\\\":\\\"粉丝专属价\\\"}]\",\n"
			+ "            \"realPointFee\": 0,\n"
			+ "            \"sellerCodFee\": 0,\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"stepPaidFee\": 0,\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"totalFee\": 59.9\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"ayTradeSearch\": {\n"
			+ "        \"abbreviationNick\": \"\",\n"
			+ "        \"ayOrderType\": 1,\n"
			+ "        \"ayStatus\": 5,\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"buyerNick\": \"hejinghan20131415\",\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"created\": \"2019-01-26T16:23:20.121\",\n"
			+ "        \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "        \"goodsNum\": 1,\n"
			+ "        \"invoiceNos\": \"804102410967024363\",\n"
			+ "        \"isError\": false,\n"
			+ "        \"isInvoice\": false,\n"
			+ "        \"isO2oPassport\": false,\n"
			+ "        \"isRefund\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"logisticsCompany\": \"圆通速递\",\n"
			+ "        \"mergeMd5\": \"7ACB31FECAA104483AFF385EF25B0E73\",\n"
			+ "        \"modified\": \"2019-01-25 21:29:52\",\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"orderTag\": 0,\n"
			+ "        \"orderType\": \"fixed\",\n"
			+ "        \"outerIids\": \"VV6036\",\n"
			+ "        \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "        \"receiver\": \"null|15879927779|江苏省鹰潭市锦江镇东北街88号|江苏省|鹰潭市|其它区||null|周琴珍\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"skuNum\": 1,\n"
			+ "        \"stepTradeStatus\": \"NEW_CREATED\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": \"260938254057663628\",\n"
			+ "        \"titles\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\"\n"
			+ "    },\n"
			+ "    \"ayTradeSubOrderList\": [\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayStatus\": 5,\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerRate\": true,\n"
			+ "            \"cid\": 50007003,\n"
			+ "            \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"created\": \"2019-01-03 22:02:30\",\n"
			+ "            \"discountFee\": 20,\n"
			+ "            \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "            \"fqgNum\": 0,\n"
			+ "            \"invoiceNo\": \"804102410967024363\",\n"
			+ "            \"isFqgSFee\": false,\n"
			+ "            \"isOversold\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"logisticsCompany\": \"圆通速递\",\n"
			+ "            \"mdFee\": 0,\n"
			+ "            \"num\": 1,\n"
			+ "            \"numIid\": \"************\",\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"outerIid\": \"VV6036\",\n"
			+ "            \"outerSkuId\": \"VV6036\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"refundStatus\": \"NO_REFUND\",\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"sellerRate\": false,\n"
			+ "            \"sellerType\": \"C\",\n"
			+ "            \"shippingType\": \"express\",\n"
			+ "            \"skuId\": \"4130555350550\",\n"
			+ "            \"skuPropertiesName\": \"颜色分类:粉色[预售12-15天]\",\n"
			+ "            \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"taoOrderFrom\": \"WAP,WAP\",\n"
			+ "            \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"title\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\",\n"
			+ "            \"totalFee\": 39.9\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"encryptionType\": 1,\n"
			+ "    \"endPoint\": false,\n"
			+ "    \"originDecryptData\": {\n"
			+ "        \"buyerAlipayNo\": \"158****7779\",\n"
			+ "        \"buyerNick\": \"hejinghan20131415\",\n"
			+ "        \"receiverMobile\": \"15879927779\",\n"
			+ "        \"receiverName\": \"周琴珍\"\n"
			+ "    },\n"
			+ "    \"sellerId\": \"1056865090\",\n"
			+ "    \"storeId\": \"TAO\",\n"
			+ "    \"tid\": \"260938254057663628\",\n"
			+ "    \"trade\": {\n"
			+ "        \"adjustFee\": \"0.00\",\n"
			+ "        \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "        \"alipayPoint\": 0,\n"
			+ "        \"availableConfirmFee\": \"0.00\",\n"
			+ "        \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "        \"buyerArea\": \"江苏电信\",\n"
			+ "        \"buyerCodFee\": \"0.00\",\n"
			+ "        \"buyerEmail\": \"\",\n"
			+ "        \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "        \"buyerObtainPointFee\": 0,\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"codFee\": \"0.00\",\n"
			+ "        \"codStatus\": \"NEW_CREATED\",\n"
			+ "        \"commissionFee\": \"0.00\",\n"
			+ "        \"consignTime\": 1547638032000,\n"
			+ "        \"couponFee\": 0,\n"
			+ "        \"created\": 1546524150000,\n"
			+ "        \"discountFee\": \"0.00\",\n"
			+ "        \"endTime\": 1548177088000,\n"
			+ "        \"hasPostFee\": true,\n"
			+ "        \"hasYfx\": false,\n"
			+ "        \"is3D\": false,\n"
			+ "        \"isBrandSale\": false,\n"
			+ "        \"isDaixiao\": false,\n"
			+ "        \"isForceWlb\": false,\n"
			+ "        \"isGift\": false,\n"
			+ "        \"isLgtype\": false,\n"
			+ "        \"isPartConsign\": false,\n"
			+ "        \"isShShip\": false,\n"
			+ "        \"isWt\": false,\n"
			+ "        \"modified\": 1548422992000,\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"nrShopGuideId\": \"\",\n"
			+ "        \"nrShopGuideName\": \"\",\n"
			+ "        \"num\": 1,\n"
			+ "        \"numIid\": ************,\n"
			+ "        \"orders\": [\n"
			+ "            {\n"
			+ "                \"adjustFee\": \"0.00\",\n"
			+ "                \"buyerRate\": true,\n"
			+ "                \"cid\": 50007003,\n"
			+ "                \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "                \"discountFee\": \"20.00\",\n"
			+ "                \"divideOrderFee\": \"39.90\",\n"
			+ "                \"endTime\": 1548177088000,\n"
			+ "                \"invoiceNo\": \"804102410967024363\",\n"
			+ "                \"isDaixiao\": false,\n"
			+ "                \"isOversold\": false,\n"
			+ "                \"logisticsCompany\": \"圆通速递\",\n"
			+ "                \"nrOuterIid\": \"VV6036\",\n"
			+ "                \"num\": 1,\n"
			+ "                \"numIid\": ************,\n"
			+ "                \"oid\": 260938254057663628,\n"
			+ "                \"oidStr\": \"260938254057663628\",\n"
			+ "                \"orderFrom\": \"WAP,WAP\",\n"
			+ "                \"outerIid\": \"VV6036\",\n"
			+ "                \"outerSkuId\": \"VV6036\",\n"
			+ "                \"payment\": \"39.90\",\n"
			+ "                \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "                \"price\": \"59.90\",\n"
			+ "                \"refundStatus\": \"NO_REFUND\",\n"
			+ "                \"sellerRate\": false,\n"
			+ "                \"sellerType\": \"C\",\n"
			+ "                \"shippingType\": \"express\",\n"
			+ "                \"skuId\": \"4130555350550\",\n"
			+ "                \"skuPropertiesName\": \"颜色分类:粉色[预售12-15天]\",\n"
			+ "                \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "                \"status\": \"TRADE_FINISHED\",\n"
			+ "                \"title\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\",\n"
			+ "                \"totalFee\": \"39.90\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"payTime\": 1546524153000,\n"
			+ "        \"payment\": \"39.90\",\n"
			+ "        \"pccAf\": 0,\n"
			+ "        \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "        \"platformSubsidyFee\": \"0.00\",\n"
			+ "        \"pointFee\": 0,\n"
			+ "        \"postFee\": \"0.00\",\n"
			+ "        \"price\": \"59.90\",\n"
			+ "        \"promotionDetails\": [\n"
			+ "            {\n"
			+ "                \"discountFee\": \"20.00\",\n"
			+ "                \"id\": 260938254057663628,\n"
			+ "                \"promotionDesc\": \"粉丝专属价:省20.00元\",\n"
			+ "                \"promotionId\": \"MZDZ33760-7623776011_56609136268\",\n"
			+ "                \"promotionName\": \"粉丝专属价\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"realPointFee\": 0,\n"
			+ "        \"receivedPayment\": \"39.90\",\n"
			+ "        \"receiverAddress\": \"江苏省鹰潭市锦江镇东北街88号\",\n"
			+ "        \"receiverCity\": \"鹰潭市\",\n"
			+ "        \"receiverCountry\": \"\",\n"
			+ "        \"receiverDistrict\": \"其它区\",\n"
			+ "        \"receiverMobile\": \"021e1ea77bd91aaa0fc4d01a943a654e~~~MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4xoxkWIKLMHbw8GpBmQCPQ\",\n"
			+ "        \"receiverName\": \"5ZHB55DN54+m\",\n"
			+ "        \"receiverState\": \"江苏省\",\n"
			+ "        \"receiverZip\": \"335000\",\n"
			+ "        \"sellerAlipayNo\": \"***<EMAIL>\",\n"
			+ "        \"sellerCanRate\": true,\n"
			+ "        \"sellerCodFee\": \"0.00\",\n"
			+ "        \"sellerEmail\": \"\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerMobile\": \"18988853116\",\n"
			+ "        \"sellerName\": \"张昌荣\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"serviceTags\": [\n"
			+ "            {\n"
			+ "                \"logisticServiceTagList\": [\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"origAreaId=360682;consignDate=720\",\n"
			+ "                        \"serviceType\": \"TB_CONSIGN_DATE\"\n"
			+ "                    },\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"lgType=-4\",\n"
			+ "                        \"serviceType\": \"FAST\"\n"
			+ "                    }\n"
			+ "                ],\n"
			+ "                \"orderId\": \"260938254057663628\"\n"
			+ "            },\n"
			+ "            {\n"
			+ "                \"logisticServiceTagList\": [\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"consignDate=720\",\n"
			+ "                        \"serviceType\": \"TB_CONSIGN_DATE\"\n"
			+ "                    }\n"
			+ "                ],\n"
			+ "                \"orderId\": \"260938254057663628\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"serviceType\": \"\",\n"
			+ "        \"shippingType\": \"express\",\n"
			+ "        \"sid\": \"260938254057663628\",\n"
			+ "        \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "        \"status\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": 260938254057663628,\n"
			+ "        \"tidStr\": \"260938254057663628\",\n"
			+ "        \"title\": \"薇娅viya饰品店\",\n"
			+ "        \"totalFee\": \"59.90\",\n"
			+ "        \"tradeFrom\": \"WAP,WAP\",\n"
			+ "        \"type\": \"fixed\",\n"
			+ "        \"youXiang\": false\n"
			+ "    }\n"
			+ "}";
		TradeBo tradeBo = JSON.parseObject(s, TradeBo.class);
		return tradeBo;
	}
}
