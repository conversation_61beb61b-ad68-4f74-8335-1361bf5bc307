package cn.loveapp.orders.consumer.common.service.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.EncryptionTypeConstant;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.constant.TimingPromiseConstant;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.common.service.impl.AyStatusCodeConfigServiceImpl;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mysql.TradeHandleServiceImpl;
import com.alibaba.fastjson.JSON;
import com.taobao.api.domain.Order;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 17:43
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = { MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, AyStatusCodeConfigServiceImpl.class,
		TradeHandleServiceImpl.class
	})
public class TradeHandleServiceImplTest {

	@MockBean
	private AyStatusCodeConfigService statusCodeConfigService;

	@SpyBean
	private TradeHandleServiceImpl tradeHandleService;

	@MockBean
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@Before
	public void setup(){
		when(orderSavingPlatformHandleService.generateMergeMd5(any(TradeBo.class), any(), any(), any())).thenReturn("MD5");
	}

	@Test
	public void handle() {
		TradeBo tradeBo = generateTradeBo();
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeHandleService.handle(generateTradeHandleBo(),tradeBo,
			"TDE111111");
		Assert.assertNotNull(tradeBo.getAyTradeMain());
		Assert.assertEquals("MD5", tradeBo.getAyTradeMain().getMergeMd5());
	}

	@Test
	public void handle2() {
		TradeBo tradeBo = generateTimingTradeBo("tmallPlatformEstimate." + TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111");

		AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
		Assert.assertNotNull(ayTradeMain);
		AyTrade trade = tradeBo.getTrade();
//		Assert.assertEquals(trade.getTimingPromise(), ayTradeMain.getTimingPromise());
//		Assert.assertEquals(trade.getPromiseService(), ayTradeMain.getPromiseService());
//		Assert.assertEquals(TimingPromiseConstant.ARRIVAL_TYPE_VALUE, ayTradeMain.getPromiseServiceType().intValue());
//		Assert.assertEquals(DateUtil.parseString(trade.getCollectTime()), ayTradeMain.getCollectTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDeliveryTime()), ayTradeMain.getDeliveryTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDispatchTime()), ayTradeMain.getDispatchTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getSignTime()), ayTradeMain.getSignTime());
//		Assert.assertEquals(trade.getCutoffMinutes(), ayTradeMain.getCutoffMinutes().toString());
//		Assert.assertEquals(trade.getEsTime(), ayTradeMain.getEsTime().toString());
//		Assert.assertEquals(trade.getEsDate(), ayTradeMain.getEsDate());
//		Assert.assertEquals(trade.getEsRange(), ayTradeMain.getEsRange());
//		Assert.assertEquals(trade.getOsDate(), ayTradeMain.getOsDate());
//		Assert.assertEquals(trade.getOsRange(), ayTradeMain.getOsRange());
		Assert.assertEquals("MD5", ayTradeMain.getMergeMd5());
	}

	@Test
	public void handle3() {
		TradeBo tradeBo = generateTimingTradeBo("tmallPromise." + TimingPromiseConstant.CONSIGN_TMALLPROMISE_NAME);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111");

		AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
		Assert.assertNotNull(ayTradeMain);
		AyTrade trade = tradeBo.getTrade();
//		Assert.assertEquals(trade.getTimingPromise(), ayTradeMain.getTimingPromise());
//		Assert.assertEquals(trade.getPromiseService(), ayTradeMain.getPromiseService());
//		Assert.assertEquals(TimingPromiseConstant.CONSIGN_TYPE_VALUE, ayTradeMain.getPromiseServiceType().intValue());
//		Assert.assertEquals(DateUtil.parseString(trade.getCollectTime()), ayTradeMain.getCollectTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDeliveryTime()), ayTradeMain.getDeliveryTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDispatchTime()), ayTradeMain.getDispatchTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getSignTime()), ayTradeMain.getSignTime());
//		Assert.assertEquals(trade.getCutoffMinutes(), ayTradeMain.getCutoffMinutes().toString());
//		Assert.assertEquals(trade.getEsTime(), ayTradeMain.getEsTime().toString());
//		Assert.assertEquals(trade.getEsDate(), ayTradeMain.getEsDate());
//		Assert.assertEquals(trade.getEsRange(), ayTradeMain.getEsRange());
//		Assert.assertEquals(trade.getOsDate(), ayTradeMain.getOsDate());
//		Assert.assertEquals(trade.getOsRange(), ayTradeMain.getOsRange());
		Assert.assertEquals("MD5", ayTradeMain.getMergeMd5());
	}

	@Test
	public void handle4() {
		TradeBo tradeBo = generateTimingTradeBo(
			"tmallPromise." + TimingPromiseConstant.CONSIGN_TMALLPROMISE_NAME + "," + "tmallPlatformEstimate." + TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111");

		AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
		Assert.assertNotNull(ayTradeMain);
		AyTrade trade = tradeBo.getTrade();
//		Assert.assertEquals(trade.getTimingPromise(), ayTradeMain.getTimingPromise());
//		Assert.assertEquals(trade.getPromiseService(), ayTradeMain.getPromiseService());
//		Assert.assertEquals(TimingPromiseConstant.ARRIVAL_CONSIGN_TYPE_VALUE, ayTradeMain.getPromiseServiceType().intValue());
//		Assert.assertEquals(DateUtil.parseString(trade.getCollectTime()), ayTradeMain.getCollectTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDeliveryTime()), ayTradeMain.getDeliveryTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getDispatchTime()), ayTradeMain.getDispatchTime());
//		Assert.assertEquals(DateUtil.parseString(trade.getSignTime()), ayTradeMain.getSignTime());
//		Assert.assertEquals(trade.getCutoffMinutes(), ayTradeMain.getCutoffMinutes().toString());
//		Assert.assertEquals(trade.getEsTime(), ayTradeMain.getEsTime().toString());
//		Assert.assertEquals(trade.getEsDate(), ayTradeMain.getEsDate());
//		Assert.assertEquals(trade.getEsRange(), ayTradeMain.getEsRange());
//		Assert.assertEquals(trade.getOsDate(), ayTradeMain.getOsDate());
//		Assert.assertEquals(trade.getOsRange(), ayTradeMain.getOsRange());
		Assert.assertEquals("MD5", ayTradeMain.getMergeMd5());
	}

	/**
	 * 时效订单的时间为-
	 */
	@Test
	public void handle5() {
		TradeBo tradeBo = generateErrorTimingTradeBo(
			"tmallPromise." + TimingPromiseConstant.CONSIGN_TMALLPROMISE_NAME + "," + "tmallPlatformEstimate." + TimingPromiseConstant.ARRIVAL_TMALLPROMISE_NAME);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeHandleService.handle(generateTradeHandleBo(), tradeBo,
			"TDE111111");

		AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
		Assert.assertNotNull(ayTradeMain);
		AyTrade trade = tradeBo.getTrade();
//		Assert.assertEquals(trade.getTimingPromise(), ayTradeMain.getTimingPromise());
//		Assert.assertEquals(trade.getPromiseService(), ayTradeMain.getPromiseService());
//		Assert.assertEquals(TimingPromiseConstant.ARRIVAL_CONSIGN_TYPE_VALUE, ayTradeMain.getPromiseServiceType().intValue());
//		Assert.assertNull(ayTradeMain.getCollectTime());
//		Assert.assertNull(ayTradeMain.getDeliveryTime());
//		Assert.assertNull(ayTradeMain.getDispatchTime());
//		Assert.assertNull(ayTradeMain.getSignTime());
//		Assert.assertEquals(trade.getCutoffMinutes(), ayTradeMain.getCutoffMinutes().toString());
//		Assert.assertEquals(trade.getEsTime(), ayTradeMain.getEsTime().toString());
//		Assert.assertEquals(trade.getEsDate(), ayTradeMain.getEsDate());
//		Assert.assertEquals(trade.getEsRange(), ayTradeMain.getEsRange());
//		Assert.assertEquals(trade.getOsDate(), ayTradeMain.getOsDate());
//		Assert.assertEquals(trade.getOsRange(), ayTradeMain.getOsRange());
		Assert.assertEquals("MD5", ayTradeMain.getMergeMd5());
	}

	/**
	 * corpId为null
	 */
	@Test
	public void handle6() {
		TradeBo tradeBo = generateTradeBo();
		TradeHandleBo tradeHandleBo = generateTradeHandleBo();
		tradeHandleBo.setCorpId(null);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setCorpId("xxx");
		tradeHandleService.handle(tradeHandleBo,tradeBo,
			"TDE111111");
		Assert.assertNotNull(tradeBo.getAyTradeMain());
		Assert.assertNotNull(tradeBo.getAyTradeMain().getCorpId());
	}

	/**
	 * 检查StepTradeStatus是否正确
	 */
	@Test
	public void handle7() {
		TradeBo tradeBo = generateTradeBo();
		tradeBo.getTrade().setStepTradeStatus("FRONT_NOPAID_FINAL_NOPAID");
		TradeHandleBo tradeHandleBo = generateTradeHandleBo();
		tradeHandleBo.setCorpId(null);
		tradeBo.setAyTradeMain(null);
		AyTradeMain tradeMain = new AyTradeMain();
		tradeMain.setCorpId("xxx");
		tradeMain.setStepTradeStatus("FRONT_NOPAID_FINAL_NOPAID");
		tradeHandleService.handle(tradeHandleBo,tradeBo,
			"TDE111111");
		Assert.assertNotNull(tradeBo.getAyTradeMain());
		Assert.assertNotNull(tradeBo.getAyTradeMain().getCorpId());
		Assert.assertEquals("FRONT_NOPAID_FINAL_NOPAID", tradeBo.getAyTradeMain().getStepTradeStatus());
	}

	private AyTradeMain generateConsignLogistics() {
		AyTradeMain tradeConsignLogistics = new AyTradeMain();
		tradeConsignLogistics.setTid("11111111111");
		tradeConsignLogistics.setAyTid("TDE11111111111");
		return tradeConsignLogistics;
	}

	private TradeHandleBo generateTradeHandleBo() {
		TradeHandleBo tradeHandleBo = new TradeHandleBo();
		tradeHandleBo.setTid("11111111111");
		tradeHandleBo.setTrade(generateTrade());
		return tradeHandleBo;
	}

	private TradeBo generateTradeBo() {
		String s = "{\n"
			+ "    \"ayTradeConsignLogistics\": [\n"
			+ "        {\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"buyerEmail\": \"\",\n"
			+ "            \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "            \"codStatus\": \"NEW_CREATED\",\n"
			+ "            \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"hasYfx\": false,\n"
			+ "            \"invoiceNo\": \"804102410967024363\",\n"
			+ "            \"listId\": 90,\n"
			+ "            \"logisticsCompany\": \"圆通速递\",\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"receiverAddress\": \"江西省鹰潭市锦江镇东北街88号\",\n"
			+ "            \"receiverCity\": \"鹰潭市\",\n"
			+ "            \"receiverCountry\": \"\",\n"
			+ "            \"receiverDistrict\": \"其它区\",\n"
			+ "            \"receiverMobile\": \"021e1ea77bd91aaa0fc4d01a943a654e~~~MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4xoxkWIKLMHbw8GpBmQCPQ\",\n"
			+ "            \"receiverName\": \"5ZHB55DN54+m\",\n"
			+ "            \"receiverState\": \"江西省\",\n"
			+ "            \"receiverZip\": \"335000\",\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"serviceTags\": \"[{\\\"logisticServiceTagList\\\":[{\\\"serviceTag\\\":\\\"origAreaId=360682;consignDate=720\\\",\\\"serviceType\\\":\\\"TB_CONSIGN_DATE\\\"},{\\\"serviceTag\\\":\\\"lgType=-4\\\",\\\"serviceType\\\":\\\"FAST\\\"}],\\\"orderId\\\":\\\"260938254057663628\\\"},{\\\"logisticServiceTagList\\\":[{\\\"serviceTag\\\":\\\"consignDate=720\\\",\\\"serviceType\\\":\\\"TB_CONSIGN_DATE\\\"}],\\\"orderId\\\":\\\"260938254057663628\\\"}]\",\n"
			+ "            \"shippingType\": \"express\",\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"yfxFee\": 0\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"ayTradeExtTaobao\": {\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"listId\": 90,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"tid\": \"260938254057663628\"\n"
			+ "    },\n"
			+ "    \"ayTradeInvoiceIdinfo\": {\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"isInvoice\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"tid\": \"260938254057663628\"\n"
			+ "    },\n"
			+ "    \"ayTradeMain\": {\n"
			+ "        \"ayOrderType\": 1,\n"
			+ "        \"ayStatus\": 5,\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"created\": \"2019-01-03 22:02:30\",\n"
			+ "        \"encryptionType\": 1,\n"
			+ "        \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "        \"goodsNum\": 1,\n"
			+ "        \"isError\": false,\n"
			+ "        \"isO2oPassport\": false,\n"
			+ "        \"isRefund\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"mergeMd5\": \"7ACB31FECAA104483AFF385EF25B0E73\",\n"
			+ "        \"modified\": \"2019-01-25 21:29:52\",\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"orderTag\": 0,\n"
			+ "        \"orderType\": \"fixed\",\n"
			+ "        \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "        \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"shippingType\": \"express\",\n"
			+ "        \"skuNum\": 1,\n"
			+ "        \"stepTradeStatus\": \"NEW_CREATED\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": \"260938254057663628\",\n"
			+ "        \"tradeFrom\": \"WAP,WAP\"\n"
			+ "    },\n"
			+ "    \"ayTradePayment\": [\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"alipayPoint\": 0,\n"
			+ "            \"availableConfirmFee\": 0,\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "            \"buyerCodFee\": 0,\n"
			+ "            \"buyerObtainPointFee\": 0,\n"
			+ "            \"codFee\": 0,\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"couponFee\": 0,\n"
			+ "            \"creditCardFee\": 0,\n"
			+ "            \"discountFee\": 0,\n"
			+ "            \"hasPostFee\": true,\n"
			+ "            \"isCreditPay\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"oid\": \"0\",\n"
			+ "            \"orderTaxFee\": 0,\n"
			+ "            \"paidCouponFee\": 0,\n"
			+ "            \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"platformSubsidyFee\": 0,\n"
			+ "            \"pointFee\": 0,\n"
			+ "            \"postFee\": 0,\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"promotionDetails\": \"[{\\\"discountFee\\\":\\\"20.00\\\",\\\"id\\\":260938254057663628,\\\"promotionDesc\\\":\\\"粉丝专属价:省20.00元\\\",\\\"promotionId\\\":\\\"MZDZ33760-7623776011_56609136268\\\",\\\"promotionName\\\":\\\"粉丝专属价\\\"}]\",\n"
			+ "            \"realPointFee\": 0,\n"
			+ "            \"sellerCodFee\": 0,\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"stepPaidFee\": 0,\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"totalFee\": 59.9\n"
			+ "        },\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "            \"alipayPoint\": 0,\n"
			+ "            \"availableConfirmFee\": 0,\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "            \"buyerCodFee\": 0,\n"
			+ "            \"buyerObtainPointFee\": 0,\n"
			+ "            \"codFee\": 0,\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"couponFee\": 0,\n"
			+ "            \"creditCardFee\": 0,\n"
			+ "            \"discountFee\": 0,\n"
			+ "            \"hasPostFee\": true,\n"
			+ "            \"isCreditPay\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"orderTaxFee\": 0,\n"
			+ "            \"paidCouponFee\": 0,\n"
			+ "            \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"platformSubsidyFee\": 0,\n"
			+ "            \"pointFee\": 0,\n"
			+ "            \"postFee\": 0,\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"promotionDetails\": \"[{\\\"discountFee\\\":\\\"20.00\\\",\\\"id\\\":260938254057663628,\\\"promotionDesc\\\":\\\"粉丝专属价:省20.00元\\\",\\\"promotionId\\\":\\\"MZDZ33760-7623776011_56609136268\\\",\\\"promotionName\\\":\\\"粉丝专属价\\\"}]\",\n"
			+ "            \"realPointFee\": 0,\n"
			+ "            \"sellerCodFee\": 0,\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"stepPaidFee\": 0,\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"totalFee\": 59.9\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"ayTradeSearch\": {\n"
			+ "        \"abbreviationNick\": \"\",\n"
			+ "        \"ayOrderType\": 1,\n"
			+ "        \"ayStatus\": 5,\n"
			+ "        \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "        \"buyerNick\": \"hejinghan20131415\",\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "        \"corpId\": \"1056865090\",\n"
			+ "        \"created\": \"2019-01-26T16:23:20.121\",\n"
			+ "        \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "        \"goodsNum\": 1,\n"
			+ "        \"invoiceNos\": \"804102410967024363\",\n"
			+ "        \"isError\": false,\n"
			+ "        \"isInvoice\": false,\n"
			+ "        \"isO2oPassport\": false,\n"
			+ "        \"isRefund\": false,\n"
			+ "        \"listId\": 90,\n"
			+ "        \"logisticsCompany\": \"圆通速递\",\n"
			+ "        \"mergeMd5\": \"7ACB31FECAA104483AFF385EF25B0E73\",\n"
			+ "        \"modified\": \"2019-01-25 21:29:52\",\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"orderTag\": 0,\n"
			+ "        \"orderType\": \"fixed\",\n"
			+ "        \"outerIids\": \"VV6036\",\n"
			+ "        \"payTime\": \"2019-01-03 22:02:33\",\n"
			+ "        \"receiver\": \"null|15879927779|江西省鹰潭市锦江镇东北街88号|江西省|鹰潭市|其它区||null|周琴珍\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerId\": \"1056865090\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"skuNum\": 1,\n"
			+ "        \"stepTradeStatus\": \"NEW_CREATED\",\n"
			+ "        \"storeId\": \"TAO\",\n"
			+ "        \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": \"260938254057663628\",\n"
			+ "        \"titles\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\"\n"
			+ "    },\n"
			+ "    \"ayTradeSubOrderList\": [\n"
			+ "        {\n"
			+ "            \"adjustFee\": 0,\n"
			+ "            \"ayOid\": \"TDE1901041204470000004026827361\",\n"
			+ "            \"ayStatus\": 5,\n"
			+ "            \"ayTid\": \"TDE190104120447000000402682736\",\n"
			+ "            \"buyerRate\": true,\n"
			+ "            \"cid\": 50007003,\n"
			+ "            \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "            \"corpId\": \"1056865090\",\n"
			+ "            \"created\": \"2019-01-03 22:02:30\",\n"
			+ "            \"discountFee\": 20,\n"
			+ "            \"endTime\": \"2019-01-23 01:11:28\",\n"
			+ "            \"fqgNum\": 0,\n"
			+ "            \"invoiceNo\": \"804102410967024363\",\n"
			+ "            \"isFqgSFee\": false,\n"
			+ "            \"isOversold\": false,\n"
			+ "            \"listId\": 90,\n"
			+ "            \"logisticsCompany\": \"圆通速递\",\n"
			+ "            \"mdFee\": 0,\n"
			+ "            \"num\": 1,\n"
			+ "            \"numIid\": \"************\",\n"
			+ "            \"oid\": \"260938254057663628\",\n"
			+ "            \"outerIid\": \"VV6036\",\n"
			+ "            \"outerSkuId\": \"VV6036\",\n"
			+ "            \"payment\": 39.9,\n"
			+ "            \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "            \"price\": 59.9,\n"
			+ "            \"refundStatus\": \"NO_REFUND\",\n"
			+ "            \"sellerId\": \"1056865090\",\n"
			+ "            \"sellerNick\": \"viyayaya\",\n"
			+ "            \"sellerRate\": false,\n"
			+ "            \"sellerType\": \"C\",\n"
			+ "            \"shippingType\": \"express\",\n"
			+ "            \"skuId\": \"4130555350550\",\n"
			+ "            \"skuPropertiesName\": \"颜色分类:粉色[预售12-15天]\",\n"
			+ "            \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "            \"storeId\": \"TAO\",\n"
			+ "            \"taoOrderFrom\": \"WAP,WAP\",\n"
			+ "            \"taoStatus\": \"TRADE_FINISHED\",\n"
			+ "            \"tid\": \"260938254057663628\",\n"
			+ "            \"title\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\",\n"
			+ "            \"totalFee\": 39.9\n"
			+ "        }\n"
			+ "    ],\n"
			+ "    \"encryptionType\": 1,\n"
			+ "    \"endPoint\": false,\n"
			+ "    \"originDecryptData\": {\n"
			+ "        \"buyerAlipayNo\": \"158****7779\",\n"
			+ "        \"buyerNick\": \"hejinghan20131415\",\n"
			+ "        \"receiverMobile\": \"15879927779\",\n"
			+ "        \"receiverName\": \"周琴珍\"\n"
			+ "    },\n"
			+ "    \"sellerId\": \"1056865090\",\n"
			+ "    \"storeId\": \"TAO\",\n"
			+ "    \"tid\": \"260938254057663628\",\n"
			+ "    \"trade\": {\n"
			+ "        \"adjustFee\": \"0.00\",\n"
			+ "        \"alipayNo\": \"2019010322001141710571840347\",\n"
			+ "        \"alipayPoint\": 0,\n"
			+ "        \"availableConfirmFee\": \"0.00\",\n"
			+ "        \"buyerAlipayNo\": \"Sg==Tg==UQ==Qw==Qw==Qw==Qw==UA==UA==UA==Ug==\",\n"
			+ "        \"buyerArea\": \"江西电信\",\n"
			+ "        \"buyerCodFee\": \"0.00\",\n"
			+ "        \"buyerEmail\": \"\",\n"
			+ "        \"buyerNick\": \"gQ==fg==gw==gg==hw==gA==gQ==eg==hw==Sw==SQ==Sg==TA==Sg==TQ==Sg==Tg==\",\n"
			+ "        \"buyerObtainPointFee\": 0,\n"
			+ "        \"buyerRate\": true,\n"
			+ "        \"codFee\": \"0.00\",\n"
			+ "        \"codStatus\": \"NEW_CREATED\",\n"
			+ "        \"commissionFee\": \"0.00\",\n"
			+ "        \"consignTime\": 1547638032000,\n"
			+ "        \"couponFee\": 0,\n"
			+ "        \"created\": 1546524150000,\n"
			+ "        \"discountFee\": \"0.00\",\n"
			+ "        \"endTime\": 1548177088000,\n"
			+ "        \"hasPostFee\": true,\n"
			+ "        \"hasYfx\": false,\n"
			+ "        \"is3D\": false,\n"
			+ "        \"isBrandSale\": false,\n"
			+ "        \"isDaixiao\": false,\n"
			+ "        \"isForceWlb\": false,\n"
			+ "        \"isGift\": false,\n"
			+ "        \"isLgtype\": false,\n"
			+ "        \"isPartConsign\": false,\n"
			+ "        \"isShShip\": false,\n"
			+ "        \"isWt\": false,\n"
			+ "        \"modified\": 1548422992000,\n"
			+ "        \"newPresell\": false,\n"
			+ "        \"nrShopGuideId\": \"\",\n"
			+ "        \"nrShopGuideName\": \"\",\n"
			+ "        \"num\": 1,\n"
			+ "        \"numIid\": ************,\n"
			+ "        \"orders\": [\n"
			+ "            {\n"
			+ "                \"adjustFee\": \"0.00\",\n"
			+ "                \"buyerRate\": true,\n"
			+ "                \"cid\": 50007003,\n"
			+ "                \"consignTime\": \"2019-01-16 19:27:12\",\n"
			+ "                \"discountFee\": \"20.00\",\n"
			+ "                \"divideOrderFee\": \"39.90\",\n"
			+ "                \"endTime\": 1548177088000,\n"
			+ "                \"invoiceNo\": \"804102410967024363\",\n"
			+ "                \"isDaixiao\": false,\n"
			+ "                \"isOversold\": false,\n"
			+ "                \"logisticsCompany\": \"圆通速递\",\n"
			+ "                \"nrOuterIid\": \"VV6036\",\n"
			+ "                \"num\": 1,\n"
			+ "                \"numIid\": ************,\n"
			+ "                \"oid\": 260938254057663628,\n"
			+ "                \"oidStr\": \"260938254057663628\",\n"
			+ "                \"orderFrom\": \"WAP,WAP\",\n"
			+ "                \"outerIid\": \"VV6036\",\n"
			+ "                \"outerSkuId\": \"VV6036\",\n"
			+ "                \"payment\": \"39.90\",\n"
			+ "                \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "                \"price\": \"59.90\",\n"
			+ "                \"refundStatus\": \"NO_REFUND\",\n"
			+ "                \"sellerRate\": false,\n"
			+ "                \"sellerType\": \"C\",\n"
			+ "                \"shippingType\": \"express\",\n"
			+ "                \"skuId\": \"4130555350550\",\n"
			+ "                \"skuPropertiesName\": \"颜色分类:粉色[预售12-15天]\",\n"
			+ "                \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "                \"status\": \"TRADE_FINISHED\",\n"
			+ "                \"title\": \"1.3薇娅定制儿童款新年猪猪围巾 VV6036\",\n"
			+ "                \"totalFee\": \"39.90\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"payTime\": 1546524153000,\n"
			+ "        \"payment\": \"39.90\",\n"
			+ "        \"pccAf\": 0,\n"
			+ "        \"picPath\": \"https://img.alicdn.com/bao/uploaded/i1/1056865090/O1CN01DcQiun1nTGSu8HyY6_!!1056865090.jpg\",\n"
			+ "        \"platformSubsidyFee\": \"0.00\",\n"
			+ "        \"pointFee\": 0,\n"
			+ "        \"postFee\": \"0.00\",\n"
			+ "        \"price\": \"59.90\",\n"
			+ "        \"promotionDetails\": [\n"
			+ "            {\n"
			+ "                \"discountFee\": \"20.00\",\n"
			+ "                \"id\": 260938254057663628,\n"
			+ "                \"promotionDesc\": \"粉丝专属价:省20.00元\",\n"
			+ "                \"promotionId\": \"MZDZ33760-7623776011_56609136268\",\n"
			+ "                \"promotionName\": \"粉丝专属价\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"realPointFee\": 0,\n"
			+ "        \"receivedPayment\": \"39.90\",\n"
			+ "        \"receiverAddress\": \"江西省鹰潭市锦江镇东北街88号\",\n"
			+ "        \"receiverCity\": \"鹰潭市\",\n"
			+ "        \"receiverCountry\": \"\",\n"
			+ "        \"receiverDistrict\": \"其它区\",\n"
			+ "        \"receiverMobile\": \"021e1ea77bd91aaa0fc4d01a943a654e~~~MDAwMDAwMDAwMDAwMDAwMDM0YWJjZGVmZ2hpamtsbW4xoxkWIKLMHbw8GpBmQCPQ\",\n"
			+ "        \"receiverName\": \"5ZHB55DN54+m\",\n"
			+ "        \"receiverState\": \"江西省\",\n"
			+ "        \"receiverZip\": \"335000\",\n"
			+ "        \"sellerAlipayNo\": \"***<EMAIL>\",\n"
			+ "        \"sellerCanRate\": true,\n"
			+ "        \"sellerCodFee\": \"0.00\",\n"
			+ "        \"sellerEmail\": \"\",\n"
			+ "        \"sellerFlag\": 0,\n"
			+ "        \"sellerMobile\": \"18988853116\",\n"
			+ "        \"sellerName\": \"张昌荣\",\n"
			+ "        \"sellerNick\": \"viyayaya\",\n"
			+ "        \"sellerRate\": false,\n"
			+ "        \"serviceTags\": [\n"
			+ "            {\n"
			+ "                \"logisticServiceTagList\": [\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"origAreaId=360682;consignDate=720\",\n"
			+ "                        \"serviceType\": \"TB_CONSIGN_DATE\"\n"
			+ "                    },\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"lgType=-4\",\n"
			+ "                        \"serviceType\": \"FAST\"\n"
			+ "                    }\n"
			+ "                ],\n"
			+ "                \"orderId\": \"260938254057663628\"\n"
			+ "            },\n"
			+ "            {\n"
			+ "                \"logisticServiceTagList\": [\n"
			+ "                    {\n"
			+ "                        \"serviceTag\": \"consignDate=720\",\n"
			+ "                        \"serviceType\": \"TB_CONSIGN_DATE\"\n"
			+ "                    }\n"
			+ "                ],\n"
			+ "                \"orderId\": \"260938254057663628\"\n"
			+ "            }\n"
			+ "        ],\n"
			+ "        \"serviceType\": \"\",\n"
			+ "        \"shippingType\": \"express\",\n"
			+ "        \"sid\": \"260938254057663628\",\n"
			+ "        \"snapshotUrl\": \"o:260938254057663628_1\",\n"
			+ "        \"status\": \"TRADE_FINISHED\",\n"
			+ "        \"tid\": 260938254057663628,\n"
			+ "        \"tidStr\": \"260938254057663628\",\n"
			+ "        \"title\": \"薇娅viya饰品店\",\n"
			+ "        \"totalFee\": \"59.90\",\n"
			+ "        \"tradeFrom\": \"WAP,WAP\",\n"
			+ "        \"type\": \"fixed\",\n"
			+ "        \"youXiang\": false\n"
			+ "    }\n"
			+ "}";
		TradeBo tradeBo = JSON.parseObject(s, TradeBo.class);
		tradeBo.setMergeMd5("MD5");
		return tradeBo;
	}

	private TradeBo generateTimingTradeBo(String promiseService) {
		TradeBo tradeBo = generateTradeBo();
		AyTrade trade = tradeBo.getTrade();
		trade.setTimingPromise("tmallPromise");
		trade.setPromiseService(promiseService);
		trade.setCutoffMinutes("960");
		trade.setCollectTime("2019-07-03 23:59:59");
		trade.setDeliveryTime("2019-07-02 23:59:59");
		trade.setDispatchTime("2019-07-04 23:59:59");
		trade.setSignTime("2019-07-04 23:59:00");
		trade.setEsTime("1");
		trade.setEsDate("2019-04-12");
		trade.setEsRange("09:00-21:00");
		trade.setOsDate("2019-04-13");
		trade.setOsRange("09:00-22:00");
		tradeBo.setMergeMd5("MD5");
		return tradeBo;
	}

	private TradeBo generateErrorTimingTradeBo(String promiseService) {
		TradeBo tradeBo = generateTradeBo();
		AyTrade trade = tradeBo.getTrade();
		trade.setTimingPromise("tmallPromise");
		trade.setPromiseService(promiseService);
		trade.setCutoffMinutes("960");
		trade.setCollectTime("-");
		trade.setDeliveryTime("-");
		trade.setDispatchTime("-");
		trade.setSignTime("-");
		trade.setEsTime("1");
		trade.setEsDate("2019-04-12");
		trade.setEsRange("09:00-21:00");
		trade.setOsDate("2019-04-13");
		trade.setOsRange("09:00-22:00");
		tradeBo.setMergeMd5("MD5");
		return tradeBo;
	}

	private AyTrade generateTrade() {
		AyTrade trade = new AyTrade();
		trade.setTid(11111111111L);
		List<Order> ol = new ArrayList<>();
		Order o = new Order();
		o.setOid(11111111111L);
		o.setOidStr("11111111111");
		ol.add(o);
		trade.setOrders(ol);
		return trade;
	}

	@Test
	public void tradeStatusConvertTag() {
		int tag = 0;
		tag = tradeHandleService.tradeStatusConvertTag(TaobaoStatusConstant.SELLER_CONSIGNED_PART, tag);
		Assert.assertEquals(AyTradeMain.SELLER_CONSIGNED_PART_TAG, tag);
	}

	@Test
	public void tradeStatusConvertTag1() {
		int tag = 0;
		tag = tradeHandleService.tradeStatusConvertTag(TaobaoStatusConstant.TRADE_BUYER_SIGNED, tag);
		Assert.assertEquals(AyTradeMain.TRADE_BUYER_SIGNED_TAG, tag);
	}

	@Test
	public void tradeStatusConvertTag2() {
		int tag = 0;
		tag = tradeHandleService.tradeStatusConvertTag(TaobaoStatusConstant.PAID_FORBID_CONSIGN, tag);
		Assert.assertEquals(AyTradeMain.FREEZE_SELLER_GOODS_TAG, tag);
	}

	@Test
	public void tradeStatusConvertTag3() {
		int tag = AyTradeMain.FREEZE_SELLER_GOODS_TAG;
		tag = tradeHandleService.tradeStatusConvertTag(TaobaoStatusConstant.WAIT_BUYER_PAY, tag);
		Assert.assertEquals(AyTradeMain.FREEZE_SELLER_GOODS_TAG, tag);
	}

	@Test
	public void tradeStatusConvertTag4() {
		int tag = AyTradeMain.SELLER_GOODS_FAILED_TAG;
		tag = tradeHandleService.tradeStatusConvertTag(TaobaoStatusConstant.WAIT_BUYER_PAY, tag);
		Assert.assertEquals(AyTradeMain.SELLER_GOODS_FAILED_TAG, tag);
	}
}
