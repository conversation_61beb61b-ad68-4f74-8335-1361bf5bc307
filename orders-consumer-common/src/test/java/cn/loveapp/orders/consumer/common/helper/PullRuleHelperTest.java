package cn.loveapp.orders.consumer.common.helper;

import cn.loveapp.orders.common.constant.PullHistoryRuleConstant;
import cn.loveapp.orders.common.utils.DateUtil;

import java.time.LocalDateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Created by: IntelliJ IDEA.
 * @description: ${description}
 * @authr: jason
 * @date: 2019-01-26
 * @time: 18:45
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {PullRuleHelperTest.class, MetricsAutoConfiguration.class,
		CompositeMeterRegistryAutoConfiguration.class, PullRuleHelper.class
	})
public class PullRuleHelperTest {

	@SpyBean
	private PullRuleHelper pullRuleHelper;

	@Test
	public void convertRuleToDate() {
		String ruleAttr = PullHistoryRuleConstant.PULL_LATEST_ORDERS;
		pullRuleHelper.setPullRuleLastest(1L);
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime dt = pullRuleHelper.convertRuleToDate(ruleAttr, now);
		LocalDateTime d = DateUtil.calculateCustomDate(now, 1L);
		Assert.assertEquals(dt, d);
	}

	@Test
	public void convertRuleToDate1() {
		String ruleAttr = PullHistoryRuleConstant.PULL_NEARLY_THREE_MONTHS_ORDERS;
		pullRuleHelper.setPullRuleNearlyThreeMonths(1L);
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime dt = pullRuleHelper.convertRuleToDate(ruleAttr, now);
		LocalDateTime d = DateUtil.calculateCustomDate(now, 1L);
		Assert.assertEquals(dt, d);
	}

	@Test
	public void convertRuleToDate2() {
		String ruleAttr = "";
		pullRuleHelper.setPullRuleLastest(1L);
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime dt = pullRuleHelper.convertRuleToDate(ruleAttr, now);
		LocalDateTime d = DateUtil.calculateCustomDate(now, 1L);
		Assert.assertEquals(dt, d);
	}
}
