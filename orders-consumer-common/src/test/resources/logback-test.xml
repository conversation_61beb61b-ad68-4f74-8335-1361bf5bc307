<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scanPeriod="60 seconds">
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>[%d{HH:mm:ss}] [%level] [%logger{1.}] %msg%n</pattern>
		</encoder>
	</appender>

	<logger name="cn.loveapp" level="debug" additivity="false">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="org.mybatis" level="debug" additivity="false">
		<appender-ref ref="STDOUT"/>
	</logger>

	<root level="info">
		<appender-ref ref="STDOUT"/>
	</root>
</configuration>
