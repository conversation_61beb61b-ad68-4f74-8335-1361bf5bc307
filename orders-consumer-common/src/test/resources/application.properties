logging.config=classpath:logback-test.xml

mybatis.configuration.mapUnderscoreToCamelCase=true
mybatis.mapper-locations=classpath:mapper/*.xml


orders.sms.enable=true
orders.taobao.ons.sms.topic = smstopic
orders.taobao.ons.sms.tag = smstag
orders.taobao.ons.sms.consumerurl = smsconsumerurl
orders.taobao.taobaoReportServerUrl=http://mockgw.hz.taeapp.com/gw


spring.main.banner-mode=off

orders.batch.dir=/tmp/test

## mycat
#orders.orders.datasource.url=******************************************************************************************************************************************
#orders.orders.datasource.username=root
#orders.orders.datasource.password=JbcZjTyl
#orders.orders.datasource.driverClassName=com.mysql.jdbc.Driver
#orders.orders.datasource.type=com.alibaba.druid.pool.DruidDataSource
#orders.orders.datasource.initial-size = 20
#orders.orders.datasource.max-active = 100
#orders.orders.datasource.min-idle = 10
#orders.orders.datasource.max-wait= 60000
#orders.orders.datasource.name= ordersDatasource

## è®¢ååºæ°æ®æº(ç´è¿) orders_error_package  orders_package_listen_
orders.orders.datasource.url=************************************************************
orders.orders.datasource.username=aiyong
orders.orders.datasource.password=JbcZjTyl
orders.orders.datasource.driverClassName=org.postgresql.Driver
orders.orders.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.orders.datasource.initial-size = 5
orders.orders.datasource.max-active = 50
orders.orders.datasource.min-idle = 10
orders.orders.datasource.max-wait= 60000
orders.orders.datasource.name=ordersDataSource

# è®¢ååºæ°æ®æº trade_list_
orders.order.datasource.url=***********************************************************
orders.order.datasource.username=aiyong
orders.order.datasource.password=JbcZjTyl
orders.order.datasource.driverClassName=org.postgresql.Driver
orders.order.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.order.datasource.min-idle=1
orders.order.datasource.initial-size=5
orders.order.datasource.max-active=50
orders.order.datasource.max-wait=60000
orders.order.datasource.name= tradeDatasource

#ç¨æ·ç±ç¨ä¿¡æ¯è¡¨æ°æ®æº user_productinfo_trade
orders.dream.datasource.url = ************************************************************************************************************************************************************
orders.dream.datasource.username = root
orders.dream.datasource.password = JbcZjTyl
orders.dream.datasource.driverClassName = com.mysql.jdbc.Driver
orders.dream.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.dream.datasource.min-idle=5
orders.dream.datasource.initial-size=5
orders.dream.datasource.max-active=50
orders.dream.datasource.max-wait=60000
orders.dream.datasource.name= dreamDatasource

#å¼å¸¸è®¢åè§åè¡¨æ°æ®æº trade_orders_rule
orders.print.datasource.url = ***************************************************************************************************************************************************************
orders.print.datasource.username = root
orders.print.datasource.password = JbcZjTyl
orders.print.datasource.driverClassName = com.mysql.jdbc.Driver
orders.print.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.print.datasource.min-idle=5
orders.print.datasource.initial-size=50
orders.print.datasource.max-active=100
orders.print.datasource.max-wait=60000
orders.print.datasource.name= printDatasource

#ons aliyun application
orders.taobao.aliyun.ons.access-key = LTAIwwRwPsnmL4Dq
orders.taobao.aliyun.ons.srcret-key = 5uE58zJxFHR2AIGmQMtuSSfmVHI1Pr
orders.taobao.aliyun.ons.channel = CLOUD
#ons delay order data
orders.taobao.aliyun.ons.delay.topic = iytmctradenotify
orders.taobao.aliyun.ons.delay.producerid = PID_21081218
orders.taobao.aliyun.ons.delay.consumerid = CID_21085840-106
orders.taobao.aliyun.ons.delay.max-thread-num = 50
orders.taobao.aliyun.ons.delay.delay-time = 30000
orders.taobao.aliyun.ons.delay.consume-timeout = 1200
orders.taobao.aliyun.ons.delay.tag = *

