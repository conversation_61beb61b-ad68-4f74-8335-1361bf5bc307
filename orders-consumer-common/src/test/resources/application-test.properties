logging.config=classpath:logback-test.xml

mybatis.configuration.mapUnderscoreToCamelCase=true
mybatis.mapper-locations=classpath:mapper/*.xml


orders.sms.enable=true
orders.taobao.ons.sms.topic = smstopic
orders.taobao.ons.sms.tag = smstag
orders.taobao.ons.sms.consumerurl = smsconsumerurl


spring.main.banner-mode=off

orders.batch.dir=/tmp/test

orders.taobao.taobaoReportServerUrl=http://mockgw.hz.taeapp.com/gw


## mycat
#orders.orders.datasource.url=******************************************************************************************************************************************
#orders.orders.datasource.username=root
#orders.orders.datasource.password=JbcZjTyl
#orders.orders.datasource.driverClassName=com.mysql.jdbc.Driver
#orders.orders.datasource.type=com.alibaba.druid.pool.DruidDataSource
#orders.orders.datasource.initial-size = 20
#orders.orders.datasource.max-active = 100
#orders.orders.datasource.min-idle = 10
#orders.orders.datasource.max-wait= 60000
#orders.orders.datasource.name= ordersDatasource

## è®¢ååºæ°æ®æº(ç´è¿) orders_error_package  orders_package_listen_
orders.orders.datasource.url=************************************************************
orders.orders.datasource.username=aiyong
orders.orders.datasource.password=JbcZjTyl
orders.orders.datasource.driverClassName=org.postgresql.Driver
orders.orders.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.orders.datasource.initial-size = 5
orders.orders.datasource.max-active = 50
orders.orders.datasource.min-idle = 10
orders.orders.datasource.max-wait= 60000
orders.orders.datasource.name=ordersDataSource

# è®¢ååºæ°æ®æº trade_list_
orders.order.datasource.url=***********************************************************
orders.order.datasource.username=aiyong
orders.order.datasource.password=JbcZjTyl
orders.order.datasource.driverClassName=org.postgresql.Driver
orders.order.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.order.datasource.min-idle=1
orders.order.datasource.initial-size=5
orders.order.datasource.max-active=50
orders.order.datasource.max-wait=60000
orders.order.datasource.name= tradeDatasource

#ç¨æ·ç±ç¨ä¿¡æ¯è¡¨æ°æ®æº user_productinfo_trade
orders.dream.datasource.url = ************************************************************************************************************************************************************
orders.dream.datasource.username = root
orders.dream.datasource.password = JbcZjTyl
orders.dream.datasource.driverClassName = com.mysql.jdbc.Driver
orders.dream.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.dream.datasource.min-idle=5
orders.dream.datasource.initial-size=5
orders.dream.datasource.max-active=50
orders.dream.datasource.max-wait=60000
orders.dream.datasource.name= dreamDatasource

#å¼å¸¸è®¢åè§åè¡¨æ°æ®æº trade_orders_rule
orders.print.datasource.url = ***************************************************************************************************************************************************************
orders.print.datasource.username = root
orders.print.datasource.password = JbcZjTyl
orders.print.datasource.driverClassName = com.mysql.jdbc.Driver
orders.print.datasource.type=com.alibaba.druid.pool.DruidDataSource
orders.print.datasource.min-idle=5
orders.print.datasource.initial-size=50
orders.print.datasource.max-active=100
orders.print.datasource.max-wait=60000
orders.print.datasource.name= printDatasource

#ons application
orders.taobao.ons.access-key = 21085840
orders.taobao.ons.srcret-key = a5ed0811cac2d21e1c21e0dc56d652b8
orders.taobao.ons.channel = CLOUD

#ons pull rds
orders.taobao.ons.vip.rds.topic = iysaverdsorders
orders.taobao.ons.vip.rds.producerid = PID_20181121
orders.taobao.ons.vip.rds.consumerid = CID_20181121-101
orders.taobao.ons.vip.rds.max-thread-num = 50
orders.taobao.ons.vip.rds.tag = "*"

#ons pull taobao refund
orders.taobao.ons.refund.topic = iyrefundorders
orders.taobao.ons.refund.producerid = PID_20181127
orders.taobao.ons.refund.consumerid = CID_20181127-101
orders.taobao.ons.refund.max-thread-num = 50
orders.taobao.ons.refund.tag = *

#ons pull taobao history fullinfo
orders.taobao.ons.history.topic = iyapifullinfoorders
orders.taobao.ons.history.producerid = PID_20181210
orders.taobao.ons.history.consumerid = CID_20181210-101
orders.taobao.ons.history.max-thread-num = 50
orders.taobao.ons.history.tag = *

#ons pull taobao soldget
orders.taobao.ons.soldget.topic = iyapisoldgetorders
orders.taobao.ons.soldget.producerid = PID_20181210
orders.taobao.ons.soldget.consumerid = CID_20181210-101
orders.taobao.ons.soldget.max-thread-num = 50
orders.taobao.ons.soldget.tag = *

