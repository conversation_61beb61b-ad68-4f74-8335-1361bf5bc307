package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.*;

import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcTradeExt1688;
import cn.loveapp.orders.consumer.common.bo.TradeBo;

/**
 * Ali1688OrderSavingPlatformHandleServiceImpl
 *
 * <AUTHOR>
 * @date 2021/4/7
 */
@Service
public class Ali1688OrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService{
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(Ali1688OrderSavingPlatformHandleServiceImpl.class);

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
        tradeSearch.setOaid(trade.getOaid());
        TcTradeExt1688 ali1688TradeExt = trade.getAli1688TradeExt();
        tradeSearch.addServiceType(generalServiceType(trade));
        if (Objects.isNull(ali1688TradeExt)) {
            appendSearchESNumIid(trade, tradeSearch);
            return;
        }
        List<TcTradeExt1688.SubItem> subItems = ali1688TradeExt.getSubItems();
        tradeSearch.setHasCustomizationService(generalHasCustomizationService(subItems));
    }

    @Override
    public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
        TcTradeExt1688 ali1688TradeExt = trade.getAli1688TradeExt();
        if (Objects.isNull(ali1688TradeExt)) {
            return;
        }
        List<TcTradeExt1688.SubItem> subItems = ali1688TradeExt.getSubItems();
        ayTradeMain.setHasCustomizationService(generalHasCustomizationService(subItems));
    }

    private ArrayList<Boolean> generalHasCustomizationService(List<TcTradeExt1688.SubItem> subItems) {
        if (CollectionUtils.isEmpty(subItems)) {
           return null;
        }

        Set<Boolean> hasCustomizationServiceSet = new HashSet<>();
        subItems.forEach(subItem -> {
            if (StringUtils.isNotEmpty(subItem.getFreeEntryServiceInfo()) || CollectionUtils.isNotEmpty(subItem.getPayEntryServiceInfo())) {
                hasCustomizationServiceSet.add(Boolean.TRUE);
            } else {
                hasCustomizationServiceSet.add(Boolean.FALSE);
            }
        });

        return ElasticsearchUtil.toList(hasCustomizationServiceSet);
    }

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        // 1688不需要存储脱敏字段 直接返回
        return;
    }

    @Override
	public TcTradeExt1688 generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
		return tradeBo.getTrade().getAli1688TradeExt();
	}

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
        String ayTid, AyTrade trade, String platformId, String appName) {
        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        appendSubOrderNumIid(trade, subOrderList);
    }

    @Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}
}
