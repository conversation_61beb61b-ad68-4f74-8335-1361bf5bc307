package cn.loveapp.orders.consumer.common.controller;

import cn.loveapp.orders.common.controller.BaseCloudNativeController;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.sql.DataSource;
import java.util.Map;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
@Controller
@RequestMapping("/")
public class CloudNativeController extends BaseCloudNativeController {
	@Autowired
	@Qualifier("stringRedisTemplate")
	private StringRedisTemplate rdsStringRedisTemplate;

	@Autowired
	@Qualifier("userInfoStringRedisTemplate")
	private StringRedisTemplate userInfoStringRedisTemplate;

	public CloudNativeController(ObjectProvider<Map<String, DataSource>> provider){
		super(provider);
	}

	@Override
	protected HttpStatus checkReadNess(){
		if (!PONG.equalsIgnoreCase(rdsStringRedisTemplate.execute(RedisConnectionCommands::ping, true)) ||
				!PONG.equalsIgnoreCase(userInfoStringRedisTemplate.execute(RedisConnectionCommands::ping, true))) {
			return HttpStatus.INTERNAL_SERVER_ERROR;
		}
		return super.checkReadNess();
	}
}
