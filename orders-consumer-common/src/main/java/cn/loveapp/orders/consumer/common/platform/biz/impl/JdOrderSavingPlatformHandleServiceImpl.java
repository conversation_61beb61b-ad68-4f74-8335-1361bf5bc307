package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.*;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 京东 订单存储 处理服务
 * <AUTHOR>
 * @date 2025/2/19 17:13
 */
@Service
public class JdOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(JdOrderSavingPlatformHandleServiceImpl.class);

	/**
	 * 暂停订单状态
	 */
	private static final List<String> PAUSE_STATUS_LIST = Lists.newArrayList("PAUSE", "ZanTing");

	/**
	 * 配送退货订单状态
	 */
	private static final List<String> DELIVERY_RETURN_STATUS_LIST = Lists.newArrayList("DELIVERY_RETURN", "PeiSongTuiHuo");

	/**
	 * 锁定订单状态
	 */
	private static final String LOCKED_STATUS = "LOCKED";

	private static final String JC_ORDER = "京仓订单";
	private static final String YC_ORDER = "云仓订单";

    /**
     * 京东送礼单
     */
	private static final String GIFT_ORDER = "\"1209\":\"1\"";
    /**
     * 京东收礼单
     */
	private static final String RECEIVE_GIFT_ORDER = "\"1217\":\"1\"";


	@Override
	public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {
		// 京东不返回发货时间，此处做兼容
		AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
		AyTrade trade = tradeBo.getTrade();
		if (lastAyTradeMain == null || lastAyTradeMain.getConsignTime() == null) {
			// 数据库不存在，但本次入库为已发货，将更新时间作为发货时间
			List<Order> orders = trade.getOrders();
			Date modified = trade.getModified();
			for (Order order : orders) {
				if (TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS.equals(order.getStatus())) {
					order.setConsignTime(DateUtil.convertDateToString(modified));
				}
			}
			if (TaobaoStatusConstant.WAIT_BUYER_CONFIRM_GOODS.equals(trade.getStatus())) {
				trade.setConsignTime(modified);
			}
		}

	}

	@Override
	public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
		ayTradeMain.addServiceType(ElasticsearchUtil.toList(getServiceTypeSet(trade)));
	}


	@Override
	public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch, Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
		tradeSearch.addServiceType(ElasticsearchUtil.toList(getServiceTypeSet(trade)));
        tradeSearch.setParentOrderId(trade.getParentOrderId());
	}

	@Override
	public TcTradeExtJd generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getJdTradeExt();
	}

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();

		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();

		// 京东手机号返回每次都在变，忽略md5统计方便合单（自动合单会在调用oaid校验接口判断合单）
		return orderMergePlatformHandleService.generateMergeMd5(
				buyerNick,
				trade.getReceiverCountry(),
				trade.getReceiverState(),
				trade.getReceiverCity(),
				trade.getReceiverDistrict(),
				receiverAddress,
				trade.getReceiverZip(),
				receiverName,
				null,
				null,
				platformId, appName);
	}


    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
	}

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_JD;
    }


	private Set<String> getServiceTypeSet(AyTrade ayTrade) {
		if (ayTrade == null) {
			return null;
		}

		Set<String> serviceTypeSet = new HashSet<>();
		TcTradeExtJd jdTradeExt = ayTrade.getJdTradeExt();
		if (jdTradeExt == null) {
			serviceTypeSet.addAll(generalServiceType(ayTrade));
			return serviceTypeSet;
		}

		String orderState = jdTradeExt.getOrderState();
		if (PAUSE_STATUS_LIST.contains(orderState)) {
			serviceTypeSet.add(OrderServiceType.pause.name());
		} else if (LOCKED_STATUS.equals(orderState)) {
			serviceTypeSet.add(OrderServiceType.locked.name());
		} else if (DELIVERY_RETURN_STATUS_LIST.contains(orderState)) {
			serviceTypeSet.add(OrderServiceType.delivery_return.name());
		}

		String storeOrder = jdTradeExt.getStoreOrder();
		if (StringUtils.isNotEmpty(storeOrder)) {
			if (YC_ORDER.equals(storeOrder)) {
				serviceTypeSet.add(OrderServiceType.yc_order.name());
			} else if (JC_ORDER.equals(storeOrder)) {
				serviceTypeSet.add(OrderServiceType.jc_order.name());
			}
		}

        String sendPayMap = jdTradeExt.getSendpayMap();
        if (StringUtils.isNotEmpty(sendPayMap)) {
            if (sendPayMap.contains(GIFT_ORDER)) {
                serviceTypeSet.add(OrderServiceType.gift_order.name());
            }

            if (sendPayMap.contains(RECEIVE_GIFT_ORDER)) {
                serviceTypeSet.add(OrderServiceType.receive_gift_order.name());
            }
        }

        serviceTypeSet.addAll(generalServiceType(ayTrade));
		return serviceTypeSet;
	}
}
