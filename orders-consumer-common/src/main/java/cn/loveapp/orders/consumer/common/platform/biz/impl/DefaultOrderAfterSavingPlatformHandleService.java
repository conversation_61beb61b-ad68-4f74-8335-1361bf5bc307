package cn.loveapp.orders.consumer.common.platform.biz.impl;


import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import org.springframework.stereotype.Service;

/**
 * 订单保存后续操作 default
 *
 * <AUTHOR>
 * @Date 2023/6/2 14:50
 */
@Service
public class DefaultOrderAfterSavingPlatformHandleService extends AbstractOrderAfterSavePlatformHandleServiceImpl {



    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_DEFAULT;
    }
}
