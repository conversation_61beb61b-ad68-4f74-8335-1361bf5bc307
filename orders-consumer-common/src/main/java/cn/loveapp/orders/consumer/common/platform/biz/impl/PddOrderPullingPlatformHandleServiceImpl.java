package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: orders-services-group
 * @description:
 * @author: Jason
 * @create: 2021-01-27 18:05
 **/
@Service
public class PddOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {
	private static final Integer PART = 90;

	@Override
	public boolean isSoldGetNeedSend2FullInfo(String platformId, String appName) {
		return false;
	}

    @Override
    public boolean isSoldGetNeedPullLogisticsTrace(String platformId, String appName) {
        return true;
    }

	@Override
	public List<SyncOrdersProgress> generateProgress(List<SyncOrdersProgress> soldGetProgressList, LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		if(CollectionUtils.isEmpty(soldGetProgressList)){
			soldGetProgressList = getSoldGetProgresses(startCreated, endCreated);
		}
		return soldGetProgressList;
	}

	@Override
	public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		return null;
	}

	protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated,
                                                            LocalDateTime endCreated) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		LocalDateTime startTime = endCreated.minusDays(1);
		for (int i = 0; i < PART; i++) {
			SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated);
			soldGetProgressList.add(soldGetProgress);
			endCreated = endCreated.minusDays(1);
			startTime = startTime.minusDays(1);

            if (startCreated != null && startTime.isBefore(startCreated)) {
                break;
            }
		}
		return soldGetProgressList;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
