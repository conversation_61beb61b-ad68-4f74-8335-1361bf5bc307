package cn.loveapp.orders.consumer.common.service;

import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.entity.AyTradeSubOrder;
import java.util.HashMap;
import java.util.List;
import javax.validation.constraints.NotNull;

/**
 * @program: orders-services-group
 * @description: BaseHandleService
 * @author: <PERSON>
 * @create: 2020-04-16 19:22
 **/
public interface BaseHandleService {
	/**
	 * 处理数据
	 *
	 * @param tradeHandleBo
	 * @param tradeBo
	 * @param ayTid
	 * @param ayOid
	 */
	void handle(TradeHandleBo tradeHandleBo, TradeBo tradeBo, @NotNull String ayTid, @NotNull HashMap<String, String> ayOid);

	/**
	 * 处理数据
	 *
	 * @param tradeHandleBo
	 * @param tradeBo
	 */
	void handlePurchase(TradeHandleBo tradeHandleBo, TradeBo tradeBo, @NotNull String ayTid);

}
