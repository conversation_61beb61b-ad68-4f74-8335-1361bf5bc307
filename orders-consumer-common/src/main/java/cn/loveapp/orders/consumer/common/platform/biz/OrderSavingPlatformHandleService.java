package cn.loveapp.orders.consumer.common.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderRefund;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtBase;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.bo.TradeReceiverDistrictListMongoBo;
import com.taobao.api.domain.Order;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 入库前逻辑多平台扩展追加数据
 *
 * @program: orders-services-group
 * @description: PlatformHandleService
 * @author: Jason
 * @create: 2021-01-26 16:48
 **/
public interface OrderSavingPlatformHandleService extends CommonPlatformHandler {

	/**
	 * 向trade原始信息追加一些额外的信息(如: 退款信息等)
	 *
	 * @param tradeBo
	 * @param platformId
	 * @param appName
	 */
	void appendToTradeInfo(@NotNull TradeBo tradeBo, String platformId, String appName);

	/**
	 * tradeMain生成完后, 向tradeMain追加数据
	 *
	 * @param tradeHandleBo
	 * @param tradeBo
	 * @param ayTid
	 * @param trade
	 * @param platformId
	 * @param appName
	 * @return
	 */
	void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName);

	/**
	 * TcSubOrder生成完后, 向TcSubOrder追加数据
	 * @param subOrderList
	 * @param tradeHandleBo
	 * @param tradeBo
	 * @param ayTid
	 * @param trade
	 * @param platformId
	 * @param appName
	 */
	void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName);

	/**
	 * tradeSearch生成完后, 向tradeSearch追加数据
	 *
	 * @param trade
	 * @param tradeBo
	 * @param tradeSearch
	 * @param sbInvoiceNos
	 * @param sbLogisticsCompanys
	 * @param platformId
	 * @param appName
	 */
    void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch, Set<String> sbInvoiceNos,
        Set<String> sbLogisticsCompanys, String platformId, String appName);

	/**
	 * 生成trade_ext
	 * @param tradeBo
	 * @param platformId
	 * @param appName
	 * @param <T>
	 * @return
	 */
	<T extends TcTradeExtBase> T generateTradeExt(TradeBo tradeBo, String platformId, String appName);

	/**
	 * 更新TradeReceiverDistrictListMongoBo 地址列表
	 * @param tradeBo
	 * @param trade
	 * @param tradeReceiverDistrictListMongoBo
	 * @param lastAyTradeMain
	 * @param platformId
	 * @param appName
	 */
	void updatingStatusAndReceiverStatus(TradeBo tradeBo, AyTrade trade,
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo, AyTradeMain lastAyTradeMain, String platformId, String appName);

	/**
	 * 根据平台规则生成MD5值
	 * @param tradeBo
	 * @param lastAyTradeMain
	 * @param platformId
	 * @param appName
	 * @return
	 */
	String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName);

    /**
     * 给tcOrder追加脱敏信息
     *
     * @param tradeBo
     * @param platformId
     * @param appName
     */
    void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName);


    /**
     * 生成物流超时时间
     * @param trade
     * @param order
     * @param platformId
     * @param appName
     * @return
     */
    Date generateLogisticsTimeoutTime(AyTrade trade, Order order, String platformId, String appName);

    /**
     * 追加地址变更申请信息
     *
     * @param newAyTradeMain
     * @param tradeAddrModifiedBySelfString
     * @param tradeAddrModifiedBySelfString
     * @param modified
     * @param appName
     */
    default TcOrder appendAddressChangeAppliedInfo(AyTradeMain newAyTradeMain,
        TmcOrdersRequest.TradeAddrModifiedBySelf tradeAddrModifiedBySelfString, LocalDateTime modified,
        String platformId, String appName) {
        return null;
    };

	/**
	 * 入库前根据历史数据处理子单
	 *
	 * @param tradeBo
	 * @param platformId
	 * @param appName
	 */
	default void processLastSubOrder(TradeBo tradeBo, String platformId, String appName) {
	};
}
