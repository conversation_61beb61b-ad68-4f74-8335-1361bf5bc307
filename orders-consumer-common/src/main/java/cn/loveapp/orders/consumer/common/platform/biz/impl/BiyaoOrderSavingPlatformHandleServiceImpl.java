package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.AyPurchaseOrderInfo;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAppenExtInfoPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2022/9/20 18:08
 **/
@Service
public class BiyaoOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    @Autowired
    private OrderAppenExtInfoPlatformHandleService platformHandleService;

    @Override
    public void appendToTradeInfo(@NotNull TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        AyPurchaseOrderInfo ayPurchaseOrderInfo = tradeBo.getAyPurchaseOrderInfo();
        String sourcePlatformId = ayPurchaseOrderInfo.getTradeSource();
        TcOrder cpTcOrder = ayPurchaseOrderInfo.getCpTcOrder();
        platformHandleService.appenExtInfoToAyTrade(trade, cpTcOrder, sourcePlatformId, cpTcOrder.getAppName());
    }

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        return;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_BIYAO;
    }
}
