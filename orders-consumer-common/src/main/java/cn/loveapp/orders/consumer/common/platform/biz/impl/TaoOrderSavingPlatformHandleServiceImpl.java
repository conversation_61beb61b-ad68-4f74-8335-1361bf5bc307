package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import cn.loveapp.orders.common.constant.AyOrderPayTypeEnum;
import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.constant.TaobaoAsdpAdsConstant;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import com.taobao.api.domain.LogisticsAgreement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.taobao.api.domain.LogisticServiceTag;
import com.taobao.api.domain.LogisticsTag;
import com.taobao.api.domain.Order;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtTaobao;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;

/**
 * @program: orders-services-group
 * @description:
 * @author: Jason
 * @create: 2021-01-26 16:50
 **/
@Service
public class TaoOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoOrderSavingPlatformHandleServiceImpl.class);

    /**
     * 淘宝fullinfo字段service_tags对应发货时间的tag
     */
    public static final String TB_CONSIGN_DATE = "consignDate";

    /**
     * 淘宝fullinfo字段orderAttr对应发货时间的tag
     */
    private static final String TB_EST_CON_TIME = "estConTime";

    /**
     * 发货时间的tag分隔符
     */
    private static final String EST_CON_TIME_SEPARATOR = "_";


    /**
     *  estConTime类型判断标识
     */
    private static final String EST_CON_TIME_TYPE_JUDGMENT_MARK = "年";

    /**
     *  发货时间的tag类型一 相对时间
     */
    private static final String EST_CON_TIME_RELATIVE_TIME = "2";

    /**
     *  发货时间的tag类型二 具体时间
     */
    private static final String EST_CON_TIME_CONCRETE_TIME = "1";

    /**
     * 发货时间正则——相对时间：5天内发货
     */
    private static final Pattern EST_CON_RELATIVE_TIME_PATTERN = Pattern.compile("\\D");
    /**
     * 发货时间正则准确时间：2024年10月31日24点前
     */
    private static final Pattern EST_CON_TIME_PATTERN = Pattern.compile("(\\d{4})年(\\d{1,2})月(\\d{1,2})日(\\d{1,2})点前");

    /**
     * 小时达信息对象字段
     */
    private static final String XSD_INFO_OBJ_FIELDS= "xsdFulfillmentInfo";

    /**
     * 保价服务类型
     */
    private static final String PRICE_PROTECT= "priceProtect";


	@Override
	public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
		// 淘宝平台专有的OAID，用来收件人信息解脱敏
		ayTradeMain.setOaid(trade.getOaid());
        // 订单服务类型
        ayTradeMain.addServiceType(generalServiceTypeByTrade(trade));
        // 将付款类型放入公共字段
        String payType = AyOrderPayTypeEnum.transformPayMethod(platformId, trade.getPaymentMethod());
        if (payType != null) {
            trade.setPayType(Lists.newArrayList(payType));
        }

        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
        if (!Objects.isNull(lastAyTradeMain)) {
            String nowSellerMemo = ayTradeMain.getSellerMemo();
            String lastSellerMemo = lastAyTradeMain.getSellerMemo();

            Integer nowSellerFlag= ayTradeMain.getSellerFlag();
            Integer lastSellerFlag =  lastAyTradeMain.getSellerFlag();
            if (!Objects.equals(nowSellerMemo, lastSellerMemo) || !Objects.equals(nowSellerFlag, lastSellerFlag)) {
                ayTradeMain.setMemoOrFlagModifyTime(ayTradeMain.getModified());
                // 备注在打印之后
                if (!BooleanUtils.isTrue(ayTradeMain.getIsModifiedMemoOrFlagAfterPrinted())
                    && lastAyTradeMain.getFirstPrintTime() != null
                    && lastAyTradeMain.getFirstPrintTime().before(ayTradeMain.getModified())) {
                    ayTradeMain.setIsModifiedMemoOrFlagAfterPrinted(true);
                }
            }
        }
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
        String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }
        appendSubOrderNumIid(trade, subOrderList);

        // 防止天猫换货订单开始有refund_status, 后面又没了的情况
        if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
            return;
        }

        List<TcSubOrder> lastTcSubOrder = tradeBo.getLastTcSubOrder();
        Map<String, TcSubOrder> oidAndTcSubOrder = null;
        if (CollectionUtils.isNotEmpty(lastTcSubOrder)) {
            oidAndTcSubOrder =
                lastTcSubOrder.stream().collect(Collectors.toMap(TcSubOrder::getOid, Function.identity()));
        }

        for (TcSubOrder tcSubOrder : subOrderList) {
            TcSubOrder dbTcSubOrder = null;
            if (oidAndTcSubOrder != null) {
                dbTcSubOrder= oidAndTcSubOrder.get(tcSubOrder.getOid());
            }

            if (dbTcSubOrder != null && dbTcSubOrder.getRefundModified() != null) {
                // 如果refundModified不为null 说明退款单在同步退款状态给订单，此时无需使用订单中的退款状态
                tcSubOrder.setRefundStatus(dbTcSubOrder.getRefundStatus());
            } else {
                // 有refundId没有refundStatus, 需要更新refundStatus到库中
                if (tcSubOrder != null && StringUtils.isNotEmpty(tcSubOrder.getRefundId())
                    && StringUtils.isEmpty(tcSubOrder.getRefundStatus())) {
                    tcSubOrder.setRefundStatus(StringUtils.EMPTY);
                    LOGGER.logWarn(tradeHandleBo.getSellerNick(), tradeHandleBo.getTid(), "缺少 refund_status");
                }
            }
        }
    }

	@Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
		tradeSearch.setOaid(trade.getOaid());
        appendSearchESNumIid(trade, tradeSearch);

        // 订单服务类型
        tradeSearch.addServiceType(generalServiceTypeByTrade(trade));

        AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
        tradeSearch.setMemoOrFlagModifyTime(ayTradeMain.getMemoOrFlagModifyTime());
        tradeSearch.setIsModifiedMemoOrFlagAfterPrinted(ayTradeMain.getIsModifiedMemoOrFlagAfterPrinted());
    }

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        return;
    }

    @Override
	public TcTradeExtTaobao generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
		TcTradeExtTaobao tcTradeExtTaobao = new TcTradeExtTaobao();
		tcTradeExtTaobao.additionalInit(tradeBo.getTrade());
		return tcTradeExtTaobao;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}

    @Override
    public void appendToTradeInfo(@NotNull TradeBo tradeBo, String platformId, String appName) {
    }


    @Override
    public Date generateLogisticsTimeoutTime(AyTrade trade, Order order, String platformId, String appName) {
        // todo 目前淘宝在更进商品维度3天以内 estConTime字段返回（目前三天内发货时效不返回）此处已兼容
        // 目前调研情况：仅当卖家商品发货时效为主单维度且发货时效在3天以内，并且商品参加任意官方活动，则下面三种情况都不会返回
        // 情况一: 先按子单维度处理(用户设置了不同sku不同发货时效后返回)
        if (TaobaoStatusConstant.REFUND_SUCCESS.equals(order.getRefundStatus())) {
            // 已退款子单不返回超时时间
            return null;
        }

        // 优先级最高 "order": [{ "consign_due_time": "1_2024-12-01", "estimate_con_time": "2024年12月01日24点前"}]
        String consignDueTime = order.getConsignDueTime();
        if (StringUtils.isNotEmpty(consignDueTime)) {
            String[] split = consignDueTime.split(EST_CON_TIME_SEPARATOR);
            if (Objects.equals(split[0], EST_CON_TIME_CONCRETE_TIME)) {
                LocalDate estConTimeDay = LocalDate.parse(split[1], DateUtil.FORMATTER_DATE_SINGLE_M_D);
                LocalTime time = LocalTime.of(23, 59, 59);
                return DateUtil.convertLocalDateTimetoDate(LocalDateTime.of(estConTimeDay, time));
            } else if (Objects.equals(split[0], EST_CON_TIME_RELATIVE_TIME)) {
                Integer days = getEstConTimeDays(split[1]);
                return getLogisticsTimeoutTime(trade, days);
            }
        }

        if (order.getOrderAttr() != null || order.getEstimateConTime() != null) {
            // {"orderAttr":"{\"estConTime\":\"2_5\"}","estimateConTime":"付款后5天内"} estConTime可能存在两种格式 1：2_3（代表三天内发货）
            // 2：1_2024-06-18（2024-06-18 23：59：59发货）
            // 优先取OrderAttr字段
            if (order.getOrderAttr() != null) {
                JSONObject orderAttrObj = JSON.parseObject(order.getOrderAttr());
                String estConTimeStr = orderAttrObj.getString(TB_EST_CON_TIME);
                if (estConTimeStr != null) {
                    String[] split = estConTimeStr.split(EST_CON_TIME_SEPARATOR);
                    if (Objects.equals(split[0], EST_CON_TIME_CONCRETE_TIME)) {
                        LocalDate estConTimeDay = LocalDate.parse(split[1], DateUtil.FORMATTER_DATE);
                        LocalTime time = LocalTime.of(23, 59, 59);
                        return DateUtil.convertLocalDateTimetoDate(LocalDateTime.of(estConTimeDay, time));
                    } else if (Objects.equals(split[0], EST_CON_TIME_RELATIVE_TIME)) {
                        Integer days = getEstConTimeDays(split[1]);
                        return getLogisticsTimeoutTime(trade, days);
                    }
                }
            }

            if (order.getEstimateConTime() != null) {
                if (order.getEstimateConTime().contains(EST_CON_TIME_TYPE_JUDGMENT_MARK)) {
                    // 格式一准确时间：2024年10月31日24点前
                    String estimateConTime = order.getEstimateConTime();
                    return parseEstimateConTimeByExactTime(estimateConTime);
                } else {
                    // 格式二相对时间: 付款后5天内
                    Integer estConTimeDay = getEstConTimeDays(order.getEstimateConTime());
                    if (estConTimeDay != null) {
                        return getLogisticsTimeoutTime(trade, estConTimeDay);
                    }
                }
            }
        }

        // 情况二:子单维度不存在 取主单维度（用户设置商品维度的发货时效大于3天返回）
        // 发货超时时间 相对时间:（"estConTime": "付款后5天内",） 准确时间:（"estConTime": "2024年10月31日24点前",）
        String estConTime = trade.getEstConTime();
        if (StringUtils.isNotEmpty(estConTime)) {
            if (estConTime.contains(EST_CON_TIME_TYPE_JUDGMENT_MARK)) {
                return parseEstimateConTimeByExactTime(estConTime);
            } else {
                Integer estConTimeDays = getEstConTimeDays(estConTime);
                if (estConTimeDays != null) {
                    return getLogisticsTimeoutTime(trade, estConTimeDays);
                }
            }
        }

        // 情况三: 当上面字段都不存在的时候取ServiceTags（当商品参加了活动不返回）
        if (null != trade.getServiceTags()) {
            for (LogisticsTag serviceTag : trade.getServiceTags()) {
                if (!order.getOidStr().equals(serviceTag.getOrderId())) {
                    continue;
                }

                if (CollectionUtils.isNotEmpty(serviceTag.getLogisticServiceTagList())) {
                    for (LogisticServiceTag logisticServiceTag : serviceTag.getLogisticServiceTagList()) {
                        // 判断logisticServiceTags的类型是否符合，符合则拼接承诺的发货时间 例:
                        // {"service_tag": "origAreaId=430104;consignDate=72","service_type":"TB_CONSIGN_DATE"}
                        if (!logisticServiceTag.getServiceTag().contains(TB_CONSIGN_DATE)) {
                            continue;
                        }
                        String[] tagArray = logisticServiceTag.getServiceTag().split(TB_CONSIGN_DATE + "=");
                        String consignDateValue = null;
                        if (tagArray.length > 0) {
                            consignDateValue = tagArray[1].split(";")[0];
                        } else {
                            continue;
                        }

                        Date logisticsTimeoutTime = null;
                        if (null == trade.getPayTime()) {
                            logisticsTimeoutTime =
                                DateUtils.addHours(trade.getCreated(), Integer.parseInt(consignDateValue));
                        } else {
                            logisticsTimeoutTime =
                                DateUtils.addHours(trade.getPayTime(), Integer.parseInt(consignDateValue));
                        }

                        return logisticsTimeoutTime;
                    }
                }

            }
        }

        // 情况四：当其他所有情况字段都没有取值，说明是发货时效为主单维度且时效在3天以内，此时取物流发货时效，单位小时（活动商品 商品维度发货时效 改字段也会透出）{"consignInterval": 720}
        Long consignInterval = trade.getConsignInterval();
        if (consignInterval != null) {
            Date logisticsTimeoutTime = null;
            if (null == trade.getPayTime()) {
                logisticsTimeoutTime = DateUtils.addHours(trade.getCreated(), consignInterval.intValue());
            } else {
                logisticsTimeoutTime = DateUtils.addHours(trade.getPayTime(), consignInterval.intValue());
            }
            return logisticsTimeoutTime;
        }
        return null;
    }



    /**
     * 处理平台返回的发货超时相对天数
     *
     * @param estConTime
     * @return
     */
    private Integer getEstConTimeDays(String estConTime) {
        if (StringUtils.isNotEmpty(estConTime)) {
            if (estConTime.length() == 1) {
                return Integer.valueOf(estConTime);
            }

            String day = EST_CON_RELATIVE_TIME_PATTERN.matcher(estConTime).replaceAll("");
            return Integer.valueOf(day);
        }

        return null;
    }

    /**
     * 解析estimateConTime 准确时间
     * @param estimateConTime
     * @return
     */
    private Date parseEstimateConTimeByExactTime(String estimateConTime) {
        // 格式一准确时间：2024年10月31日24点前
        Matcher matcher = EST_CON_TIME_PATTERN.matcher(estimateConTime);
        if (matcher.find()) {
            if (matcher.groupCount() == 4) {
                int year = Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));
                int day = Integer.parseInt(matcher.group(3));
                int hour = Integer.parseInt(matcher.group(4));
                if (hour == 24) {
                    return DateUtil.convertLocalDateTimetoDate(LocalDateTime.of(year, month, day, hour - 1, 59, 59));
                } else {
                    return DateUtil.convertLocalDateTimetoDate(LocalDateTime.of(year, month, day, hour, 0, 0));
                }
            } else {
                LOGGER.logError("estimateConTime字段解析异常: " + estimateConTime);
            }
        }
        return null;
    }

    /**
     * 获取最后的发货超时时间
     *
     * @param trade
     * @param consignDateValue
     * @return
     */
    private Date getLogisticsTimeoutTime(AyTrade trade, Integer consignDateValue) {
        // 若无发付款时间则可能为货到付款订单或过期订单，使用订单创建时间拼接兼容
        Date logisticsTimeoutTime = null;
        if (null == trade.getPayTime()) {
            logisticsTimeoutTime = DateUtils.addDays(trade.getCreated(), consignDateValue);
        } else {
            logisticsTimeoutTime = DateUtils.addDays(trade.getPayTime(), consignDateValue);
        }

        return logisticsTimeoutTime;
    }

    private ArrayList<String> generalServiceTypeByTrade(AyTrade trade){
        // 订单服务类型
        ArrayList<String> serviceTypeList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(trade.getStageAddressType())) {
            serviceTypeList.add(trade.getStageAddressType());
        }
        LogisticsAgreement logisticsAgreement = trade.getLogisticsAgreement();
        if (logisticsAgreement != null && StringUtils.isNotEmpty(logisticsAgreement.getAsdpAds())) {
            if (logisticsAgreement.getAsdpAds().contains(TaobaoAsdpAdsConstant.OPTIONAL_EXPRESS_SERVE)) {
                serviceTypeList.add(OrderServiceType.optional_express.name());
            }

            if (logisticsAgreement.getAsdpAds().contains(TaobaoAsdpAdsConstant.FAST_VISIT_SERVE)) {
                serviceTypeList.add(OrderServiceType.fast_visit.name());
            }
        }

        String tradeAttr = trade.getTradeAttr();
        if (StringUtils.isNotEmpty(tradeAttr)) {
            try {
                JSONObject tradeAttrJsonObject = JSONObject.parseObject(tradeAttr);
                JSONObject xsdJsonObject = tradeAttrJsonObject.getJSONObject(XSD_INFO_OBJ_FIELDS);
                if (!Objects.isNull(xsdJsonObject)) {
                    serviceTypeList.add(OrderServiceType.xsd.name());
                }
            } catch (Exception e) {
                LOGGER.logError(trade.getSellerNick(), "tradeAttr解析异常, 跳过解析", e.getMessage());
            }
        }

        String realReceiverOpenId = trade.getRealReceiverOpenId();
        if (StringUtils.isNotEmpty(realReceiverOpenId)) {
            serviceTypeList.add(OrderServiceType.gift_order.name());
        }

        String qnDistr = trade.getQnDistr();
        List<Order> orders = trade.getOrders();
        if (StringUtils.isNotEmpty(qnDistr)) {
            serviceTypeList.add(OrderServiceType.qn_distr.name());
        } else {
            boolean isExitQnDistr = orders.stream().anyMatch(s -> StringUtils.isNotEmpty(s.getQnDistr()));
            if (isExitQnDistr) {
                serviceTypeList.add(OrderServiceType.qn_distr.name());
            }
        }

        for (Order order : orders) {
            // 保价订单
            String specialRefundType = order.getSpecialRefundType();
            if (StringUtils.isNotEmpty(specialRefundType) && Objects.equals(specialRefundType, PRICE_PROTECT)) {
                serviceTypeList.add(OrderServiceType.price_protect.name());
                break;
            }
        }

        serviceTypeList.addAll(generalServiceType(trade));

        return CollectionUtils.isEmpty(serviceTypeList) ? null : serviceTypeList;
    }
}
