package cn.loveapp.orders.consumer.common.service;

import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.entity.AyPurchaseOrderInfo;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderBase;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.uac.response.UserInfoResponse;
import javax.validation.constraints.NotNull;

import java.time.LocalDateTime;
/**
 * @program: orders-services-group
 * @description: SyncVIPTradeService
 * @author: Jason
 * @create: 2018-11-21 17:45
 **/
public interface MigrateTradeHandleService {

	/**
	 * 统一入库逻辑
	 * @param tradeBo TradeBo 业务主体信息
	 * @param sellerNick String 用户信息
	 * @param jdpModified LocalDateTime rds修改时间用于埋点统计
	 * @param type String 类型
	 */
	boolean batchOrdersInfo(@NotNull TradeBo tradeBo, String sellerNick, LocalDateTime jdpModified, OrderBatchType type, boolean forceHandleFlag);

	/**
	 * tmc订单特殊变更消息入库
	 *
	 * @param tmcOrdersRequest
	 * @param sellerId
	 */
	void batchTmcOrdersChangeInfo(@NotNull TmcOrdersRequest tmcOrdersRequest, String sellerId, boolean forceHandleFlag, String storeId, String appName);

	/**
	 * batch入库逻辑
	 * @param trade Trade
	 * @param topSession String
	 * @param sellerId String
	 * @param sellerNick String
	 * @param tid String
	 * @param jdpModified LocalDateTime
	 * @param platformId String
	 * @param appName String
	 */
    TradeBo executeBatchOrders(AyTrade trade, String topSession, String sellerId, String sellerNick, String tid,
        LocalDateTime jdpModified, String platformId, String appName, boolean forceHandleFlag);

	/**
	 * 从Trade生成TradeBo
	 *
	 * @param trade
	 * @param topSession
	 * @param sellerId
	 * @param sellerNick
	 * @param tid
	 * @param endPoint
	 * @param platformId
	 * @param appName
	 * @return
	 */
    TradeBo createTradeBo(AyTrade trade, String topSession, String sellerId, String sellerNick, String tid,
        boolean endPoint, String platformId, String appName);

	/**
	 * 从生成采购订单使用TradeBo
	 * @param trade
	 * @param tcOrder
	 * @param ayPurchaseOrderInfo
	 * @param topSession
	 * @param platformId
	 * @param appName
	 * @return
	 */
	TradeBo createPurchaseTradeBo(AyTrade trade, TcOrder tcOrder, AyPurchaseOrderInfo ayPurchaseOrderInfo, String platformId, String appName, String topSession);

	/**
	 * 生成爱用采购单的TradeBo
	 * @param trade
	 * @param tcOrder
	 * @param distributeUser
	 * @param supplierUser
	 * @return
	 */
	TradeBo createAyPurchaseTradeBo(AyTrade trade, TcOrderBase tcOrder, UserInfoResponse distributeUser, UserInfoResponse supplierUser);

	/**
	 * 获取入库需要的解密数据
	 * @param tcOrder
	 * @param topSession
	 * @return
	 */
	OriginDecryptData getOriginDecryptDataInfo(TcOrderBase tcOrder, String topSession);
}
