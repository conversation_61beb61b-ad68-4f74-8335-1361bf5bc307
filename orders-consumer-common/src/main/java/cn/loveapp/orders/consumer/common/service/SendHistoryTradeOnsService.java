package cn.loveapp.orders.consumer.common.service;

import cn.loveapp.orders.common.proto.PullApiOrdersRequestProto;
import cn.loveapp.orders.common.proto.PullSoldGetApiOrdersRequestProto;

import java.time.LocalDateTime;

/**
 * @program: orders-services-group
 * @description: TaobaoTradeSoldGetServiceImpl
 * @author: <PERSON>
 * @create: 2018-12-07 15:59
 **/
public interface SendHistoryTradeOnsService {

	/**
	 * 停止拉单任务
	 */
	void stop();

	/**
	 * 拉取历史订单
	 *
	 * @param sellerId
	 * @param sellerNick
	 * @param startCreated
	 * @param endCreated
	 * @param taoStatus
	 * @param refundStatus
	 * @param tradeType
	 * @param ignoreSameModified sold拉单是否忽略与当前库中modified相同的订单
	 * @param maxRetryCount
	 * @param send2Fullinfo 是否发送fullinfo 或 直接入库
	 *
	 * @return 用户是否没有历史订单
	 * @throws Exception
	 */
	boolean pullHistoryTrades(PullApiOrdersRequestProto pullApiOrdersRequestProto, String sellerId,
		String sellerNick, LocalDateTime startCreated, LocalDateTime endCreated, String taoStatus, String refundStatus,
		String tradeType, boolean ignoreSameModified, int maxRetryCount, String toTopic, boolean send2Fullinfo, boolean forceHandleFlag)
		throws Exception;


	/**
	 * 增量拉取历史订单
	 *
	 * @param sellerId
	 * @param sellerNick
	 * @param startCreated
	 * @param endCreated
	 * @param taoStatus
	 * @param refundStatus
	 * @param tradeType
	 * @param ignoreSameModified sold拉单是否忽略与当前库中modified相同的订单
	 * @param maxRetryCount
	 * @param send2Fullinfo 是否发送fullinfo 或 直接入库
	 *
	 * @return 用户是否没有历史订单
	 * @throws Exception
	 */
	boolean pullIncrementHistoryTrades(PullApiOrdersRequestProto pullApiOrdersRequestProto, String sellerId,
									   String sellerNick, LocalDateTime startCreated, LocalDateTime endCreated, String taoStatus, String refundStatus,
									   String tradeType, boolean ignoreSameModified, int maxRetryCount, String toTopic, boolean send2Fullinfo, boolean forceHandleFlag)
		throws Exception;

	/**
	 * 供货商拉取历史订单
	 *
	 * @param sellerId
	 * @param sellerNick
	 * @param startCreated
	 * @param endCreated
	 * @param taoStatus
	 * @param refundStatus
	 * @param tradeType
	 * @param ignoreSameModified sold拉单是否忽略与当前库中modified相同的订单
	 * @param maxRetryCount
	 * @param send2Fullinfo 是否发送fullinfo 或 直接入库
	 *
	 * @return 用户是否没有历史订单
	 * @throws Exception
	 */
	boolean pullSupplierOrderQueryHistoryTrades(PullApiOrdersRequestProto pullApiOrdersRequestProto, Long supplierId, String sellerId,
							  String sellerNick, LocalDateTime startCreated, LocalDateTime endCreated, String taoStatus, String refundStatus,
							  String tradeType, boolean ignoreSameModified, int maxRetryCount, String toTopic, boolean send2Fullinfo, boolean forceHandleFlag)
		throws Exception;

    /**
     * 处理拉单后拉单类型的后续逻辑
     *
     * @param sellerNick
     * @param isEmpty
     * @param pullSoldGetApiOrdersRequestProto
     * @param startTime
     * @param apiPullOrderStartCreated
     * @param apiPullOrderCreatedEndTime
     * @param topic
     * @param platformId
     * @param appName
     */
    void handlePullTypeAfterPullOrder(String sellerNick, boolean isEmpty,
        PullSoldGetApiOrdersRequestProto pullSoldGetApiOrdersRequestProto, LocalDateTime startTime,
        LocalDateTime apiPullOrderStartCreated, LocalDateTime apiPullOrderCreatedEndTime, String topic,
        String platformId, String appName);
}
