package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.convert.CommonConvertMapper;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrderPdd;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtPdd;
import cn.loveapp.orders.common.utils.ConvertUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.common.utils.SearchIndexUtils;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.bo.TradeReceiverDistrictListMongoBo;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @program: orders-services-group
 * @description:
 * @author: Jason
 * @create: 2021-01-26 16:55
 **/
@Service
public class PddOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddOrderSavingPlatformHandleServiceImpl.class);

    @Autowired
    private OrderRepository orderRepository;

	@Override
	public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
		// PDD 推送可能没有buyerNick, 而且PDD没有加密解密的问题
		if(StringUtils.isEmpty(ayTradeMain.getBuyerNick()) && tradeBo.getLastAyTradeMain() != null){
			ayTradeMain.setBuyerNick(tradeBo.getLastAyTradeMain().getBuyerNick());
			ayTradeMain.setEncryptionType(tradeBo.getLastAyTradeMain().getEncryptionType());
		}
	}

	@Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
		String mobile = null;
		String phone = null;
		String receiverName = null;
        OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
        if (!Objects.isNull(originDecryptData) && !StringUtils.isEmpty(originDecryptData.getReceiverMobile())) {
			mobile = originDecryptData.getReceiverMobile();
		}
		if (!Objects.isNull(originDecryptData) && !StringUtils.isEmpty(originDecryptData.getReceiverPhone())) {
			phone = originDecryptData.getReceiverPhone();
		}
		if (!Objects.isNull(originDecryptData) && !StringUtils.isEmpty(originDecryptData.getReceiverName())) {
			receiverName = originDecryptData.getReceiverName();
		}
		/**
		 * 拼多多订单
		 * 风控订单、待发货且退款成功、已发货，这几种情况不更新receiver
		 **/
		if (StringUtils.isAllEmpty(phone,
			mobile,
			trade.getReceiverAddress(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			trade.getReceiverTown(),
			receiverName)){
			tradeSearch.setReceiver(null);
		}

		Set<String> invoiceNoSet = Sets.newHashSet(sbInvoiceNos);
		TcTradeExtPdd pddTradeExt = trade.getPddTradeExt();
		if (pddTradeExt != null && CollectionUtils.isNotEmpty(pddTradeExt.getExtraDeliveryList())) {
			for (TcTradeExtPdd.OrderInfoGetResponseOrderInfoExtraDeliveryListItem extraDeliveryListItem : trade.getPddTradeExt().getExtraDeliveryList()) {
				invoiceNoSet.add(extraDeliveryListItem.getTrackingNumber());
			}
		} else {
            appendSearchESNumIid(trade, tradeSearch);
        }

		tradeSearch.setInvoiceNo(ElasticsearchUtil.toList(invoiceNoSet));
		tradeSearch.setLogisticsCompany(ElasticsearchUtil.toList(sbLogisticsCompanys));
		tradeSearch.addServiceType(generalServiceType(trade));
	}

	@Override
	public TcTradeExtPdd generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();

        TcTradeExtPdd pddTradeExt = tradeBo.getTrade().getPddTradeExt();

        // pdd发货后不返回密文信息
        if (pddTradeExt != null && lastAyTradeMain != null && StringUtils.isAllEmpty(pddTradeExt.getEncryptReceiverAddress(), pddTradeExt.getEncryptReceiverName(), pddTradeExt.getEncryptReceiverPhone())) {
            TcTradeExtPdd lastPddTradeExt = orderRepository.queryTradeExt(CommonConvertMapper.INSTANCE.toTcOrder(lastAyTradeMain), TcOrderPdd.class);
            if (lastPddTradeExt != null) {
                pddTradeExt.setEncryptReceiverAddress(lastPddTradeExt.getEncryptReceiverAddress());
                pddTradeExt.setEncryptReceiverPhone(lastPddTradeExt.getEncryptReceiverPhone());
                pddTradeExt.setEncryptReceiverName(lastPddTradeExt.getEncryptReceiverName());
            }
        }

		return pddTradeExt;
	}

	@Override
	public void updatingStatusAndReceiverStatus(TradeBo tradeBo, AyTrade trade,
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		if (lastAyTradeMain != null) {
			AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
			if (!StringUtils.equals(lastAyTradeMain.getMergeMd5(), ayTradeMain.getMergeMd5())) {
				/**
				 * 拼多多订单
				 * 风控订单、待发货且退款成功、已发货，这几种情况不更新receiver
				 **/
				if (StringUtils.isAllEmpty(trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(),
					trade.getReceiverTown(), trade.getReceiverAddress())){
					//拼多多的地址为空，这种情况，不需要更新地址，但是状态必须更新
					tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(true);
				}else {
					//地址不为空，就地址和状态都更新
					tradeReceiverDistrictListMongoBo.setHasTradeReceiverChanged(Boolean.TRUE);
				}
			} else {
				// 订单状态变更
				tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(true);
			}
		}
	}

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
        boolean hasLast = Objects.nonNull(lastAyTradeMain);

		String buyerNick = ConvertUtil.findFirstNotEmpty(trade.getBuyerNick(), hasLast ? lastAyTradeMain.getBuyerNick() : null);
		String receiverAddress = ConvertUtil.findFirstNotEmpty(trade.getReceiverAddress(), hasLast ? lastAyTradeMain.getReceiverAddress() : null);
		String receiverName = ConvertUtil.findFirstNotEmpty(trade.getReceiverName(), hasLast ? lastAyTradeMain.getReceiverName() : null);
		String receiverMobile = ConvertUtil.findFirstNotEmpty(trade.getReceiverMobile(), hasLast ? lastAyTradeMain.getReceiverMobile() : null);
		String receiverPhone = ConvertUtil.findFirstNotEmpty(trade.getReceiverPhone(), hasLast ? lastAyTradeMain.getReceiverPhone() : null);

        TcTradeExtPdd pddTradeExt = trade.getPddTradeExt();
        if (pddTradeExt != null && pddTradeExt.getConsolidateInfo() != null) {
            Integer consolidateType = pddTradeExt.getConsolidateInfo().getConsolidateType();
            if (consolidateType != null) {
                // 集运仓订单生成md5忽略详细地址，后续会通过合单api判断是否可合单
                LOGGER.logInfo("集运地址合单MD5生成忽略详细地址，集运类型：" + consolidateType);
                receiverAddress = StringUtils.EMPTY;
            }
        }


        String receiverCountry = ConvertUtil.findFirstNotEmpty(trade.getReceiverCountry(), hasLast ? lastAyTradeMain.getReceiverCountry() : null);
        String receiverState = ConvertUtil.findFirstNotEmpty(trade.getReceiverState(), hasLast ? lastAyTradeMain.getReceiverState() : null);
        String receiverCity = ConvertUtil.findFirstNotEmpty(trade.getReceiverCity(), hasLast ? lastAyTradeMain.getReceiverCity() : null);
        String receiverDistrict = ConvertUtil.findFirstNotEmpty(trade.getReceiverDistrict(), hasLast ? lastAyTradeMain.getReceiverDistrict() : null);
        String receiverZip = ConvertUtil.findFirstNotEmpty(trade.getReceiverZip(), hasLast ? lastAyTradeMain.getReceiverZip() : null);

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
            receiverCountry,
            receiverState,
            receiverCity,
            receiverDistrict,
			receiverAddress,
            receiverZip,
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        doAppendDesensitizationInfo(tradeBo, platformId, appName);
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtPdd pddTradeExt = tradeHandleBo.getTrade().getPddTradeExt();
        if (pddTradeExt == null) {
			appendSubOrderNumIid(trade, subOrderList);
            return;
        }
        Long logisticsId = pddTradeExt.getLogisticsId();
        if (Objects.isNull(logisticsId) || logisticsId == 0) {
            return;
        }
        //将保存在主表ext内的物流公司id存入suborder内
        for (TcSubOrder tcSubOrder : subOrderList) {
            tcSubOrder.setLogisticsCompanyId(String.valueOf(logisticsId));
        }
    }

    @Override
    public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        Integer sellerFlag = trade.getSellerFlag() == null ? 0 : trade.getSellerFlag().intValue();
        String sellerMemo = trade.getSellerMemo();
        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
        Integer oldSellerFlag = null;
        String oldSellerMemo = null;
        if (lastAyTradeMain != null) {
            oldSellerFlag = lastAyTradeMain.getSellerFlag();
            oldSellerMemo = lastAyTradeMain.getSellerMemo();
        }

        if (!Objects.equals(sellerFlag, oldSellerFlag) || !Objects.equals(sellerMemo, oldSellerMemo)) {
            if (!(oldSellerFlag == null && sellerFlag == 0)) {
                // 以前老数据是null pdd现在可以将旗帜改回 改回默认值后平台不返回为null, 但订单入库不更新null， 手动设置默认值是0
                tradeBo.setIsModifyFlagOrMemo(true);
            }
        }
    }

    @Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
