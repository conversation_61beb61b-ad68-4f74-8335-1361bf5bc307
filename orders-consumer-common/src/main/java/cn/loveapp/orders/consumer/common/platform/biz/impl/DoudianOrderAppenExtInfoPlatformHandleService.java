package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.BiyaoPlatformMapConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderDoudian;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtDoudian;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAppenExtInfoPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunh<PERSON>
 * @create: 2022/9/20 18:29
 **/
@Service
public class DoudianOrderAppenExtInfoPlatformHandleService implements OrderAppenExtInfoPlatformHandleService {

    @Autowired
    private OrderRepository orderRepository;

    @Override
    public void appenExtInfoToAyTrade(AyTrade ayTrade, TcOrder cpTcOrder, String platformId, String appName) {
        TcTradeExtDoudian tcTradeExtDoudian = orderRepository.queryTradeExt(cpTcOrder, TcOrderDoudian.class);
        List<TcTradeExtDoudian.SubItem> subItems = tcTradeExtDoudian.getSubItems();
        subItems.forEach(subItem -> {
            subItem.setRefundStatus(null);
            subItem.setAfterSaleStatus(null);
        });
        tcTradeExtDoudian.setMainStatus(null);

        ayTrade.setDoudianTradeExt(tcTradeExtDoudian);
    }

    @Override
    public Integer getDistributionPlatform(String platformId, String appName) {
        return BiyaoPlatformMapConstant.DOUDIAN;
    }

    @Override
    public String getCpOrderId(TcSubOrder cpOrder, String platformId, String appName) {
        return getPlatformId() + cpOrder.getOid();
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_DOUDIAN;
    }
}
