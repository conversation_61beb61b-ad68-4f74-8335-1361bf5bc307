package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.OrderSensitiveInfo;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtTikTok;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.bo.TradeReceiverDistrictListMongoBo;

/**
 * <AUTHOR>
 * @date 2024-04-16 22:18
 * @description: tikTok订单存储 处理服务
 */
@Service
public class TikTokOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    /**
     * 未揽收状态集合
     */
    private static final List<String> UNCOLLECTED_PACKAGE_STATUS_LIST =
        Lists.newArrayList("TO_FULFILL", "PROCESSING", "CANCELLED");

    /**
     * 未交付包裹状态列表
     */
    private static final List<String> UNDELIVERED_PACKAGE_STATUS_LIST =
        Lists.newArrayList("TO_FULFILL", "PROCESSING", "CANCELLED", "FULFILLING");

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {

    }

    @Override
    public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid,
        AyTrade trade, String platformId, String appName) {
        if (StringUtils.isEmpty(ayTradeMain.getBuyerNick()) && tradeBo.getLastAyTradeMain() != null) {
            ayTradeMain.setBuyerNick(tradeBo.getLastAyTradeMain().getBuyerNick());
            ayTradeMain.setEncryptionType(tradeBo.getLastAyTradeMain().getEncryptionType());
        }

        if (trade != null) {
            TcTradeExtTikTok tikTokTradeExt = trade.getTikTokTradeExt();
            if (tikTokTradeExt != null && !CollectionUtils.isEmpty(tikTokTradeExt.getSubOrderExt())) {
                // 是否已揽收
                Boolean isCollected = null;
                // 是否已交付
                Boolean isDelivered = null;
                for (TcTradeExtTikTok.SubOrderExt subOrderExt : tikTokTradeExt.getSubOrderExt()) {
                    // 只要有一个子单未揽收 整个就是未揽收
                    if (UNCOLLECTED_PACKAGE_STATUS_LIST.contains(subOrderExt.getPackageStatus())) {
                        isCollected = false;
                    } else {
                        isCollected = true;
                    }

                    if (UNDELIVERED_PACKAGE_STATUS_LIST.contains(subOrderExt.getPackageStatus())) {
                        isDelivered = false;
                    } else {
                        isDelivered = true;
                    }
                }

                ayTradeMain.setIsCollected(BooleanUtils.isTrue(isCollected));
                ayTradeMain.setIsDelivered(BooleanUtils.isTrue(isDelivered));
            }
        }
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
        String ayTid, AyTrade trade, String platformId, String appName) {

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtTikTok tikTokTradeExt = trade.getTikTokTradeExt();
        if (tikTokTradeExt == null) {
            appendSubOrderNumIid(trade, subOrderList);
            return;
        }

        List<TcTradeExtTikTok.SubOrderExt> subOrderExtList = tikTokTradeExt.getSubOrderExt();
        Map<String, TcTradeExtTikTok.SubOrderExt> oidAndSubOrderExtMap = subOrderExtList.stream()
            .collect(Collectors.toMap(TcTradeExtTikTok.SubOrderExt::getOid, Function.identity()));

        for (TcSubOrder tcSubOrder : subOrderList) {
            // 是否已揽收
            Boolean isCollected = null;
            // 是否已交付
            Boolean isDelivered = null;
            TcTradeExtTikTok.SubOrderExt subOrderExt = oidAndSubOrderExtMap.get(tcSubOrder.getOid());
            if (UNCOLLECTED_PACKAGE_STATUS_LIST.contains(subOrderExt.getPackageStatus())) {
                isCollected = false;
            } else {
                isCollected = true;
            }

            if (UNDELIVERED_PACKAGE_STATUS_LIST.contains(subOrderExt.getPackageStatus())) {
                isDelivered = false;
            } else {
                isDelivered = true;
            }
            tcSubOrder.setIsCollected(isCollected);
            tcSubOrder.setIsDelivered(isDelivered);
        }
    }

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {

        tradeSearch.addServiceType(generalServiceType(trade));
        TcTradeExtTikTok tikTokTradeExt = trade.getTikTokTradeExt();
        if (tikTokTradeExt == null) {
            appendSearchESNumIid(trade, tradeSearch);
            return;
        }
        List<TcTradeExtTikTok.SubOrderExt> subOrderExt = tikTokTradeExt.getSubOrderExt();
        if (!CollectionUtils.isEmpty(subOrderExt)) {
            // 是否已揽收
            boolean isCollected = true;
            // 是否已交付
            boolean isDelivered = true;
            for (TcTradeExtTikTok.SubOrderExt orderExt : subOrderExt) {
                // 只要有一个子单未揽收 整个就是未揽收
                if (UNCOLLECTED_PACKAGE_STATUS_LIST.contains(orderExt.getPackageStatus())) {
                    isCollected = false;
                }

                if (UNDELIVERED_PACKAGE_STATUS_LIST.contains(orderExt.getPackageStatus())) {
                    isDelivered = false;
                }
            }
            tradeSearch.setIsCollected(BooleanUtils.isTrue(isCollected));
            tradeSearch.setIsDelivered(BooleanUtils.isTrue(isDelivered));
        }
    }

    @Override
    public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {
        super.appendToTradeInfo(tradeBo, platformId, appName);
    }

    @Override
    public TcTradeExtTikTok generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getTikTokTradeExt();
    }

    @Override
    public void updatingStatusAndReceiverStatus(TradeBo tradeBo, AyTrade trade,
        TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo, AyTradeMain lastAyTradeMain,
        String platformId, String appName) {
        super.updatingStatusAndReceiverStatus(tradeBo, trade, tradeReceiverDistrictListMongoBo, lastAyTradeMain,
            platformId, appName);
    }

    @Override
    public String generateMergeMd5(TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
        return super.generateMergeMd5(tradeBo, lastAyTradeMain, platformId, appName);
    }

    @Override
    protected void produceEncryptOrderSensitiveInfo(String tid, String sellerNick,
        OrderSensitiveInfo orderSensitiveInfo) {
        super.produceEncryptOrderSensitiveInfo(tid, sellerNick, orderSensitiveInfo);
    }

    @Override
    protected void doAppendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        super.doAppendDesensitizationInfo(tradeBo, platformId, appName);
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TIKTOK;
    }
}
