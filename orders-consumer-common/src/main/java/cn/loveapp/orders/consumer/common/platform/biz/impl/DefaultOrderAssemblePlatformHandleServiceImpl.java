package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAssemblePlatformHandleService;
import cn.loveapp.orders.consumer.common.service.TradeHandleService;
import cn.loveapp.orders.consumer.common.service.TradeSearchESHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeReceiverDistrictListMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeSubMongoHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 订单存单入库实体参数封装接口
 *
 * @program: orders-services-group
 * @description:
 * @author: zhangchu<PERSON><PERSON>
 * @create: 2022/9/16 15:17
 **/
@Service
public class DefaultOrderAssemblePlatformHandleServiceImpl implements OrderAssemblePlatformHandleService {

    @Autowired
    private TradeHandleService tradeHandleService;

    @Autowired
    private TradeSearchESHandleService tradeSearchESHandleService;

    @Autowired
    private TradeMongoHandleService tradeMongoHandleService;

    @Autowired
    private TradeSubMongoHandleService tradeSubMongoHandleService;

    @Autowired
    private TradeReceiverDistrictListMongoHandleService tradeReceiverDistrictListMongoHandleService;

    @Override
    public void handleMain(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, String platformId,
        String appName) {
        // 先处理主单
        tradeHandleService.handle(tradeHandleBo, tradeBo, ayTid);
    }

    @Override
    public void handleEs(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, String platformId,
        String appName) {
        // 处理ES搜索信息
        tradeSearchESHandleService.handle(tradeHandleBo, tradeBo, ayTid);
    }

    @Override
    public void handleMongo(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, HashMap<String, String> ayOid,
        String platformId, String appName) {
        // 1 先处理主单
        tradeMongoHandleService.handle(tradeHandleBo, tradeBo, ayTid, ayOid);
        // 2 处理子单
        tradeSubMongoHandleService.handle(tradeHandleBo, tradeBo, ayTid, ayOid);
        // 9 处理收货区域信息
        tradeReceiverDistrictListMongoHandleService.handle(tradeHandleBo, tradeBo, ayTid, ayOid);
    }

    @Override
    public String getPlatformId() {
         return CommonPlatformConstants.PLATFORM_DEFAULT;
    }
}
