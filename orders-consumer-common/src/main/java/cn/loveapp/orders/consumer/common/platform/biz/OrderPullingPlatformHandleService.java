package cn.loveapp.orders.consumer.common.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 平台订单拉取扩展接口
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
public interface OrderPullingPlatformHandleService extends CommonPlatformHandler {

	/**
	 * soldGet拉单是否发送到fullInfo队列
	 * @return
	 */
	default boolean isSoldGetNeedSend2FullInfo(String platformId, String appName) {
		return true;
	}

    /**
     * soldGet拉单是否拉取物流轨迹
     * @return
     */
    default boolean isSoldGetNeedPullLogisticsTrace(String platformId, String appName) {
        return false;
    }

    /**
     * soldGet拉单响应结果是否只有tid
     * @return
     */
    default boolean isSoldGetResponseOnlyHasTid(String platformId, String appName) {
        return false;
    }

    /**
     * soldGet/increment拉单时是否保存调用api时的最终时间(针对无消息推送，需要根据pullEndTime做increment拉取订单开始时间的平台设置为true，比如微信)
     * @return
     */
    default boolean isInsertApiPullOrderEndTime(String platformId, String appName) {
        return false;
    }

    /**
     * fullinfo拉去详情结束后是否发送到refundReflow队列
     * @return
     */
    default boolean isFullinfoNeedSend2RefundReflow(String platformId, String appName) {
        return false;
    }

	/**
	 * 处理不同平台的配置和progress生成规则
	 * @param syncOrdersProgressList
	 * @param startCreated
	 * @param endCreated
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws Exception
	 */
	List<SyncOrdersProgress> generateProgress(
            List<SyncOrdersProgress> syncOrdersProgressList, LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception;


	/**
	 * 处理不同平台的配置和progress生成规则
	 * @param startCreated
	 * @param endCreated
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws Exception
	 */
	List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception;


}
