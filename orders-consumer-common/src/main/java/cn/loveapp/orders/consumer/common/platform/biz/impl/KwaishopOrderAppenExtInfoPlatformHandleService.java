package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.List;

import cn.loveapp.orders.common.entity.mongo.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.BiyaoPlatformMapConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAppenExtInfoPlatformHandleService;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2022/9/20 18:29
 **/
@Service
public class KwaishopOrderAppenExtInfoPlatformHandleService implements OrderAppenExtInfoPlatformHandleService {

    @Autowired
    private OrderRepository orderRepository;

    @Override
    public void appenExtInfoToAyTrade(AyTrade ayTrade, TcOrder cpTcOrder, String platformId, String appName) {
        TcTradeExtKwaishop tcTradeExtKwaishop = orderRepository.queryTradeExt(cpTcOrder, TcOrderKwaishop.class);
        ayTrade.setKwaishopTradeExt(tcTradeExtKwaishop);
    }

    @Override
    public Integer getDistributionPlatform(String platformId, String appName) {
        return BiyaoPlatformMapConstant.KUAISHOP;
    }

    @Override
    public String getCpOrderId(TcSubOrder cpOrder, String platformId, String appName) {
        return getPlatformId() + "_" + cpOrder.getTid() + "_" + cpOrder.getOid();
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
