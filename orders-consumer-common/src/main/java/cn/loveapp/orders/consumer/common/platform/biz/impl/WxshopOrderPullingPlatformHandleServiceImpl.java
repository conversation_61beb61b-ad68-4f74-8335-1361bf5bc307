package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import javax.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 微信小商店订单拉取扩展接口
 * <AUTHOR>
 * @date 2021/12/14 10:32
 */
@Service
public class WxshopOrderPullingPlatformHandleServiceImpl extends TaoOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(WxshopOrderPullingPlatformHandleServiceImpl.class);


	private static final Integer PART = 90;

	@Override
	public boolean isSoldGetNeedSend2FullInfo(String platformId, String appName) {
		return false;
	}

	@Override
	public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		return getIncrementProgresses(startCreated,endCreated);
	}

	@NotNull
	protected List<SyncOrdersProgress> getIncrementProgresses(LocalDateTime startUpdated, LocalDateTime endUpdated) {
		LOGGER.logInfo("increment:start:" + DateUtil.convertLocalDateTimetoString(startUpdated) + ",end:" + DateUtil.convertLocalDateTimetoString(endUpdated));
		if (startUpdated == null) {
			//讲道理这里不能为空的，为空了则说明是soldget请求，应该走soldget
			LOGGER.logInfo("increment时发现startUpdated为空，开始执行soldget...");
			return getSoldGetProgresses(startUpdated, endUpdated);
		}
		if (endUpdated == null) {
			endUpdated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> incrementProgressList = Lists.newArrayList();
		incrementProgressList.add(new SyncOrdersProgress(startUpdated, endUpdated));
		return incrementProgressList;
	}





	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_WXSHOP;
	}
}
