package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-14 15:30
 * @description: 淘工厂 订单拉单处理服务实现类
 */
@Service
public class TgcOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {
    private static final Integer PART = 90;

    @Override
    public List<SyncOrdersProgress> generateProgress(List<SyncOrdersProgress> soldGetProgressList, LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        if(CollectionUtils.isEmpty(soldGetProgressList)){
            soldGetProgressList = getSoldGetProgresses(startCreated, endCreated);
        }
        return soldGetProgressList;    }

    @Override
    public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        return Collections.emptyList();
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TGC;
    }

    protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startDate, LocalDateTime endDate) {
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }
        if (startDate == null) {
            startDate = endDate.minusDays(PART);
        }

        List<SyncOrdersProgress> soldGetProgressList = new ArrayList<>();
        while (startDate.isBefore(endDate)) {
            LocalDateTime monthEnd = startDate.withDayOfMonth(startDate.toLocalDate().lengthOfMonth()).withHour(23)
                .withMinute(59).withSecond(59);
            if (monthEnd.isAfter(endDate)) {
                monthEnd = endDate;
            }
            SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startDate, monthEnd);
            soldGetProgressList.add(soldGetProgress);
            startDate = monthEnd.plusSeconds(1);
        }
        return soldGetProgressList;
    }
}
