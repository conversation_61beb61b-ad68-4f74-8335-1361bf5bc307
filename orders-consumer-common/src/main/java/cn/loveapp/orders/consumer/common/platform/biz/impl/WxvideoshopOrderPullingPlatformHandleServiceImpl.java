package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.constraints.NotNull;

import cn.loveapp.common.utils.DateUtil;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;

/**
 * 微信视频号小店 订单拉取扩展接口
 * <AUTHOR>
 * @date 2021/12/14 10:32
 */
@Service
public class WxvideoshopOrderPullingPlatformHandleServiceImpl extends TaoOrderPullingPlatformHandleServiceImpl{

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(WxvideoshopOrderPullingPlatformHandleServiceImpl.class);

    protected static final Integer PART = 90;

	@Override
	public boolean isSoldGetNeedSend2FullInfo(String platformId, String appName) {
		return true;
	}

    @Override
    public boolean isSoldGetResponseOnlyHasTid(String platformId, String appName) {
        return true;
    }

    @Override
    public boolean isInsertApiPullOrderEndTime(String platformId, String appName) {
        return true;
    }

    @Override
    public boolean isFullinfoNeedSend2RefundReflow(String platformId, String appName) {
        return true;
    }

	@Override
	public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		return getIncrementProgresses(startCreated,endCreated);
	}

	@NotNull
	protected List<SyncOrdersProgress> getIncrementProgresses(LocalDateTime startUpdated, LocalDateTime endUpdated) {
        LOGGER.logInfo("wxvideoshop:increment:start:" + DateUtil.convertLocalDateTimetoString(startUpdated) + ",end:" + DateUtil.convertLocalDateTimetoString(endUpdated));
        if (startUpdated == null) {
            //讲道理这里不能为空的，为空了则说明是soldget请求，应该走soldget
            LOGGER.logInfo("increment时发现startUpdated为空，开始执行soldget...");
            return getSoldGetProgresses(startUpdated, endUpdated);
        }
        if (endUpdated == null) {
            endUpdated = LocalDateTime.now();
        }
        List<SyncOrdersProgress> incrementProgressList = Lists.newArrayList();
        incrementProgressList.add(new SyncOrdersProgress(startUpdated, endUpdated));
        return incrementProgressList;
	}

    protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
        if (startCreated == null) {
            startCreated = LocalDateTime.now().minusDays(PART);
        }
        List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
        endCreated = startCreated.plusDays(1);
        for (int i = 0; i < PART; i++) {
            SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated);
            soldGetProgressList.add(soldGetProgress);
            startCreated = startCreated.plusDays(1);
            endCreated = endCreated.plusDays(1);
        }
        return soldGetProgressList;
    }


	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
	}
}
