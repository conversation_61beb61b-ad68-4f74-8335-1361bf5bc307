package cn.loveapp.orders.consumer.common.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;

/**
 * @program: orders-services-group
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/9/20 18:28
 **/
public interface OrderAppenExtInfoPlatformHandleService extends CommonPlatformHandler {

    /**
     * 入库前添加ext数据
     * @param ayTrade
     * @param cpTcOrder
     * @param platformId
     * @param appName
     */
    void appenExtInfoToAyTrade(AyTrade ayTrade, TcOrder cpTcOrder, String platformId, String appName);

    /**
     * 获取平台映射鸿源平台id
     * @param platformId
     * @param appName
     * @return
     */
    Integer getDistributionPlatform(String platformId, String appName);

    /**
     * 获取平台映射鸿源单号
     * @param cpOrder
     * @param platformId
     * @param appName
     * @return
     */
    String getCpOrderId(TcSubOrder cpOrder, String platformId, String appName);

}
