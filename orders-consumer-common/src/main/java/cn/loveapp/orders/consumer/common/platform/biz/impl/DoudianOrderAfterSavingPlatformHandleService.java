package cn.loveapp.orders.consumer.common.platform.biz.impl;


import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.helper.YchHelper;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 抖店订单保存后续处理
 *
 * <AUTHOR>
 * @Date 2023/6/2 14:50
 */
@Service
public class DoudianOrderAfterSavingPlatformHandleService extends AbstractOrderAfterSavePlatformHandleServiceImpl {

    @Autowired
    private YchHelper ychHelper;

    @Override
    public void orderReport(TradeBo tradeBo, String storeId, String appName) {
        if (tradeBo.getLastAyTradeMain() == null && tradeBo.getTrade() != null) {
            // 第一次入库, 推送完成转单
            ychHelper.sendDoudianYchOnsMsg(tradeBo.getTrade(), YchHelper.ReportStatus.ERP_TRANSFER.name(), appName);
        }

    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_DOUDIAN;
    }
}
