package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.config.kwaishop.KwaishopAppConfig;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 快手电商平台 历史订单拉取相关service
 *
 * <AUTHOR>
 */
@Service
public class KwaishopOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {

    @Autowired
    private KwaishopAppConfig kwaishopAppConfig;

    @Override
    public List<SyncOrdersProgress> generateProgress(List<SyncOrdersProgress> soldGetProgressList, LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        if (CollectionUtils.isEmpty(soldGetProgressList)){
            soldGetProgressList = getSoldGetProgresses(startCreated, endCreated);
        }
        return soldGetProgressList;
    }

	@Override
	public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		return null;
	}

	@NotNull
    protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
        int soldGetMaxBeginTimeBeforeHours = kwaishopAppConfig.getSoldGetMaxBeginTimeBeforeHours();
        LocalDateTime now = LocalDateTime.now();
        if (startCreated == null || startCreated.isBefore(now.minusHours(soldGetMaxBeginTimeBeforeHours))) {
            startCreated = now.minusHours(soldGetMaxBeginTimeBeforeHours);
        }
        if (endCreated == null) {
            endCreated = now;
        }

        int maxTimeRangeHours = kwaishopAppConfig.getSoldGetMaxTimeRangeHours();
        List<SyncOrdersProgress> soldGetProgressesList = Lists.newArrayList();
        LocalDateTime startTime = null;
        LocalDateTime endTime = endCreated;
        boolean end = false;
        do {
            startTime = endTime.minusHours(maxTimeRangeHours);
            if (startTime.isBefore(startCreated)) {
                startTime = startCreated;
                end = true;
            }
            soldGetProgressesList.add(new SyncOrdersProgress(startTime, endTime));
            endTime = startTime;
        } while (!end);
        return soldGetProgressesList;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
