package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import javax.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 抖店订单拉取扩展接口
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Service
public class DoudianOrderPullingPlatformHandleServiceImpl extends TaoOrderPullingPlatformHandleServiceImpl implements
	OrderPullingPlatformHandleService {

	@NotNull
	protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		if (startCreated == null) {
			// 抖店的起始时间不能为空, 而且必须在90天内
			startCreated = endCreated.minusDays(90);
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		for (int i = 0; i < PART; i++) {
			LocalDateTime startTime = endCreated.minusMonths(i + 1);
			if (startCreated.isAfter(startTime)) {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
				break;
			} else if(i == PART - 1){
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			} else {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			}
		}
		return soldGetProgressList;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_DOUDIAN;
	}
}
