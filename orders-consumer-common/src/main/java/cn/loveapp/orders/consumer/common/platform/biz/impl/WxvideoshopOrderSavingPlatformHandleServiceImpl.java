package cn.loveapp.orders.consumer.common.platform.biz.impl;

import javax.validation.constraints.NotNull;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.dto.AyRefundOidAndModifyDto;
import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtWxvideoshop;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.consumer.common.bo.TradeBo;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 微信视频号小店 订单存储 处理服务
 * <AUTHOR>
 * @date 2021/12/24 17:13
 */
@Service
public class WxvideoshopOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(WxvideoshopOrderSavingPlatformHandleServiceImpl.class);

    @Override
    public TcTradeExtWxvideoshop generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getWxvideoshopTradeExt();
    }

    @Override
    public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
        if (trade.getWxvideoshopTradeExt() == null) {
            return;
        }

        TcTradeExtWxvideoshop wxvideoshopTradeExt = trade.getWxvideoshopTradeExt();
        if (BooleanUtils.isTrue(wxvideoshopTradeExt.getIsPresent())) {
            // 礼物单
            ayTradeMain.addServiceType(Lists.newArrayList(OrderServiceType.gift_order.name()));
        }
    }

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch, Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
        appendSearchESNumIid(trade, tradeSearch);
        tradeSearch.addServiceType(generalServiceType(trade));

        if (trade.getWxvideoshopTradeExt() != null) {
            TcTradeExtWxvideoshop wxvideoshopTradeExt = trade.getWxvideoshopTradeExt();
            if (BooleanUtils.isTrue(wxvideoshopTradeExt.getIsPresent())) {
                // 礼物单
                tradeSearch.addServiceType(Lists.newArrayList(OrderServiceType.gift_order.name()));
            }

        }
    }

    @Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();
		String receiverMobile = originDecryptData.getReceiverMobile();
		String receiverPhone = originDecryptData.getReceiverPhone();

        TcTradeExtWxvideoshop wxvideoshopTradeExt = trade.getWxvideoshopTradeExt();
        if (wxvideoshopTradeExt != null) {
            // 微信视频号详细地址全是*无法判断是否相同, 用平台返回的hashCode参与生成md5防止不同用户错误合单
            receiverAddress = wxvideoshopTradeExt.getAddressHashCode();
        }

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			receiverAddress,
			trade.getReceiverZip(),
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        return;
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
                                 String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtWxvideoshop wxvideoshopTradeExt = tradeHandleBo.getTrade().getWxvideoshopTradeExt();

        if (wxvideoshopTradeExt == null || CollectionUtils.isEmpty(wxvideoshopTradeExt.getRefundInfo())) {
            appendSubOrderNumIid(trade, subOrderList);
            return;
        }
        List<AyRefundOidAndModifyDto> refundInfoList = wxvideoshopTradeExt.getRefundInfo();

        //将保存在主表ext内的refundid和refundModify存入suborder内
        Map<String, Date> refundIdAndModifyMap = refundInfoList.stream().collect(Collectors.toMap(k -> k.getRefundId(), v -> v.getModify(), (v1, v2) -> v1));
        for (TcSubOrder tcSubOrder : subOrderList) {
            if (StringUtils.isNotEmpty(tcSubOrder.getRefundId()) && refundIdAndModifyMap.containsKey(tcSubOrder.getRefundId())) {
                tcSubOrder.setRefundModified(refundIdAndModifyMap.get(tcSubOrder.getRefundId()));
            }
        }
    }

    @Override
    public void processLastSubOrder(TradeBo tradeBo, String platformId, String appName) {

        List<TcSubOrder> lastTcSubOrder = tradeBo.getLastTcSubOrder();
        if (CollectionUtils.isEmpty(lastTcSubOrder)) {
            return;
        }
        AyTrade trade = tradeBo.getTrade();
        List<Order> orders = trade.getOrders();
        Set<String> newOidSet = orders.stream().map(Order::getOidStr).collect(Collectors.toSet());
        if (CommonAppConstants.APP_TRADE.equals(appName) && lastTcSubOrder.stream().map(TcSubOrder::getOid).anyMatch(oid -> !newOidSet.contains(oid))) {
            // 交易历史数据，oid使用的是skuId, 上线erp后，修正为 订单商品内唯一id,此处兼容已入库老数据
            for (Order order : orders) {
                order.setOidStr(order.getSkuId());
            }
        }


    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
    }
}
