package cn.loveapp.orders.consumer.common.service;

import cn.loveapp.orders.common.api.entity.AyDistribute;
import cn.loveapp.orders.common.api.entity.AyRefund;
import cn.loveapp.orders.common.api.entity.AyRateInfo;
import cn.loveapp.orders.common.api.request.AyDistributeGetRequest;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.dto.AyTradeBatchFullinfoPutDto;
import javax.validation.constraints.NotNull;

import java.util.List;

/**
 * @program: orders-services-group
 * @description: MigrateTaoApiOrderService
 * @author: Jason
 * @create: 2018-12-10 16:56
 **/
public interface MigrateTaoApiOrderService {

	/**
	 * 批量fullinfo记录
	 *
	 * @param ayTradeBatchFullinfoPutDto
	 */
    List<TradeBo> batchFullInfoOrderPutRecords(AyTradeBatchFullinfoPutDto ayTradeBatchFullinfoPutDto);

    /**
     * 处理特殊tmc消息传来的fullinfo记录
     *
     * @param tmcOrdersRequest
     * @param sellerId
     * @param forceHandleFlag
     * @param appName
     */
    void batchTmcOrderPutRecords(@NotNull TmcOrdersRequest tmcOrdersRequest, String sellerId, boolean forceHandleFlag,
        String appName);

	/**
	 * refund.get 记录
	 * @param refund
	 * @param sellerNick
	 * @param sellerId
	 * @param platformId
	 */
	void refundGetPutRecords(@NotNull AyRefund refund, String sellerNick, String sellerId, String platformId, String appName, boolean forceIgnore, boolean forceHandleFlag) throws Exception;

	/**
	 * traderates.get 记录
	 * @param ayTradeMain
	 * @param ayRateInfoList
	 * @param sellerNick
	 * @param corpId
	 * @param sellerId
	 * @param platformId
	 * @param appName
	 * @param forceHandleFlag
	 * @throws Exception
	 */
	void rateGetBatchPutRecords(@NotNull AyTradeMain ayTradeMain, @NotNull List<AyRateInfo> ayRateInfoList, String sellerNick, String corpId, String sellerId, String platformId, String appName, boolean forceHandleFlag) throws Exception;


	/**
	 * distribute.get 记录
	 * @param request
	 * @param distributes
	 * @param sellerNick
	 * @param sellerId
	 * @param forceHandleFlag
	 * @param resendTimes
	 * @param platformId
	 * @param appName
	 * @throws Exception
	 */
	void distributeGetPullRecords(AyDistributeGetRequest request, @NotNull List<AyDistribute> distributes, String sellerNick, String sellerId, boolean forceHandleFlag, Integer resendTimes, String platformId, String appName) throws Exception;

	/**
	 * 处理物流tmc消息传来的fullinfo记录
	 * @param tradeMain
	 * @param tmcOrdersRequest
	 * @param sellerId
	 * @param forceHandleFlag
	 */
	void batchTmcLogisticsOrderPutRecords(AyTradeMain tradeMain, TmcOrdersRequest tmcOrdersRequest, String sellerId, boolean forceHandleFlag);
}
