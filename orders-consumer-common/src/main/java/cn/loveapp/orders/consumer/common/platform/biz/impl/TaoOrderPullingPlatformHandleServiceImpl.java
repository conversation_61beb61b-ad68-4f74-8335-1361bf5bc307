package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import javax.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: orders-services-group
 * @description: SendHistoryTradeOnsTaoDynamicServiceImpl
 * @author: Jason
 * @create: 2021-01-27 18:01
 **/
@Service
public class TaoOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {


	protected static final Integer PART = 3;

	@Override
	public List<SyncOrdersProgress> generateProgress(
            List<SyncOrdersProgress> soldGetProgressList, LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		if(CollectionUtils.isEmpty(soldGetProgressList)){
			if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
				soldGetProgressList = getSupplierOrderQueryProgresses(startCreated, endCreated);
			} else {
				soldGetProgressList = getSoldGetProgresses(startCreated, endCreated);
			}
		}
		return soldGetProgressList;
	}

	@Override
	public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
		return null;
	}

	@NotNull
	protected List<SyncOrdersProgress> getSupplierOrderQueryProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
		// 供货商接口目前只支持查询30天，但实际查30天会报错，29天不会
		LocalDateTime startTime = endCreated.minusDays(29);
		SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated);
		soldGetProgressList.add(soldGetProgress);
//		int days = 10;
//		for (int i = 0; i < PART; i++) {
//			if (i == PART - 1) {
//				// 最后一次拉取只拉days-1天，虽然接口文档说支持查询30天，但实际上查30天会报错
//				days = days - 1;
//			}
//			LocalDateTime startTime = endCreated.minusDays((i + 1) * days);
//			if (startCreated != null && startCreated.isAfter(startTime)) {
//				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated.minusDays(i * 10));
//				soldGetProgressList.add(soldGetProgress);
//				break;
//			} else if(i == PART - 1){
//				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated.minusDays(i * 10));
//				soldGetProgressList.add(soldGetProgress);
//			} else {
//				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated.minusDays(i * 10));
//				soldGetProgressList.add(soldGetProgress);
//			}
//		}
		return soldGetProgressList;
	}

	@NotNull
	protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
		if (endCreated == null) {
			endCreated = LocalDateTime.now();
		}
		List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
        int partSize = getPartSize();
		for (int i = 0; i < partSize; i++) {
			LocalDateTime startTime = endCreated.minusMonths(i + 1);
			if (startCreated != null && startCreated.isAfter(startTime)) {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startCreated, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
				break;
			} else if(i == partSize - 1){
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(null, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			} else {
				SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated.minusMonths(i));
				soldGetProgressList.add(soldGetProgress);
			}
		}
		return soldGetProgressList;
	}

    protected int getPartSize() {
        return PART;
    }

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}
}
