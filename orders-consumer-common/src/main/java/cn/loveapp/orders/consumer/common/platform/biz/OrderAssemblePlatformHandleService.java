package cn.loveapp.orders.consumer.common.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;

import javax.validation.constraints.NotNull;
import java.util.HashMap;

/**
 * 订单存单入库实体参数封装接口
 *
 * @program: orders-services-group
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/9/16 15:15
 **/
public interface OrderAssemblePlatformHandleService extends CommonPlatformHandler {

    /**
     * 封装AyTradeMain合单实体
     * @param tradeHandleBo
     * @param tradeBo
     * @param ayTid
     * @param platformId
     * @param appName
     */
    void handleMain(@NotNull TradeHandleBo tradeHandleBo, @NotNull TradeBo tradeBo, String ayTid, String platformId,
        String appName);

    /**
     * 封装Es入库实体
     * @param tradeHandleBo
     * @param tradeBo
     * @param ayTid
     * @param platformId
     * @param appName
     */
    void handleEs(@NotNull TradeHandleBo tradeHandleBo, @NotNull TradeBo tradeBo, String ayTid, String platformId,
        String appName);

    /**
     * 封装Mongo入库实体
     *
     * @param tradeHandleBo
     * @param tradeBo
     * @param ayTid
     * @param ayOid
     * @param platformId
     * @param appName
     */
    void handleMongo(@NotNull TradeHandleBo tradeHandleBo, @NotNull TradeBo tradeBo, String ayTid,
        HashMap<String, String> ayOid, String platformId, String appName);

}
