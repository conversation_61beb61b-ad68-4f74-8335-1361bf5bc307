package cn.loveapp.orders.consumer.common.service;

import cn.loveapp.orders.consumer.common.bo.AyDistributeTradeBo;
import cn.loveapp.orders.consumer.common.bo.AyPurchaseTradeBo;

/**
 * 分销代发入库相关
 *
 * <AUTHOR>
 * @Date 2023/11/1 5:14 PM
 */
public interface TradeAyDistributeHandleService {


    /**
     * 保存分销单入库
     *
     * @param tradeBo
     */
    void saveDistributeOrder(AyDistributeTradeBo tradeBo, boolean isNeedLockOrder);


    /**
     * 保存采购单入库
     * @param tradeBo
     * @param isNeedLockOrder
     */
    void savePurchaseOrder(AyPurchaseTradeBo tradeBo, boolean isNeedLockOrder);

}
