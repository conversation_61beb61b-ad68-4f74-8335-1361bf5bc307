package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import org.springframework.stereotype.Service;

/**
 * Ali1688OrderPullingPlatformHandleServiceImpl
 *
 * <AUTHOR>
 * @date 2021/4/7
 */
@Service
public class Ali1688OrderPullingPlatformHandleServiceImpl extends TaoOrderPullingPlatformHandleServiceImpl implements
	OrderPullingPlatformHandleService {

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}
}
