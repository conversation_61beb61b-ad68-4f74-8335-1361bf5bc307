package cn.loveapp.orders.consumer.common.service;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.OrderBatchType;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderBase;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import javax.validation.constraints.NotNull;

/**
 * @program: orders-services-group
 * @description: TradeSearchHandleService
 * @author: Jason
 * @create: 2018-11-22 20:56
 **/
public interface TradeSearchESHandleService {
	/**
	 * 处理数据
	 *
	 * @param tradeHandleBo
	 * @param tradeBo
	 * @param ayTid
	 */
	void handle(TradeHandleBo tradeHandleBo, TradeBo tradeBo, @NotNull String ayTid);

	/**
	 * 更新订单搜索表
	 *
	 * @param ayTradeSearch
	 * @param type
	 * @return
	 */
	int update(@NotNull AyTradeSearchES ayTradeSearch, OrderBatchType type);

	/**
	 * 插入或更新订单搜索表
	 *
	 * @param ayTradeSearch
	 * @param isInsert
	 * @param type
	 * @return
	 */
	int putTradeData(@NotNull AyTradeSearchES ayTradeSearch, boolean isInsert, OrderBatchType type);

    /**
     * 更新suborder内的采购单信息
     */
    void updateSubOrderOriginInfo(TcSubOrder dbTcSubOrder);

	/**
	 * 处理采购单数据
	 *
	 * @param tradeHandleBo
	 * @param tradeBo
	 * @param ayTid
	 */
	void handlePurchase(TradeHandleBo tradeHandleBo, TradeBo tradeBo, @NotNull String ayTid);

	/**
	 * 更新采购单信息
	 * @param purchaseAyTradeMain
	 */
    void updatePurchaseInfo(AyTradeMain purchaseAyTradeMain);

	/**
	 * 根据平台订单信息更新采购单
	 * @param tcOrder
	 * @param subOrders
	 * @param purchaseAyTradeMain
	 * @param originDecryptData
	 */
	void updatePurchaseByCpOrderInfo(TcOrder tcOrder, TcSubOrder subOrders, AyTradeMain purchaseAyTradeMain, OriginDecryptData originDecryptData);


}
