package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.request.LogisticsTraceSubscribeRequest;
import cn.loveapp.orders.common.config.trade.OrderLogisticsConfig;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.service.LogisticsService;
import cn.loveapp.orders.common.utils.ConvertUtil;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAfterSavePlatformHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单保存后续处理公共抽象类
 *
 * <AUTHOR>
 * @Date 2023/6/2 14:47
 */
public abstract class AbstractOrderAfterSavePlatformHandleServiceImpl implements OrderAfterSavePlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractOrderAfterSavePlatformHandleServiceImpl.class);

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private OrderLogisticsConfig orderLogisticsConfig;

    /**
     * 物流订阅消息开关，关闭默认走rpc调用
     */
    @Value("${order.logistics.subscribe.checkRepeat.enable:false}")
    private boolean logisticsCheckRepeatEnable;

    @Override
    public void appendToLogisticsInfo(TradeBo tradeBo, String storeId, String appName) {

        TcOrder tcOrder = tradeBo.getTcOrder();
        if (Objects.isNull(tcOrder)) {
            return;
        }
        String sellerNick = tcOrder.getSellerNick();
        String tid = tcOrder.getTid();

        // 物流消息订阅
        List<TcSubOrder> lastTcSubOrders = tradeBo.getLastTcSubOrder();
        List<TcSubOrder> newTcSubOrders = tradeBo.getTcSubOrder();
        Map<String, TcSubOrder> oidToSubOrder = new HashMap<>(0);
        if (CollectionUtils.isNotEmpty(lastTcSubOrders)) {
            oidToSubOrder = lastTcSubOrders.stream().collect(Collectors.toMap(TcSubOrder::getOid, subOrder -> subOrder));
        }

        List<LogisticsOrderSubscribeDTO> logisticsHandles = null;
        for (TcSubOrder newSubOrder : newTcSubOrders) {
            String oid = newSubOrder.getOid();
            String newSid = newSubOrder.getInvoiceNo();
            String logisticsCompany = newSubOrder.getLogisticsCompany();
            String logisticsCompanyCode = newSubOrder.getLogisticsCompanyCode();
            String logisticsCompanyId = newSubOrder.getLogisticsCompanyId();
            if (StringUtils.isEmpty(newSid) || StringUtils.isAllEmpty(logisticsCompany, logisticsCompanyCode, logisticsCompanyId)) {
                // 不存在物流,跳过
//                LOGGER.logInfo(sellerNick, tid, "子单：" + oid + ",不存在物流，跳过, newSid:" + newSid + "company:" + logisticsCompany + "," + logisticsCompanyCode + "," + logisticsCompanyId);
                continue;
            }
            // 判断是否物流（和发货时间）发生变化
            TcSubOrder lastSubOrders = oidToSubOrder.get(newSubOrder.getOid());
            String lastSid = null;
            Date lastConsignTime = null;
            if (!Objects.isNull(lastSubOrders)) {
                lastSid = lastSubOrders.getInvoiceNo();
                lastConsignTime = lastSubOrders.getConsignTime();
            }
            Date consignTime = newSubOrder.getConsignTime();
            if (logisticsCheckRepeatEnable && !StringUtils.isEmpty(lastSid) && Objects.equals(lastSid, newSid) && DateUtil.compareDatesBySeconds(lastConsignTime, consignTime)) {
                // 物流未发生变化
                LOGGER.logInfo(sellerNick, tid, "子单：" + oid + ",未发生变化，跳过 last:" + lastSid + "," + lastConsignTime + "; new:" + newSid + "," + consignTime);
                continue;
            }
            if (logisticsHandles == null) {
                logisticsHandles = new ArrayList<>(newTcSubOrders.size());
            }

            // 存在物流变更，需要订阅
            LogisticsOrderSubscribeDTO logisticsHandle = generalLogisticsHandle(tcOrder, newSubOrder, storeId, appName);
            if (Objects.isNull(logisticsHandle)) {
                LOGGER.logError(sellerNick, tid, "子单：" + oid + "生成物流对象错误");
                continue;
            }
            logisticsHandles.add(logisticsHandle);
        }

        if (CollectionUtils.isNotEmpty(logisticsHandles)) {
            LogisticsTraceSubscribeRequest request = new LogisticsTraceSubscribeRequest();
            request.setLogisticsHandles(logisticsHandles);
            logisticsService.logisticsTraceSubscribe(request);
        }

    }


    @Override
    public void orderReport(TradeBo tradeBo, String storeId, String appName) {

    }

    /**
     * 封装物流订阅请求体
     * @param tcOrder
     * @param subOrder
     * @return
     */
    protected LogisticsOrderSubscribeDTO generalLogisticsHandle(TcOrder tcOrder, TcSubOrder subOrder, String storeId, String appName) {
        String sourceLogisticsCompany = ConvertUtil.findFirstNotNull(subOrder.getLogisticsCompanyCode(), subOrder.getLogisticsCompanyId(), subOrder.getLogisticsCompany());
        if (StringUtils.isEmpty(sourceLogisticsCompany)) {
            // 物流公司为空不订阅
            LOGGER.logWarn("物流公司为空不订阅");
            return null;
        }
        LogisticsOrderSubscribeDTO logisticsHandle = new LogisticsOrderSubscribeDTO();
        logisticsHandle.setLogisticsStoreId(chooseLogisticsStoreId(storeId));
        logisticsHandle.setAppName(subOrder.getAppName());
        logisticsHandle.setStoreId(subOrder.getStoreId());
        logisticsHandle.setSellerId(subOrder.getSellerId());
        logisticsHandle.setSellerNick(subOrder.getSellerNick());
        logisticsHandle.setOutSid(subOrder.getInvoiceNo());
        logisticsHandle.setSourceLogisticsCompany(sourceLogisticsCompany);
        logisticsHandle.setBusinessIds(Lists.newArrayList(tcOrder.getTid()));
        logisticsHandle.setBusinessType(BusinessType.order);
        logisticsHandle.setConsignTime(subOrder.getConsignTime());
        logisticsHandle.setBuyerNick(tcOrder.getBuyerNick());
        logisticsHandle.setBuyerOpenUid(tcOrder.getBuyerOpenUid());
        return logisticsHandle;
    }

    /**
     * 获取物流平台storeId
     * @param storeId
     * @return
     */
    protected String chooseLogisticsStoreId(String storeId){

        List<String> kdniaoSubscribeExcludeStoreList = orderLogisticsConfig.getKdniaoSubscribeExcludeStoreList();

        if (CollectionUtils.isNotEmpty(kdniaoSubscribeExcludeStoreList) && kdniaoSubscribeExcludeStoreList.contains(storeId)) {
            return storeId;
        }

        return CommonLogisticsConstants.PLATFORM_CAINIAO;
    }

}
