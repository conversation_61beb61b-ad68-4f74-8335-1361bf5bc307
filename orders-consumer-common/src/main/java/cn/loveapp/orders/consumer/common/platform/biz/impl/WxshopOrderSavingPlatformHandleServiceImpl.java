package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtKwaishop;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtWxshop;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

/**
 * 微信小商店 订单存储 处理服务
 *
 * <AUTHOR>
 * @date 2021/12/24 17:13
 */
@Service
public class WxshopOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    @Override
    public TcTradeExtWxshop generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getWxshopTradeExt();
    }

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();
		String receiverMobile = originDecryptData.getReceiverMobile();
		String receiverPhone = originDecryptData.getReceiverPhone();

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			receiverAddress,
			trade.getReceiverZip(),
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_WXSHOP;
    }
}
