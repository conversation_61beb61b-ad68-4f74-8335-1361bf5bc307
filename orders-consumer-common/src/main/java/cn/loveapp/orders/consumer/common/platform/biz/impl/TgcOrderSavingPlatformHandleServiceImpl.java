package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import com.taobao.api.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtTgc;
import cn.loveapp.orders.consumer.common.bo.TradeBo;

/**
 * <AUTHOR>
 * @date 2025-04-14 15:33
 * @description: 淘工厂 订单存储处理服务实现类
 */
@Service
public class TgcOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {
    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {

    }

    @Override
    public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId,
        String appName) {
        AyTrade trade = tradeBo.getTrade();

        OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
        String buyerNick = originDecryptData.getBuyerNick();
        String receiverAddress = originDecryptData.getReceiverAddress();
        String receiverName = originDecryptData.getReceiverName();

        // 京东手机号返回每次都在变，忽略md5统计方便合单（自动合单会在调用oaid校验接口判断合单）
        return orderMergePlatformHandleService.generateMergeMd5(buyerNick, trade.getReceiverCountry(),
            trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(), receiverAddress,
            trade.getReceiverZip(), receiverName, null, null, platformId, appName);
    }

    @Override
    public TcTradeExtTgc generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getTgcTradeExt();
    }

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {

        tradeSearch.setOaid(trade.getOaid());
        appendSearchESNumIid(trade, tradeSearch);
    }

    @Override
    public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {
        List<TcSubOrder> lastTcSubOrder = tradeBo.getLastTcSubOrder();
        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
        if (lastAyTradeMain != null) {
            AyTrade trade = tradeBo.getTrade();
            if (CollectionUtils.isNotEmpty(lastTcSubOrder) && CollectionUtils.isNotEmpty(trade.getOrders())) {
                Map<String, TcSubOrder> oidAndTcSubOrderInfoMao = lastTcSubOrder.stream().collect(Collectors.toMap(TcSubOrder::getOid, Function.identity()));
                for (Order order : trade.getOrders()) {
                    TcSubOrder tcSubOrder = oidAndTcSubOrderInfoMao.get(order.getOidStr());
                    if (StringUtils.isEmpty(order.getInvoiceNo()) && (StringUtils.isNotEmpty(tcSubOrder.getInvoiceNo()))) {
                        order.setInvoiceNo(tcSubOrder.getInvoiceNo());
                    }
                }
            }
        }
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TGC;
    }
}
