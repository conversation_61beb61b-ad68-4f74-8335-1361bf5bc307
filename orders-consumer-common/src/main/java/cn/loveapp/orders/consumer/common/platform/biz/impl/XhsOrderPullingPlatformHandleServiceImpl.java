package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;

/**
 * <AUTHOR>
 * @date 2023-04-01 16:06
 * @Description:
 */
@Service
public class XhsOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {

    private static final int PART = 90;

    @Override
    public List<SyncOrdersProgress> generateProgress(List<SyncOrdersProgress> soldGetProgressList,
        LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        if (CollectionUtils.isEmpty(soldGetProgressList)) {
            soldGetProgressList = getSoldGetProgresses(startCreated, endCreated);
        }
        return soldGetProgressList;
    }

    protected List<SyncOrdersProgress> getSoldGetProgresses(LocalDateTime startCreated, LocalDateTime endCreated) {
        if (endCreated == null) {
            endCreated = LocalDateTime.now();
        }
        List<SyncOrdersProgress> soldGetProgressList = Lists.newArrayList();
        LocalDateTime startTime = endCreated.minusDays(1);
        for (int i = 0; i < PART; i++) {
            SyncOrdersProgress soldGetProgress = new SyncOrdersProgress(startTime, endCreated);
            soldGetProgressList.add(soldGetProgress);
            endCreated = endCreated.minusDays(1);
            startTime = startTime.minusDays(1);

            if (startCreated != null && startTime.isBefore(startCreated)) {
                break;
            }
        }
        return soldGetProgressList;
    }

    @Override
    public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated,
        String platformId, String appName) throws Exception {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_XHS;
    }
}
