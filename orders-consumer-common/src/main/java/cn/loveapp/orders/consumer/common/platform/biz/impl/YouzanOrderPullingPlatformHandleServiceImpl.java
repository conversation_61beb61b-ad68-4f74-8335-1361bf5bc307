package cn.loveapp.orders.consumer.common.platform.biz.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;

/**
 * 有赞订单拉取扩展接口
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@Service
public class YouzanOrderPullingPlatformHandleServiceImpl extends TaoOrderPullingPlatformHandleServiceImpl
    implements OrderPullingPlatformHandleService {

    protected static final Integer PART = 1;

    @Override
    protected int getPartSize() {
        return PART;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_YOUZAN;
    }
}
