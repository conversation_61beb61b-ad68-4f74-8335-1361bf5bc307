package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.proto.SyncOrdersProgress;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.consumer.common.platform.biz.OrderPullingPlatformHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-15 11:40
 * @description: 处理TikTok拉取订单服务接口
 */
@Service
public class TikTokOrderPullingPlatformHandleServiceImpl implements OrderPullingPlatformHandleService {

    private static final String START_TIME = "1972-01-01 00:00:00";
    @Override
    public List<SyncOrdersProgress> generateProgress(List<SyncOrdersProgress> syncOrdersProgressList,
        LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        if (CollectionUtils.isEmpty(syncOrdersProgressList)) {
            SyncOrdersProgress syncOrdersProgress = new SyncOrdersProgress();
            syncOrdersProgress.setStartTime(DateUtil.parseString(START_TIME));
            syncOrdersProgress.setEndTime(LocalDateTime.now());
            return Lists.newArrayList(syncOrdersProgress);
        }

        return syncOrdersProgressList;
    }

    @Override
    public List<SyncOrdersProgress> generateIncrementProgress(LocalDateTime startCreated, LocalDateTime endCreated, String platformId, String appName) throws Exception {
        return null;
    }

    @Override
    public boolean isSoldGetNeedSend2FullInfo(String platformId, String appName) {
        return false;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TIKTOK;
    }
}
