package cn.loveapp.orders.consumer.common.platform.biz.impl;


import cn.loveapp.common.constant.CommonPlatformConstants;
import org.springframework.stereotype.Service;

/**
 * 抖店订单保存后续处理
 *
 * <AUTHOR>
 * @Date 2023/6/2 14:50
 */
@Service
public class KwaishopOrderAfterSavingPlatformHandleService extends AbstractOrderAfterSavePlatformHandleServiceImpl {

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
