package cn.loveapp.orders.consumer.common.platform.biz.impl;

import javax.validation.constraints.NotNull;

import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtAyPurchase;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;
import cn.loveapp.orders.dto.common.AyDistributeInfo;
import com.google.common.collect.Lists;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.consumer.common.bo.TradeBo;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 爱用小店 订单存储 处理服务
 * <AUTHOR>
 * @date 2021/12/24 17:13
 */
@Service
public class AiyongOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AiyongOrderSavingPlatformHandleServiceImpl.class);

	@Autowired
	private OrderSavingPlatformHandleService orderSavingPlatformHandleService;

	@Override
	public TcTradeExtAyPurchase generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
		return tradeBo.getTrade().getAyPurchaseTradeExt();
	}

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();

		if (trade.getAyPurchaseTradeExt() != null) {
			TcTradeExtAyPurchase purchaseTradeExt = trade.getAyPurchaseTradeExt();
			String distributeAppName = purchaseTradeExt.getDistributeAppName();
			String distributeStoreId = purchaseTradeExt.getDistributeStoreId();
			String distributeMergeMd5 = purchaseTradeExt.getDistributeMergeMd5();

			if (distributeMergeMd5 == null) {
				distributeMergeMd5 = orderSavingPlatformHandleService.generateMergeMd5(tradeBo, lastAyTradeMain, distributeStoreId, distributeAppName);
			}

			AyDistributeInfo ayDistributeInfo = trade.getAyDistributeInfo();
			if (ayDistributeInfo != null && distributeMergeMd5 != null) {
				String sellerId = ayDistributeInfo.getSellerId();
				String md5Str = sellerId + distributeMergeMd5;
				return DigestUtils.md5Hex(md5Str).toUpperCase();
			}
		}

		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();
		String receiverMobile = originDecryptData.getReceiverMobile();
		String receiverPhone = originDecryptData.getReceiverPhone();

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			receiverAddress,
			trade.getReceiverZip(),
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

	@Override
	public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch, Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
		if (CollectionUtils.isEmpty(tradeSearch.getSubOrders())) {
			return;
		}

        Map<String, String> oid2NumIidMap = null;
        if (Objects.nonNull(trade.getAyPurchaseTradeExt())) {
            oid2NumIidMap = trade.getAyPurchaseTradeExt().getOid2NumIidMap();
        } else {
            // 手工单
            oid2NumIidMap = trade.getOid2NumIidMap();
        }
		if (Objects.isNull(oid2NumIidMap)) {
			return;
		}

		List<String> numIids = Lists.newArrayList();
		// 爱用代发有部分分销单(小红书)numIid是英文，特殊处理Ï
		for (AyTradeSearchES.SubOrder tcSubOrder : tradeSearch.getSubOrders()) {
			String numIid = oid2NumIidMap.get(tcSubOrder.getOid());
			if (StringUtils.isEmpty(numIid)) {
				continue;
			}
			numIids.add(numIid);
			tcSubOrder.setNumIid(numIid);
		}
		tradeSearch.setNumIid(ElasticsearchUtil.toUniqueList(numIids));
		tradeSearch.setNumIidAgg(OrderUtil.joinAndMd5WithPrefix(numIids));
		tradeSearch.addServiceType(generalServiceType(trade));
	}

	@Override
	public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
								 String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        Map<String, String> oid2NumIidMap = null;
        if (Objects.nonNull(trade.getAyPurchaseTradeExt())) {
            oid2NumIidMap = trade.getAyPurchaseTradeExt().getOid2NumIidMap();
        } else {
            // 手工单
            oid2NumIidMap = trade.getOid2NumIidMap();
        }
        if (Objects.isNull(oid2NumIidMap)) {
            return;
        }

		// 爱用代发有部分分销单numIid是英文，特殊处理Ï
		for (TcSubOrder tcSubOrder : subOrderList) {
			tcSubOrder.setNumIid(oid2NumIidMap.get(tcSubOrder.getOid()));
		}

	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		if (trade == null || trade.getAyPurchaseTradeExt() == null) {
			return;
		}
		TcTradeExtAyPurchase purchaseTradeExt = trade.getAyPurchaseTradeExt();
		tradeBo.setOrderSensitiveInfo(purchaseTradeExt.getOrderSensitiveInfo());
	}

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_AIYONG;
    }
}
