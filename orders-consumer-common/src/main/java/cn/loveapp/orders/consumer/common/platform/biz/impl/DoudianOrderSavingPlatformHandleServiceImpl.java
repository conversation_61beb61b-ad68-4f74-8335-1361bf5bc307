package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import cn.loveapp.orders.dto.proto.OrderTradeRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.taobao.api.SecretException;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.MongoConstant;
import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dto.AuthorizationInfoDTO;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderWithExt;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtDoudian;
import cn.loveapp.orders.common.platform.api.SecurityApiPlatformHandleService;
import cn.loveapp.orders.common.proto.TmcOrdersRequest;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;

/**
 * DoudianOrderSavingPlatformHandleServiceImpl
 *
 * <AUTHOR>
 * @date 2021/7/9
 */
@Service
public class DoudianOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {
	private static final LoggerHelper LOGGER =
		LoggerHelper.getLogger(DoudianOrderSavingPlatformHandleServiceImpl.class);

	/**
	 * 建议音尊达key
	 */
	private static final String SUGGEST_YIN_ZUN_DA_KEY = "sug_home_deliver";

	/**
	 * 自选快递信息
	 */
	private static final String SHOP_OPTIONAL_EXPRESS = "shop_optional_express_info";

    /**
     * 加密的详细地址信息
     */
    private static final String ENCRYPT_DETAIL= "encrypt_detail";

    /**
     * 详细地址
     */
    private static final String DETAIL= "detail";

    /**
     * 风控订单状态
     */
    private static final Integer RISK_CONTROL_STATUS = 105;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private SecurityApiPlatformHandleService securityApiPlatformHandleService;


	@Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
        TcTradeExtDoudian doudianTradeExt = trade.getDoudianTradeExt();
		if (doudianTradeExt != null) {
			List<TcTradeExtDoudian.ShopOrderTagUiItem> shopOrderTagUiItemList = doudianTradeExt.getShopOrderTagUi();
			Set<String> serviceTypeSet = new HashSet<>();
			if (shopOrderTagUiItemList != null) {
				shopOrderTagUiItemList.forEach(shopOrderTagUiItem -> {
					if (SUGGEST_YIN_ZUN_DA_KEY.equals(shopOrderTagUiItem.getKey())) {
						tradeSearch.setIsSuggestYinZunDa(true);
					}
					if (OrderServiceType.DOUDIAN_TYPE_LIST.contains(shopOrderTagUiItem.getKey())) {
						serviceTypeSet.add(shopOrderTagUiItem.getKey());
					}
				});
			}
            if (Objects.equals(RISK_CONTROL_STATUS, doudianTradeExt.getStatus())) {
                tradeSearch.setIsRiskControl(true);
            } else {
                tradeSearch.setIsRiskControl(false);
            }
			List<TcTradeExtDoudian.SubItem> subItems = doudianTradeExt.getSubItems();

			if (CollectionUtils.isNotEmpty(subItems)) {
				Set<String> warehouseIdSet = new HashSet<>();
				Set<String> outWarehouseIdSet = new HashSet<>();
				Set<String> warehouseNameSet = new HashSet<>();
				for (TcTradeExtDoudian.SubItem subItem : subItems) {
					if (CollectionUtils.isNotEmpty(subItem.getInventoryList())) {
						subItem.getInventoryList().forEach(inventoryListItem -> {
							add2Set(warehouseNameSet, inventoryListItem.getWarehouseName());
							add2Set(warehouseIdSet, inventoryListItem.getWarehouseId());
							add2Set(outWarehouseIdSet, inventoryListItem.getOutWarehouseId());
						});
					}
					if (CollectionUtils.isNotEmpty(subItem.getSkuOrderTagUi())) {
						subItem.getSkuOrderTagUi().forEach(shopOrderTagUiItem -> {
							if (OrderServiceType.DOUDIAN_TYPE_LIST.contains(shopOrderTagUiItem.getKey())) {
								serviceTypeSet.add(shopOrderTagUiItem.getKey());
							}
						});
					}
				}
				tradeSearch.setWarehouseNameList(ElasticsearchUtil.toList(warehouseNameSet));
				tradeSearch.setWarehouseIdList(ElasticsearchUtil.toList(warehouseIdSet));
				tradeSearch.setOutWarehouseIdList(ElasticsearchUtil.toList(outWarehouseIdSet));
			}
			Map<String, String> orderTag = doudianTradeExt.getOrderTag();
			if (orderTag != null && orderTag.containsKey(SHOP_OPTIONAL_EXPRESS)) {
				serviceTypeSet.add(OrderServiceType.optional_express.name());
			}

			// 订单标签可能从有到无，为了保证标签更新正常（es入库更新逻辑：更新非空的字段），就算订单标签为空，也设置一个空列表保证字段正常更新
			tradeSearch.addServiceType(new ArrayList<>(serviceTypeSet));
		} else {
            appendSearchESNumIid(trade, tradeSearch);
        }

        tradeSearch.addServiceType(generalServiceType(trade));
	}

	@Override
	public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
		String asdpAds = trade.getAsdpAds();
		if (!StringUtils.isEmpty(asdpAds)) {
			ayTradeMain.setAsdpAds(asdpAds);
		}
        TcTradeExtDoudian doudianTradeExt = trade.getDoudianTradeExt();
		if (doudianTradeExt != null) {
			Set<String> serviceTypeSet = new HashSet<>();
			List<TcTradeExtDoudian.ShopOrderTagUiItem> shopOrderTagUiItemList = doudianTradeExt.getShopOrderTagUi();
			if (shopOrderTagUiItemList != null) {
				shopOrderTagUiItemList.forEach(shopOrderTagUiItem -> {
					if (SUGGEST_YIN_ZUN_DA_KEY.equals(shopOrderTagUiItem.getKey())) {
						ayTradeMain.setIsSuggestYinZunDa(true);
					}
					if (OrderServiceType.DOUDIAN_TYPE_LIST.contains(shopOrderTagUiItem.getKey())) {
						serviceTypeSet.add(shopOrderTagUiItem.getKey());
					}
				});
			}
			List<TcTradeExtDoudian.SubItem> subItems = doudianTradeExt.getSubItems();
			if (CollectionUtils.isNotEmpty(subItems)) {
				for (TcTradeExtDoudian.SubItem subItem : subItems) {
					if (CollectionUtils.isNotEmpty(subItem.getSkuOrderTagUi())) {
						subItem.getSkuOrderTagUi().forEach(shopOrderTagUiItem -> {
							if (OrderServiceType.DOUDIAN_TYPE_LIST.contains(shopOrderTagUiItem.getKey())) {
								serviceTypeSet.add(shopOrderTagUiItem.getKey());
							}
						});
					}
				}
			}
			Map<String, String> orderTag = doudianTradeExt.getOrderTag();
			if (orderTag != null && orderTag.containsKey(SHOP_OPTIONAL_EXPRESS)) {
				serviceTypeSet.add(OrderServiceType.optional_express.name());
			}
			ayTradeMain.addServiceType(ElasticsearchUtil.toList(serviceTypeSet));
		}
	}

	@Override
	public TcTradeExtDoudian generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
        TcTradeExtDoudian doudianTradeExt = trade.getDoudianTradeExt();
        if (lastAyTradeMain == null) {
            return doudianTradeExt;
        }

        TcOrderWithExt tcOrderWithExt = orderRepository.queryByTidWithExt(tradeBo.getTid(), tradeBo.getSellerId(),
            tradeBo.getStoreId(), tradeBo.getAppName(), Lists.newArrayList(MongoConstant.TRADE_EXT_FIELD));

        TcTradeExtDoudian dbTradeExt = tcOrderWithExt.getTradeExt(TcTradeExtDoudian.class);

        if (doudianTradeExt != null && dbTradeExt!= null) {
            doudianTradeExt.setReceiverMsg(dbTradeExt.getReceiverMsg());
            doudianTradeExt.setPostReceiverMsg(dbTradeExt.getPostReceiverMsg());
            doudianTradeExt.setAddressChangeAppliedTime(dbTradeExt.getAddressChangeAppliedTime());
            doudianTradeExt.setTaskType(dbTradeExt.getTaskType());
            // 当地址第一次发生变更时标记
            if (!Objects.equals(lastAyTradeMain.getOaid(), trade.getOaid()) && !BooleanUtils.isTrue(dbTradeExt.getHasAddressChanged())) {
                doudianTradeExt.setHasAddressChanged(Boolean.TRUE);
            }
        }


        return doudianTradeExt;
	}

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();
		String receiverMobile = originDecryptData.getReceiverMobile();
		String receiverPhone = originDecryptData.getReceiverPhone();

        if (StringUtils.isAllEmpty(buyerNick, receiverAddress, receiverName, receiverMobile, receiverPhone)) {
            // 初次校验全为空判定为解密失败，改用原始值
            buyerNick = trade.getBuyerNick();
            receiverAddress = trade.getReceiverAddress();
            receiverName = trade.getReceiverName();
            receiverMobile = trade.getReceiverMobile();
            receiverPhone = trade.getReceiverPhone();
        }

        if (StringUtils.isAllEmpty(buyerNick, receiverAddress, receiverName, receiverMobile, receiverPhone)) {
            // 二次校验全为空判定为异常
            // TODO: 先加告警日志观察什么情况会走到这个分支，再做针对的处理（2024-01-03）
            LOGGER.logError(trade.getSellerNick(), String.valueOf(trade.getTid()), "解密出来的合单五要素为空，且合单五要素初始值为空，生成合单MD5异常");
        }

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			receiverAddress,
			trade.getReceiverZip(),
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtDoudian doudianTradeExt = tradeHandleBo.getTrade().getDoudianTradeExt();
        if (doudianTradeExt == null || CollectionUtils.isEmpty(doudianTradeExt.getSubItems())) {
            appendSubOrderNumIid(trade, subOrderList);
            return;
        }
        List<TcTradeExtDoudian.SubItem> subItems = doudianTradeExt.getSubItems();
        Map<String, TcTradeExtDoudian.SubItem> oidToSubItem = subItems.stream()
            .filter(subItem -> !StringUtils.isEmpty(subItem.getOrderId()))
            .collect(Collectors.toMap(TcTradeExtDoudian.SubItem::getOrderId, subItem -> subItem));

        //将保存在主表ext内的物流公司code存入suborder内
        for (TcSubOrder tcSubOrder : subOrderList) {
            TcTradeExtDoudian.SubItem subItem = oidToSubItem.get(tcSubOrder.getOid());
            if (Objects.isNull(subItem)) {
                continue;
            }
            tcSubOrder.setLogisticsCompanyCode(subItem.getLogisticsId());
			if (CollectionUtils.isNotEmpty(subItem.getInventoryList())) {
				Set<String> warehouseIdSet = new HashSet<>();
				Set<String> outWarehouseIdSet = new HashSet<>();
				Set<String> warehouseNameSet = new HashSet<>();
				subItem.getInventoryList().forEach(inventoryListItem -> {
					add2Set(warehouseNameSet, inventoryListItem.getWarehouseName());
					add2Set(warehouseIdSet, inventoryListItem.getWarehouseId());
					add2Set(outWarehouseIdSet, inventoryListItem.getOutWarehouseId());
				});
				tcSubOrder.setWarehouseNameList(ElasticsearchUtil.toList(warehouseNameSet));
				tcSubOrder.setWarehouseIdList(ElasticsearchUtil.toList(warehouseIdSet));
				tcSubOrder.setOutWarehouseIdList(ElasticsearchUtil.toList(outWarehouseIdSet));
			}
        }
    }

	private <T> void add2Set(Set<T> set, T value) {
		if (value != null) {
			set.add(value);
		}
	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        doAppendDesensitizationInfo(tradeBo, platformId, appName);
    }

    @Override
    public TcOrder appendAddressChangeAppliedInfo(AyTradeMain newAyTradeMain,
        TmcOrdersRequest.TradeAddrModifiedBySelf tradeAddrModifiedBySelfString, LocalDateTime modified,
        String platformId, String appName) {

        if (newAyTradeMain == null || tradeAddrModifiedBySelfString == null) {
            return null;
        }

        String tid = newAyTradeMain.getTid();
        String sellerId = newAyTradeMain.getSellerId();
        String sellerNick = newAyTradeMain.getSellerNick();
        TcOrderWithExt tcOrderWithExt = orderRepository.queryByTidWithExt(tid, sellerId, platformId, appName,
            Lists.newArrayList(MongoConstant.SELLER_ID_FIELD, MongoConstant.SELLER_NICK_FIELD,
                MongoConstant.STORE_ID_FIELD, MongoConstant.APP_NAME_FIELD, MongoConstant.TID_FIELD,
                MongoConstant.TRADE_EXT_FIELD));

        OrderTradeRequest.ReceiverInfo receiverMsg = tradeAddrModifiedBySelfString.getReceiverMsg();
        OrderTradeRequest.ReceiverInfo postReceiverMsg = tradeAddrModifiedBySelfString.getPostReceiverMsg();
        Integer receiverTaskType = tradeAddrModifiedBySelfString.getReceiverTaskType();
        // 发货后地址变更标记
        TcTradeExtDoudian tcTradeExtDoudian = tcOrderWithExt.getTradeExt(TcTradeExtDoudian.class);
        if (tcTradeExtDoudian == null) {
            tcTradeExtDoudian = new TcTradeExtDoudian();
        }

        AuthorizationInfoDTO authorization = userService.getAuthorization(sellerNick, sellerId, null, platformId, appName);
        String topSession = null;
        if (authorization != null) {
            topSession = authorization.getTopSession();
        }

        // 解密详细地址信息
        String postReceiverAddr = postReceiverMsg.getAddr();
        String receiverMAddr = receiverMsg.getAddr();
        JSONObject postReceiverAddrObj = JSON.parseObject(postReceiverAddr);
        JSONObject receiverMAddrObj = JSON.parseObject(receiverMAddr);
        String postReceiverEncryptDetail = postReceiverAddrObj.getString(ENCRYPT_DETAIL);
        String receiverEncryptDetail = receiverMAddrObj.getString(ENCRYPT_DETAIL);

        // 组装解密对象
        HashMap<String, String> encryptDataAndDesensitizationDataMap = new HashMap<>();
        encryptDataAndDesensitizationDataMap.put(postReceiverMsg.getEncryptName(), postReceiverMsg.getEncryptName());
        encryptDataAndDesensitizationDataMap.put(postReceiverMsg.getEncryptTel(), postReceiverMsg.getEncryptTel());
        encryptDataAndDesensitizationDataMap.put(receiverMsg.getEncryptName(), receiverMsg.getEncryptName());
        encryptDataAndDesensitizationDataMap.put(receiverMsg.getEncryptTel(), receiverMsg.getEncryptTel());
        encryptDataAndDesensitizationDataMap.put(postReceiverEncryptDetail, postReceiverEncryptDetail);
        encryptDataAndDesensitizationDataMap.put(receiverEncryptDetail, receiverEncryptDetail);

        HashMap<String, Map<String, String>> srcTidAndFieldsMap = new HashMap<>();
        srcTidAndFieldsMap.put(tid, encryptDataAndDesensitizationDataMap);
        Map<String, Map<String, String>> decryptDataAndTidMap = null;
        try {
            decryptDataAndTidMap = securityApiPlatformHandleService.decryptData(sellerNick, sellerId, topSession, srcTidAndFieldsMap, true, platformId, appName);
        } catch (SecretException e) {
            LOGGER.logError("地址变更申请中地址脱敏失败: " + e.getMessage(), e);
        }

        TcTradeExtDoudian.ReceiverInfo postReceiverInfo = new TcTradeExtDoudian.ReceiverInfo();
        BeanUtils.copyProperties(postReceiverMsg, postReceiverInfo);
        TcTradeExtDoudian.ReceiverInfo receiverInfo = new TcTradeExtDoudian.ReceiverInfo();
        BeanUtils.copyProperties(receiverMsg, receiverInfo);

        if (decryptDataAndTidMap != null) {
            Map<String, String> decryptDataMap = decryptDataAndTidMap.get(tid);
            if (decryptDataMap != null) {
                if (StringUtils.isNotEmpty(decryptDataMap.get(postReceiverInfo.getEncryptName()))) {
                    postReceiverInfo.setName(decryptDataMap.get(postReceiverInfo.getEncryptName()));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(postReceiverInfo.getEncryptTel()))) {
                    postReceiverInfo.setTel(decryptDataMap.get(postReceiverInfo.getEncryptTel()));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(postReceiverEncryptDetail))) {
                    postReceiverAddrObj.put(DETAIL, decryptDataMap.get(postReceiverEncryptDetail));
                    postReceiverInfo.setAddr(JSON.toJSONString(postReceiverAddrObj));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(receiverInfo.getEncryptName()))) {
                    receiverInfo.setName(decryptDataMap.get(receiverInfo.getEncryptName()));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(receiverInfo.getEncryptTel()))) {
                    receiverInfo.setTel(decryptDataMap.get(receiverInfo.getEncryptTel()));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(postReceiverEncryptDetail))) {
                    postReceiverAddrObj.put(DETAIL, decryptDataMap.get(postReceiverEncryptDetail));
                    receiverInfo.setAddr(JSON.toJSONString(postReceiverAddrObj));
                }

                if (StringUtils.isNotEmpty(decryptDataMap.get(receiverEncryptDetail))) {
                    receiverMAddrObj.put(DETAIL, decryptDataMap.get(receiverEncryptDetail));
                    receiverInfo.setAddr(JSON.toJSONString(receiverMAddrObj));
                }
            }
        }

        tcTradeExtDoudian.setPostReceiverMsg(postReceiverInfo);
        tcTradeExtDoudian.setReceiverMsg(receiverInfo);
        tcTradeExtDoudian.setTaskType(receiverTaskType);
        if (modified != null) {
            tcTradeExtDoudian.setAddressChangeAppliedTime(DateUtil.convertLocalDateTimetoDate(modified));
        }

        TcOrder<TcTradeExtDoudian> needUpdateTcOrder = new TcOrder<>();
        needUpdateTcOrder.setSellerId(sellerId);
        needUpdateTcOrder.setTid(tid);
        needUpdateTcOrder.setStoreId(platformId);
        needUpdateTcOrder.setAppName(appName);
        needUpdateTcOrder.setTradeExt(tcTradeExtDoudian);
        return needUpdateTcOrder;
    }

    @Override
    public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        Integer sellerFlag = trade.getSellerFlag() == null ? null : trade.getSellerFlag().intValue();
        String sellerMemo = trade.getSellerMemo();
        AyTradeMain lastAyTradeMain = tradeBo.getLastAyTradeMain();
        Integer oldSellerFlag = null;
        String oldSellerMemo = null;
        if (lastAyTradeMain != null) {
            oldSellerFlag = lastAyTradeMain.getSellerFlag();
            oldSellerMemo = lastAyTradeMain.getSellerMemo();
        }

        if (!Objects.equals(sellerFlag, oldSellerFlag) || !Objects.equals(sellerMemo, oldSellerMemo)) {
            tradeBo.setIsModifyFlagOrMemo(true);
        }
    }

    @Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_DOUDIAN;
	}

}
