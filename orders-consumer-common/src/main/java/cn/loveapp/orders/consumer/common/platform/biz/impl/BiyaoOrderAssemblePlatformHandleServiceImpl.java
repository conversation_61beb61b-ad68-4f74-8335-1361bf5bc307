package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.entity.AyPurchaseOrderInfo;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.MathUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAssemblePlatformHandleService;
import cn.loveapp.orders.consumer.common.service.TradeSearchESHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeReceiverDistrictListMongoHandleService;
import cn.loveapp.orders.consumer.common.service.impl.mongo.TradeSubMongoHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 必要存单入库扩展接口
 *
 * @program: orders-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2022/9/16 15:40
 **/
@Service
public class BiyaoOrderAssemblePlatformHandleServiceImpl implements OrderAssemblePlatformHandleService {

    @Autowired
    private AyStatusCodeConfigService statusCodeConfigService;

    @Autowired
    private TradeSearchESHandleService handleEsService;

    @Autowired
    private TradeMongoHandleService tradeMongoHandleService;

    @Autowired
    private TradeSubMongoHandleService tradeSubMongoHandleService;

    @Autowired
    private TradeReceiverDistrictListMongoHandleService tradeReceiverDistrictListMongoHandleService;

    @Override
    public void handleMain(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, String platformId,
        String appName) {
        // main 主要用于合单逻辑处理
        AyPurchaseOrderInfo ayPurchaseOrderInfo = tradeBo.getAyPurchaseOrderInfo();
        TcOrder cpTcOrder = ayPurchaseOrderInfo.getCpTcOrder();
        AyTradeMain newTradeMain = tradeHandleBo.toAyTradeMain();
        newTradeMain.setAyTid(ayTid);
        newTradeMain
            .setTaoStatus(TaobaoStatusConstant.transformOrderStatus(platformId, ayPurchaseOrderInfo.getHyStatus()));
        newTradeMain.setAyStatus(MathUtil.parseString(statusCodeConfigService.getPlatformStatusAyStatus(newTradeMain.getTaoStatus())));
        newTradeMain.setCreated(ayPurchaseOrderInfo.getCreated());
        newTradeMain.setModified(ayPurchaseOrderInfo.getModified());
        newTradeMain.setMergeMd5(cpTcOrder.getMergeMd5());
        newTradeMain.setTradeSource(cpTcOrder.getAppName());
        newTradeMain.setEncryptionType(cpTcOrder.getEncryptionType());
        newTradeMain.setBuyerNick(cpTcOrder.getBuyerNick());
        newTradeMain.setPromiseServiceType(cpTcOrder.getPromiseServiceType());
        newTradeMain.setDistributeStatus(ayPurchaseOrderInfo.getHyStatus());
        tradeBo.setAyTradeMain(newTradeMain);
    }

    @Override
    public void handleEs(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, String platformId,
        String appName) {
        handleEsService.handlePurchase(tradeHandleBo, tradeBo, ayTid);
    }

    @Override
    public void handleMongo(TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, HashMap<String, String> ayOid,
        String platformId, String appName) {
        // 1 先处理主单
        tradeMongoHandleService.handlePurchase(tradeHandleBo, tradeBo, ayTid);
        // 2 处理子单
        tradeSubMongoHandleService.handlePurchase(tradeHandleBo, tradeBo, ayTid);
        // 9 处理收货区域信息
        tradeReceiverDistrictListMongoHandleService.handlePurchase(tradeHandleBo, tradeBo, ayTid);
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_BIYAO;
    }
}
