package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.mongo.*;
import cn.loveapp.orders.common.utils.ConvertUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.OrderSensitiveInfo;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;

/**
 * <AUTHOR>
 * @date 2023-04-02 14:46
 * @Description: 小红书存单入库扩展接口实现类
 */
@Service
public class XhsOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(XhsOrderSavingPlatformHandleServiceImpl.class);

    @Autowired
    private OrderRepository orderRepository;

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
                                    Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {

        // 小红书退款状态为主单维度，判断是否为退款订单
        tradeSearch.setIsRefund(isRefundOrder(trade));

        // 小红书numIid为skuId是英文，特殊处理
        TcTradeExtXhs xhsTradeExt = trade.getXhsTradeExt();
        if (xhsTradeExt == null || trade.getAyCustomOrder() != null) {
            appendSearchESNumIid(trade, tradeSearch);
            tradeSearch.addServiceType(generalServiceType(trade));
            return;
        }

        ArrayList<AyTradeSearchES.SubOrder> subOrders = tradeSearch.getSubOrders();
        if (CollectionUtils.isNotEmpty(xhsTradeExt.getSubOrder()) && CollectionUtils.isNotEmpty(subOrders)) {
            List<String> numIids = new ArrayList<>();
            Map<String, String> oid2NumIidMap = xhsTradeExt.getSubOrder()
                    .stream().filter(subOrder -> StringUtils.isNoneEmpty(subOrder.getNumIid(), subOrder.getOid()))
                    .collect(Collectors.toMap(TcTradeExtXhs.SubOrder::getOid, TcTradeExtXhs.SubOrder::getNumIid, (v1, v2) -> v1));

            for (AyTradeSearchES.SubOrder order : subOrders) {
                String numIid = oid2NumIidMap.get(order.getOid());
                if (StringUtils.isEmpty(numIid)) {
                    continue;
                }
                numIids.add(numIid);
                order.setNumIid(numIid);
            }
            tradeSearch.setNumIid(ElasticsearchUtil.toUniqueList(numIids));
            tradeSearch.setNumIidAgg(OrderUtil.joinAndMd5WithPrefix(numIids));
        }

        OrderSensitiveInfo orderSensitiveInfo = getOrderSensitiveInfo(tradeBo);
        String recevier = OrderUtil.generateReceiverForSearch(orderSensitiveInfo.getDesensitiseReceiverMobile(), orderSensitiveInfo.getDesensitiseReceiverMobile(), orderSensitiveInfo.getDesensitiseReceiverAddress(),
            trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(),
            trade.getReceiverCountry(), trade.getReceiverTown(), orderSensitiveInfo.getDesensitiseReceiverName());

        // 设置脱敏后的收件人信息（未使用爱用加密）
        tradeSearch.setReceiverName(orderSensitiveInfo.getDesensitiseReceiverName());
        tradeSearch.setReceiver(recevier);
        tradeSearch.setReceiverMobile(orderSensitiveInfo.getDesensitiseReceiverMobile());
        tradeSearch.setReceiverPhone(orderSensitiveInfo.getDesensitiseReceiverMobile());
        ArrayList<String> serviceType = getServiceType(xhsTradeExt.getOrderTagList(), trade);
        tradeSearch.addServiceType(serviceType);
    }

    @Override
    public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid,
        AyTrade trade, String platformId, String appName) {

        // 小红书退款状态为主单维度，判断是否为退款订单
        ayTradeMain.setIsRefund(isRefundOrder(trade));

        // 小红书没有买家昵称当推送信息为待发货之后状态，拿库中订单进行赋值
        if (StringUtils.isEmpty(ayTradeMain.getBuyerNick()) && tradeBo.getLastAyTradeMain() != null) {
            ayTradeMain.setBuyerNick(tradeBo.getLastAyTradeMain().getBuyerNick());
            ayTradeMain.setEncryptionType(tradeBo.getLastAyTradeMain().getEncryptionType());
        }

        TcTradeExtXhs xhsTradeExt = trade.getXhsTradeExt();
        if (xhsTradeExt != null) {
            ayTradeMain.addServiceType(getServiceType(xhsTradeExt.getOrderTagList(), trade));
        }

    }

    /**
     * 将小红书被爱用加密的脱敏收件人信息转换为解密后的脱敏信息
     *
     * @param tradeBo
     * @return
     */
    private OrderSensitiveInfo getOrderSensitiveInfo(TradeBo tradeBo) {
        String desensitiseReceiverName = null;
        String desensitiseReceiverPhone = null;
        String desensitiseReceiverAddress = null;
        OrderSensitiveInfo orderSensitiveInfo = tradeBo.getOrderSensitiveInfo();
        if (orderSensitiveInfo != null) {
            // 新订单或者md5发生改变的订单（待发货订单）
            desensitiseReceiverName = tradeBo.getOrderSensitiveInfo().getDesensitiseReceiverName();
            desensitiseReceiverPhone = tradeBo.getOrderSensitiveInfo().getDesensitiseReceiverPhone();
            desensitiseReceiverAddress = tradeBo.getOrderSensitiveInfo().getDesensitiseReceiverAddress();
        } else if (tradeBo.getLastAyTradeMain() != null) {
            // 老订单
            desensitiseReceiverName = tradeBo.getLastAyTradeMain().getDesensitiseReceiverName();
            desensitiseReceiverAddress = tradeBo.getLastAyTradeMain().getDesensitiseReceiverAddress();
            desensitiseReceiverPhone = tradeBo.getLastAyTradeMain().getDesensitiseReceiverPhone();
        }

        AesUtil aes = AesUtil.getInstance();
        OrderSensitiveInfo sensitiveInfo = new OrderSensitiveInfo();
        sensitiveInfo.setDesensitiseReceiverName(
            StringUtils.isEmpty(desensitiseReceiverName) ? null : aes.decryptForTrade(desensitiseReceiverName));
        sensitiveInfo.setDesensitiseReceiverAddress(
            StringUtils.isEmpty(desensitiseReceiverAddress) ? null : aes.decryptForTrade(desensitiseReceiverAddress));

        if (!StringUtils.isEmpty(desensitiseReceiverPhone)) {
            String phoneStr = null;
            try {
                phoneStr = aes.decryptForPhone(desensitiseReceiverPhone, taobaoAppConfig.getSessionKey());
            } catch (Exception e) {
                LOGGER.logError("xhs 收件人手机解密失败" + e.getMessage(), e);
            }
            sensitiveInfo.setDesensitiseReceiverMobile(StringUtils.isEmpty(phoneStr) ? null : phoneStr);
        }
        return sensitiveInfo;
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
        String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtXhs xhsTradeExt = tradeHandleBo.getTrade().getXhsTradeExt();
        if (xhsTradeExt == null || trade.getAyCustomOrder() != null) {
            appendSubOrderNumIid(trade, subOrderList);
            return;
        }

        Map<String, TcTradeExtXhs.SubOrder> oidToSubItem = null;
        if (xhsTradeExt == null || CollectionUtils.isEmpty(xhsTradeExt.getSubOrder())) {
            oidToSubItem = new HashMap<>(0);
        } else {
            List<TcTradeExtXhs.SubOrder> subOrders = xhsTradeExt.getSubOrder();
            oidToSubItem = subOrders.stream().collect(Collectors.toMap(TcTradeExtXhs.SubOrder::getOid, subItem -> subItem));
        }

        // 小红书numIid为skuId是英文，特殊处理
        for (TcSubOrder tcSubOrder : subOrderList) {
            // 保存ext扩展信息到子表
            TcTradeExtXhs.SubOrder subOrder = oidToSubItem.get(tcSubOrder.getOid());
            if (Objects.isNull(subOrder)) {
                continue;
            }
            tcSubOrder.setLogisticsCompanyCode(subOrder.getExpressCompanyCode());
            tcSubOrder.setInvoiceNo(subOrder.getExpressTrackingNo());
            tcSubOrder.setNumIid(subOrder.getNumIid());
        }

    }

    @Override
    public TcTradeExtXhs generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getXhsTradeExt();
    }

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        if (StringUtils.isAnyEmpty(trade.getBuyerNick(), trade.getReceiverAddress(), trade.getReceiverName())) {
            return;
        }
        doAppendDesensitizationInfo(tradeBo, platformId, appName);

    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_XHS;
    }

    @Override
    public String generateMergeMd5(TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
        String tidStr = trade.getTidStr();
        String sellerNick = trade.getSellerNick();
        boolean hasLast = Objects.nonNull(lastAyTradeMain);



        String buyerNick = getReceiverFormLast(originDecryptData.getBuyerNick(), hasLast ? lastAyTradeMain.getBuyerNick() : null, sellerNick, tidStr);
        String receiverAddress = getReceiverFormLast(originDecryptData.getReceiverAddress(), hasLast ? lastAyTradeMain.getReceiverAddress() : null, sellerNick, tidStr);
        String receiverName = getReceiverFormLast(originDecryptData.getReceiverName(), hasLast ? lastAyTradeMain.getReceiverName() : null, sellerNick, tidStr);
        String receiverPhone = getReceiverFormLast(originDecryptData.getReceiverPhone(), hasLast ? lastAyTradeMain.getReceiverPhone() : null, sellerNick, tidStr);
        String receiverMobile = originDecryptData.getReceiverMobile();
        String receiverCountry = ConvertUtil.findFirstNotEmpty(trade.getReceiverCountry(), hasLast ? lastAyTradeMain.getReceiverCountry() : null);
        String receiverState = ConvertUtil.findFirstNotEmpty(trade.getReceiverState(), hasLast ? lastAyTradeMain.getReceiverState() : null);
        String receiverCity = ConvertUtil.findFirstNotEmpty(trade.getReceiverCity(), hasLast ? lastAyTradeMain.getReceiverCity() : null);
        String receiverDistrict = ConvertUtil.findFirstNotEmpty(trade.getReceiverDistrict(), hasLast ? lastAyTradeMain.getReceiverDistrict() : null);
        String receiverZip = ConvertUtil.findFirstNotEmpty(trade.getReceiverZip(), hasLast ? lastAyTradeMain.getReceiverZip() : null);

        return orderMergePlatformHandleService.generateMergeMd5(buyerNick, receiverCountry,
            receiverState, receiverCity, receiverDistrict, receiverAddress,
            receiverZip, receiverName, receiverMobile, receiverPhone, platformId, appName);
    }

    @Override
    public void appendToTradeInfo(TradeBo tradeBo, String platformId, String appName) {}

    /**
     * 检验是否为退款订单
     *
     * @param ayTrade
     * @return
     */
    private Boolean isRefundOrder(AyTrade ayTrade) {
        TcTradeExtXhs xhsTradeExt = ayTrade.getXhsTradeExt();
        if (xhsTradeExt != null) {
            if (!TaobaoStatusConstant.NO_REFUND.equals(xhsTradeExt.getRefundStatus())) {
                return true;
            }
        }
        return null;
    }

    private ArrayList<String> getServiceType(List<String> tagList, AyTrade trade) {
        Set<String> typeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tagList)) {
            tagList.stream().filter(tag -> OrderServiceType.XHS_TYPE_LIST.stream()
                    .anyMatch(type -> type.equalsIgnoreCase(tag)))
                .forEach(tag -> typeSet.add(tag.toLowerCase()));
        }

        typeSet.addAll(generalServiceType(trade));
        return ElasticsearchUtil.toList(typeSet);
    }
}
