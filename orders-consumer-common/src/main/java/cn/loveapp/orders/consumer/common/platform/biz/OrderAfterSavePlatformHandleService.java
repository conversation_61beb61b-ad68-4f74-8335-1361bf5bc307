package cn.loveapp.orders.consumer.common.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonDispatcherHandler;
import cn.loveapp.orders.consumer.common.bo.TradeBo;

/**
 * 订单保存后需操作
 *
 * <AUTHOR>
 * @Date 2023/6/2 14:42
 */
public interface OrderAfterSavePlatformHandleService extends CommonDispatcherHandler {

    /**
     * 物流信息补充（订阅）
     */
    void appendToLogisticsInfo(TradeBo tradeBo, String storeId, String appName);


    /**
     * 订单上报
     * @param tradeBo
     * @param storeId
     * @param appName
     */
    void orderReport(TradeBo tradeBo, String storeId, String appName);

}
