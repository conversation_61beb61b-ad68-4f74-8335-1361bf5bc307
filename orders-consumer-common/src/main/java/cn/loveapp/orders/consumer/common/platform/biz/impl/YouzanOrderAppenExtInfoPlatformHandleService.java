package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.BiyaoPlatformMapConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.mongo.*;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAppenExtInfoPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: orders-services-group
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/9/29 00:44
 **/
@Service
public class YouzanOrderAppenExtInfoPlatformHandleService implements OrderAppenExtInfoPlatformHandleService {

    @Autowired
    private OrderRepository orderRepository;

    @Override
    public void appenExtInfoToAyTrade(AyTrade ayTrade, TcOrder cpTcOrder, String platformId, String appName) {
        TcTradeExtYouzan tcTradeExtYouzan = orderRepository.queryTradeExt(cpTcOrder, TcOrderYouzan.class);

        ayTrade.setYouzanTradeExt(tcTradeExtYouzan);
    }

    @Override
    public Integer getDistributionPlatform(String platformId, String appName) {
        return BiyaoPlatformMapConstant.YOUZAN;
    }

    @Override
    public String getCpOrderId(TcSubOrder cpOrder, String platformId, String appName) {
        return getPlatformId() + cpOrder.getOid();
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_YOUZAN;
    }
}
