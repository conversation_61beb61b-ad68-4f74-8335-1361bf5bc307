package cn.loveapp.orders.consumer.common.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.OrderSensitiveInfo;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtKwaishop;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 快手电商 订单存储 处理服务
 *
 * <AUTHOR>
 */
@Service
public class KwaishopOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {

    @Override
    public TcTradeExtKwaishop generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getKwaishopTradeExt();
    }

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
		String buyerNick = originDecryptData.getBuyerNick();
		String receiverAddress = originDecryptData.getReceiverAddress();
		String receiverName = originDecryptData.getReceiverName();
		String receiverMobile = originDecryptData.getReceiverMobile();
		String receiverPhone = originDecryptData.getReceiverPhone();

		return orderMergePlatformHandleService.generateMergeMd5(
			buyerNick,
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			receiverAddress,
			trade.getReceiverZip(),
			receiverName,
			receiverMobile,
			receiverPhone,
			platformId, appName);
	}

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        OrderSensitiveInfo orderSensitiveInfo = new OrderSensitiveInfo();
        orderSensitiveInfo.setDesensitiseBuyerNick(trade.getBuyerNick());
        if (trade.getKwaishopTradeExt() == null){
            return;
        }
        orderSensitiveInfo.setDesensitiseReceiverAddress(trade.getKwaishopTradeExt().getDesensitiseAddress());
        orderSensitiveInfo.setDesensitiseReceiverMobile(trade.getKwaishopTradeExt().getDesensitiseMobile());
        orderSensitiveInfo.setDesensitiseReceiverName(trade.getKwaishopTradeExt().getDesensitiseConsignee());
    }

    @Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
        if (CollectionUtils.isEmpty(subOrderList)) {
            return;
        }

        // 保持售后状态同步， 通过售后入库更新退款状态
        appendSubOrderStatus(tradeBo.getLastTcSubOrder(), subOrderList);
        TcTradeExtKwaishop kwaishopTradeExt = tradeHandleBo.getTrade().getKwaishopTradeExt();
        if (kwaishopTradeExt == null) {
            appendSubOrderNumIid(trade, subOrderList);
            return;
        }

        //将保存在主表ext内的物流公司code存入suborder内
        for (TcSubOrder tcSubOrder : subOrderList) {
            Integer expressCode = kwaishopTradeExt.getExpressCode();
            if (Objects.isNull(expressCode)) {
                continue;
            }
            tcSubOrder.setLogisticsCompanyCode(String.valueOf(expressCode));
        }
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
