package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import cn.loveapp.orders.common.constant.OrderServiceType;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.ElasticsearchUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.common.utils.SearchIndexUtils;
import cn.loveapp.orders.dto.common.AyCustomOrderDTO;
import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Maps;
import com.taobao.api.SecretException;

import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.config.taobao.TaobaoAppConfig;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.OrderSensitiveInfo;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtBase;
import cn.loveapp.orders.common.platform.api.SecurityApiPlatformHandleService;
import cn.loveapp.orders.common.platform.biz.OrderMergePlatformHandleService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;
import cn.loveapp.orders.consumer.common.bo.TradeReceiverDistrictListMongoBo;
import cn.loveapp.orders.consumer.common.platform.biz.OrderSavingPlatformHandleService;


/**
 * 平台存单入库扩展接口
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
public abstract class AbstractOrderSavingPlatformHandleService implements OrderSavingPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractOrderSavingPlatformHandleService.class);

    protected static final String BUYER_NICK = "buyer_nick";
    protected static final String RECEIVER_NAME = "receiver_name";
    protected static final String RECEIVER_ADDRESS = "receiver_address";
    protected static final String RECEIVER_PHONE = "receiver_phone";
    protected static final String RECEIVER_MOBILE = "receiver_mobile";

	@Autowired
	protected OrderMergePlatformHandleService orderMergePlatformHandleService;

    @Autowired
    protected SecurityApiPlatformHandleService securityApiPlatformHandleService;

    @Autowired
    protected TaobaoAppConfig taobaoAppConfig;

    @Autowired
    protected UserService userService;

	@Override
	public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid, AyTrade trade, String platformId, String appName) {
	}

	@Override
    public void appendToSubOrder(List<TcSubOrder> subOrderList, TradeHandleBo tradeHandleBo, TradeBo tradeBo,
        String ayTid, AyTrade trade, String platformId, String appName) {}

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
        appendSearchESNumIid(trade, tradeSearch);
        tradeSearch.addServiceType(generalServiceType(trade));
    }

    @Override
    public void appendToTradeInfo(@NotNull TradeBo tradeBo, String platformId, String appName) {
    }

	@Override
	public <T extends TcTradeExtBase> T generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
		return null;
	}

	@Override
	public void updatingStatusAndReceiverStatus(TradeBo tradeBo, AyTrade trade,
		TradeReceiverDistrictListMongoBo tradeReceiverDistrictListMongoBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		if (lastAyTradeMain != null) {
			AyTradeMain ayTradeMain = tradeBo.getAyTradeMain();
			if (!StringUtils.equals(lastAyTradeMain.getMergeMd5(), ayTradeMain.getMergeMd5())) {
				tradeReceiverDistrictListMongoBo.setHasTradeReceiverChanged(Boolean.TRUE);
			} else {
				// 订单状态变更
				tradeReceiverDistrictListMongoBo.setOnlyUpdateStatus(true);
			}
		}
	}

	@Override
	public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId, String appName) {
		AyTrade trade = tradeBo.getTrade();
		return orderMergePlatformHandleService.generateMergeMd5(
			trade.getBuyerNick(),
			trade.getReceiverCountry(),
			trade.getReceiverState(),
			trade.getReceiverCity(),
			trade.getReceiverDistrict(),
			trade.getReceiverAddress(),
			trade.getReceiverZip(),
			trade.getReceiverName(),
			trade.getReceiverMobile(),
			trade.getReceiverPhone(),
			platformId, appName);
	}

    /**
     * 生成加密脱敏信息
     *
     * @param tid
     * @param sellerNick
     * @param orderSensitiveInfo
     */
    protected void produceEncryptOrderSensitiveInfo(String tid, String sellerNick,
        OrderSensitiveInfo orderSensitiveInfo) {
        String sessionKey = taobaoAppConfig.getSessionKey();
        String desensitiseBuyerNick = orderSensitiveInfo.getDesensitiseBuyerNick();
        String desensitiseReceiverAddress = orderSensitiveInfo.getDesensitiseReceiverAddress();
        String desensitiseReceiverMobile = orderSensitiveInfo.getDesensitiseReceiverMobile();
        String desensitiseReceiverPhone = orderSensitiveInfo.getDesensitiseReceiverPhone();
        String desensitiseReceiverName = orderSensitiveInfo.getDesensitiseReceiverName();
        AesUtil aes = AesUtil.getInstance();
        orderSensitiveInfo.setDesensitiseBuyerNick(
            StringUtils.isEmpty(desensitiseBuyerNick) ? null : aes.encryptForTrade(desensitiseBuyerNick));
        orderSensitiveInfo
            .setDesensitiseReceiverAddress(org.springframework.util.StringUtils.isEmpty(desensitiseReceiverAddress)
                ? null : aes.encryptForTrade(desensitiseReceiverAddress));
        try {
            orderSensitiveInfo.setDesensitiseReceiverMobile(StringUtils.isEmpty(desensitiseReceiverMobile) ? null
                : aes.encryptForPhone(desensitiseReceiverMobile, sessionKey));
        } catch (Exception e) {
            LOGGER.logError(sellerNick, tid, "手机号加密失败: " + e);
        }
        try {
            orderSensitiveInfo.setDesensitiseReceiverPhone(StringUtils.isEmpty(desensitiseReceiverPhone) ? null
                : aes.encryptForPhone(desensitiseReceiverPhone, sessionKey));
        } catch (Exception e) {
            LOGGER.logError(sellerNick, tid, "电话号加密失败: " + e);
        }
        orderSensitiveInfo.setDesensitiseReceiverName(
            StringUtils.isEmpty(desensitiseReceiverName) ? null : aes.encryptForTrade(desensitiseReceiverName));
    }

    /**
     * 通用脱敏信息追加
     *
     * @param tradeBo
     * @param platformId
     * @param appName
     */
    protected void doAppendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        AyTrade trade = tradeBo.getTrade();
        String tid = tradeBo.getTid();
        String sellerId = tradeBo.getSellerId();
        String sellerNick = trade.getSellerNick();
        String topSession = tradeBo.getTopSession();
        if(StringUtils.isEmpty(topSession)) {
            topSession = userService.getAuthorization(sellerNick, sellerId, platformId, appName);
        }
        try {
            Map<String, Map<String, String>> neeDecryptTidAndFieldsMap = Maps.newHashMapWithExpectedSize(1);
            Map<String, String> data = Maps.newHashMap();
            data.put(BUYER_NICK, trade.getBuyerNick());
            data.put(RECEIVER_NAME, trade.getReceiverName());
            data.put(RECEIVER_MOBILE, trade.getReceiverMobile());
            data.put(RECEIVER_PHONE, trade.getReceiverPhone());
            data.put(RECEIVER_ADDRESS, trade.getReceiverAddress());
            neeDecryptTidAndFieldsMap.put(tid, data);
            Map<String, Map<String, String>> stringMapMap = securityApiPlatformHandleService.decryptData(sellerNick,
                sellerId, topSession, neeDecryptTidAndFieldsMap, true, platformId, appName);
            Map<String, String> decryptFieldsMap = stringMapMap.get(tid);

            OrderSensitiveInfo orderSensitiveInfo = new OrderSensitiveInfo();
            orderSensitiveInfo.setDesensitiseBuyerNick(decryptFieldsMap.get(BUYER_NICK));
            orderSensitiveInfo.setDesensitiseReceiverAddress(decryptFieldsMap.get(RECEIVER_ADDRESS));
            orderSensitiveInfo.setDesensitiseReceiverMobile(decryptFieldsMap.get(RECEIVER_MOBILE));
            orderSensitiveInfo.setDesensitiseReceiverPhone(decryptFieldsMap.get(RECEIVER_PHONE));
            orderSensitiveInfo.setDesensitiseReceiverName(decryptFieldsMap.get(RECEIVER_NAME));
            LOGGER.logInfo(sellerNick, tid, "使用 API解密 mask=" + true);

            produceEncryptOrderSensitiveInfo(tid, sellerNick, orderSensitiveInfo);
            tradeBo.setOrderSensitiveInfo(orderSensitiveInfo);
        } catch (SecretException e) {
            LOGGER.logError(sellerNick, tid, "解密订单信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用收货信息获取
     * @param text
     * @param lastText
     * @param sellerNick
     * @param tid
     * @return
     */
    protected static String getReceiverFormLast(String text, String lastText, String sellerNick, String tid) {
        if (text != null) {
            return text;
        }
        if (lastText == null) {
            return null;
        }
        if(SearchIndexUtils.isEncryptData(lastText)){
            return SearchIndexUtils.extractIndex(lastText);
        }else{
            try {
                return AesUtil.getInstance().encryptForTrade(lastText);
            } catch (Exception e) {
                LOGGER.logError(sellerNick, tid, e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    public Date generateLogisticsTimeoutTime(AyTrade trade, Order order, String platformId, String appName) {
        if (order.getDeliveryTime() != null) {
            return DateUtil.parseDateString(order.getDeliveryTime());
        }

        if (trade.getDeliveryTime() != null) {
            return DateUtil.parseDateString(trade.getDeliveryTime());
        }

        return null;
    }


    protected void appendSearchESNumIid(AyTrade trade, AyTradeSearchES tradeSearch){
        Map<String, String> oid2NumIidMap = trade.getOid2NumIidMap();
        if (oid2NumIidMap == null) {
            return;
        }

        List<String> numIids = Lists.newArrayList();
        // numIid是英文，特殊处理Ï
        for (AyTradeSearchES.SubOrder tcSubOrder : tradeSearch.getSubOrders()) {
            String numIid = oid2NumIidMap.get(tcSubOrder.getOid());
            if (StringUtils.isEmpty(numIid)) {
                continue;
            }
            numIids.add(numIid);
            tcSubOrder.setNumIid(numIid);
        }
        tradeSearch.setNumIid(ElasticsearchUtil.toUniqueList(numIids));
        tradeSearch.setNumIidAgg(OrderUtil.joinAndMd5WithPrefix(numIids));
    }

    protected void appendSubOrderNumIid(AyTrade trade, List<TcSubOrder> subOrders){
        Map<String, String> oid2NumIidMap = trade.getOid2NumIidMap();
        if (oid2NumIidMap == null) {
            return;
        }

        // numIid是英文，特殊处理Ï
        for (TcSubOrder tcSubOrder : subOrders) {
            tcSubOrder.setNumIid(oid2NumIidMap.get(tcSubOrder.getOid()));
        }
    }

    /**
     * 追加历史的退款状态(接过售后入库的使用, 部分售后和售中状态有区别 导致售中状态覆盖了售后状态)
     * @param lastTcSubOrder
     * @param subOrderList
     */
    protected void appendSubOrderStatus(List<TcSubOrder> lastTcSubOrder, List<TcSubOrder> subOrderList) {
        if (CollectionUtils.isNotEmpty(lastTcSubOrder)) {
            Map<String, TcSubOrder> oidAndTcSubOrder =
                lastTcSubOrder.stream().collect(Collectors.toMap(TcSubOrder::getOid, Function.identity()));
            for (TcSubOrder tcSubOrder : subOrderList) {
                TcSubOrder dbTcSubOrder = oidAndTcSubOrder.get(tcSubOrder.getOid());
                if (dbTcSubOrder != null) {
                    tcSubOrder.setRefundStatus(dbTcSubOrder.getRefundStatus());
                } else {
                    // 有refundId没有refundStatus, 需要更新refundStatus到库中
                    if (StringUtils.isNotEmpty(tcSubOrder.getRefundId())
                        && StringUtils.isEmpty(tcSubOrder.getRefundStatus())) {
                        tcSubOrder.setRefundStatus(StringUtils.EMPTY);
                        LOGGER.logWarn(tcSubOrder.getSellerNick(), tcSubOrder.getOid(), "子单缺少 refund_status");
                    }
                }
            }
        }
    }

    protected ArrayList<String> generalServiceType(AyTrade trade) {

        AyCustomOrderDTO ayCustomOrder = trade.getAyCustomOrder();

        ArrayList<String> serviceType = new ArrayList<>();

        if (ayCustomOrder != null) {
            String associatedTid = ayCustomOrder.getAssociatedTid();
            String associatedRefundId = ayCustomOrder.getAssociatedRefundId();
            if (StringUtils.isNotEmpty(associatedRefundId)) {
                serviceType.add(OrderServiceType.refund_custom.name());
            } else if (StringUtils.isNotEmpty(associatedTid)) {
                // 订单补寄手工单
                serviceType.add(OrderServiceType.re_send_custom.name());
            }
        }
        return serviceType;
    }
}
