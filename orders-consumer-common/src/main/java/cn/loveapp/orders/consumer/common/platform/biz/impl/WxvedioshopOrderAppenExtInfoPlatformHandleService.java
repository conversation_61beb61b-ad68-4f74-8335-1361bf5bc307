package cn.loveapp.orders.consumer.common.platform.biz.impl;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.constant.BiyaoPlatformMapConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderWxvideoshop;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtWxvideoshop;
import cn.loveapp.orders.consumer.common.platform.biz.OrderAppenExtInfoPlatformHandleService;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunh<PERSON>
 * @create: 2022/9/20 18:29
 **/
@Service
public class WxvedioshopOrderAppenExtInfoPlatformHandleService implements OrderAppenExtInfoPlatformHandleService {

    @Autowired
    private OrderRepository orderRepository;

    @Override
    public void appenExtInfoToAyTrade(AyTrade ayTrade, TcOrder cpTcOrder, String platformId, String appName) {
        TcTradeExtWxvideoshop tcTradeExtWxvideoshop = orderRepository.queryTradeExt(cpTcOrder, TcOrderWxvideoshop.class);
        ayTrade.setWxvideoshopTradeExt(tcTradeExtWxvideoshop);
    }

    @Override
    public Integer getDistributionPlatform(String platformId, String appName) {
        return BiyaoPlatformMapConstant.SHIPINGHAO;
    }

    @Override
    public String getCpOrderId(TcSubOrder cpOrder, String platformId, String appName) {
        return getPlatformId() + "_" + cpOrder.getTid() + "_" + cpOrder.getOid();
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
    }
}
