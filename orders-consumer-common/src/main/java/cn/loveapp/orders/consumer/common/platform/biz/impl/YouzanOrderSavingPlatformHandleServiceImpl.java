package cn.loveapp.orders.consumer.common.platform.biz.impl;

import java.util.Set;

import javax.validation.constraints.NotNull;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.bo.OriginDecryptData;
import cn.loveapp.orders.common.entity.AyTradeMain;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcTradeExtYouzan;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.bo.TradeHandleBo;

/**
 * 有赞 order saving
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@Service
public class YouzanOrderSavingPlatformHandleServiceImpl extends AbstractOrderSavingPlatformHandleService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(YouzanOrderSavingPlatformHandleServiceImpl.class);

    @Override
    public void appendToTradeSearch(AyTrade trade, TradeBo tradeBo, AyTradeSearchES tradeSearch,
        Set<String> sbInvoiceNos, Set<String> sbLogisticsCompanys, String platformId, String appName) {
        appendSearchESNumIid(trade, tradeSearch);
        tradeSearch.addServiceType(generalServiceType(trade));
    }

    @Override
    public void appendToTradeMain(AyTradeMain ayTradeMain, TradeHandleBo tradeHandleBo, TradeBo tradeBo, String ayTid,
        AyTrade trade, String platformId, String appName) {}

    @Override
    public TcTradeExtYouzan generateTradeExt(TradeBo tradeBo, String platformId, String appName) {
        return tradeBo.getTrade().getYouzanTradeExt();
    }

    @Override
    public String generateMergeMd5(@NotNull TradeBo tradeBo, AyTradeMain lastAyTradeMain, String platformId,
        String appName) {
        AyTrade trade = tradeBo.getTrade();
        OriginDecryptData originDecryptData = tradeBo.getOriginDecryptData();
        String buyerNick = originDecryptData.getBuyerNick();
        String receiverAddress = originDecryptData.getReceiverAddress();
        String receiverName = originDecryptData.getReceiverName();
        String receiverMobile = originDecryptData.getReceiverMobile();
        String receiverPhone = originDecryptData.getReceiverPhone();

        return orderMergePlatformHandleService.generateMergeMd5(buyerNick, trade.getReceiverCountry(),
            trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(), receiverAddress,
            trade.getReceiverZip(), receiverName, receiverMobile, receiverPhone, platformId, appName);
    }

    @Override
    public void appendDesensitizationInfo(TradeBo tradeBo, String platformId, String appName) {
        doAppendDesensitizationInfo(tradeBo, platformId, appName);
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_YOUZAN;
    }

}
