<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>orders-services-group</artifactId>
        <groupId>cn.loveapp.orders</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>orders-consumers</artifactId>
    <version>1.0-SNAPSHOT</version>

    <packaging>pom</packaging>
    <name>订单-消费-组</name>
    <modules>
        <module>orders-pull-taobao-fullinfo-consumer</module>
        <module>orders-pull-taobao-soldget-consumer</module>
        <module>orders-pull-taobao-refundget-consumer</module>
        <module>orders-pull-rds-consumer</module>
        <module>orders-pull-multi-rds-consumer</module>
        <module>orders-pull-pdd-soldget-consumer</module>
        <module>orders-pull-pdd-promiseinfo-consumer</module>
        <module>orders-pull-multi-fullinfo-consumer</module>
        <module>orders-pull-multi-soldget-consumer</module>
        <module>orders-pull-multi-incrementget-consumer</module>
        <module>orders-pull-multi-rateget-consumer</module>
        <module>orders-pull-multi-distributeget-consumer</module>
        <module>orders-push-ych-consumer</module>
        <module>orders-autoconsign-consumer</module>
        <module>orders-general-conusmer</module>
        <!--		<module>orders-distribute-reflow-consumer</module>-->
        <!--        <module>orders-distribute-refund-consumer</module>-->
        <!--        <module>orders-logger-consumer</module>-->
        <module>orders-abnormal-consumer</module>
        <module>orders-pull-ay-distribute-consumer</module>
        <module>orders-db2search-consumer</module>
        <module>orders-item-label-consumer</module>
    </modules>

</project>
