<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>orders-consumers</artifactId>
		<groupId>cn.loveapp.orders</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>orders-pull-rds-consumer</artifactId>
	<name>订单-消费-淘宝-RDS推送</name>
	<description>爱用基础服务-订单服务-淘宝-RDS推送</description>

	<properties>
		<orders-common.version>1.0-SNAPSHOT</orders-common.version>
		<orders-consumer-common.version>1.1-SNAPSHOT</orders-consumer-common.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-api</artifactId>
			<version>${orders-api.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-consumer-common</artifactId>
			<version>${orders-consumer-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-common</artifactId>
			<version>${orders-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-spring-boot-web-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao</groupId>
			<artifactId>topsdk</artifactId>
		</dependency>
		<!-- 阿里淘宝 -->
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
		</dependency>
		<!--测试-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<!--<plugin>-->
			<!--<groupId>com.taobao.pandora</groupId>-->
			<!--<artifactId>pandora-boot-maven-plugin</artifactId>-->
			<!--<version>${pandora.maven-plugin.version}</version>-->
			<!--<executions>-->
			<!--<execution>-->
			<!--<phase>package</phase>-->
			<!--<goals>-->
			<!--<goal>repackage</goal>-->
			<!--</goals>-->
			<!--</execution>-->
			<!--</executions>-->
			<!--</plugin>-->
		</plugins>
	</build>
</project>
