//package cn.loveapp.orders.pull.rds.consumer;
//
//import cn.loveapp.common.constant.CommonPlatformConstants;
//import cn.loveapp.orders.common.bo.UserDbId;
//import cn.loveapp.orders.common.constant.OnsRateLimitConstant;
//import cn.loveapp.orders.common.consumer.AiyongMessageExt;
//import cn.loveapp.orders.common.entity.JdpTbRefund;
//import cn.loveapp.orders.common.entity.JdpTbTrade;
//import cn.loveapp.orders.common.exception.ResendMessageException;
//import cn.loveapp.orders.common.proto.PullRdsOrdersRefundRequestProto;
//import cn.loveapp.orders.common.proto.PullRdsOrdersRequestProto;
//import cn.loveapp.orders.common.service.JdpTbRefundService;
//import cn.loveapp.orders.common.service.JdpTbTradeService;
//import cn.loveapp.orders.common.service.UserProductionInfoExtService;
//import cn.loveapp.orders.common.service.impl.JdpTbTradeServiceImpl;
//import cn.loveapp.orders.common.utils.DateUtil;
//import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
//import cn.loveapp.orders.consumer.common.service.MigrateRdsOrderService;
//import cn.loveapp.orders.consumer.common.service.impl.MigrateRdsOrderServiceImpl;
//import com.alibaba.fastjson.JSON;
//import com.aliyun.openservices.ons.api.Action;
//import com.aliyun.openservices.ons.api.ConsumeContext;
//import com.aliyun.openservices.ons.api.Message;
//import com.ctrip.framework.apollo.enums.PropertyChangeType;
//import com.ctrip.framework.apollo.model.ConfigChange;
//import com.ctrip.framework.apollo.model.ConfigChangeEvent;
//import com.google.common.collect.Lists;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
//import org.apache.rocketmq.client.producer.DefaultMQProducer;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
//import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.boot.test.mock.mockito.SpyBean;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//
//import static org.mockito.BDDMockito.*;
//
///**
// * @Created by: IntelliJ IDEA.
// * @description: ${description}
// * @authr: jason
// * @date: 2019-01-25
// * @time: 15:47
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = WebEnvironment.NONE,
//		classes = {OrdersPullRdsConsumerTest.class, MetricsAutoConfiguration.class,
//				CompositeMeterRegistryAutoConfiguration.class, MigrateRdsOrderServiceImpl.class,
//				JdpTbTradeServiceImpl.class
//		}, properties = {OnsRateLimitConstant.ROCKETMQ_ORDERS_RATELIMIT_PULL_RDS + "=2000"})
//public class OrdersPullRdsConsumerTest {
//
//	@SpyBean
//	private OrdersPullRdsConsumer pullVIPRdsOrdersConsumer;
//
//	@MockBean
//	private RocketMqQueueHelper rocketMqQueueHelper;
//
//	@MockBean
//	private UserProductionInfoExtService userProductionInfoExtService;
//
//	@MockBean
//	private DefaultMQProducer defaultMQProducer;
//
//	@MockBean
//	private MigrateRdsOrderService migrateRdsOrderService;
//
//	@MockBean
//	private JdpTbTradeService jdpTbTradeService;
//
//	@MockBean
//	private JdpTbRefundService jdpTbRefundService;
//
//	private String sellerId = "1";
//
//	@Before
//	public void setUp() throws Exception {
//		doCallRealMethod().when(pullVIPRdsOrdersConsumer).execute(any());
//		pullVIPRdsOrdersConsumer.ignoreUserNicks.clear();
//	}
//
//	@Test
//	public void consume() throws Exception {
//		String content = JSON.toJSONString(generateRdsOrdersRequestProto());
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("文火居"), any(), any())).thenReturn(null);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(migrateRdsOrderService, never()).putJdpTradeRecords(anyString(), any(), any(), anyBoolean());
//		verify(jdpTbTradeService, never()).checkIsNoExistJdpTbTrade(eq("263519143275638593"), eq(DateUtil.parseString("2019-01-24 21:19:19")));
//		verify(pullVIPRdsOrdersConsumer, never()).rateLimiterAcquire();
//	}
//
//	@Test
//	public void consume1() throws Exception {
//		String content = JSON.toJSONString(generateRdsOrdersRequestProto());
//		UserDbId userDbId = new UserDbId("文火居", 1, 1, sellerId, sellerId, "TAO", null);
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("文火居"), any(), any())).thenReturn(userDbId);
//		when(jdpTbTradeService.checkIsNoExistJdpTbTrade(anyString(), any())).thenReturn(null);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(jdpTbTradeService).checkIsNoExistJdpTbTrade(eq("263519143275638593"), eq(DateUtil.parseString("2019-01-24 21:19:19")));
//		verify(migrateRdsOrderService, never()).putJdpTradeRecords(anyString(), any(), anyString(), anyBoolean());
//		verify(pullVIPRdsOrdersConsumer, never()).rateLimiterAcquire();
//	}
//
//
//	@Test
//	public void consume3() throws Exception {
//		String content = JSON.toJSONString(generateRdsOrdersRequestProto());
//		JdpTbTrade jdpTbTrade = generateJdpTbTrade();
//		UserDbId userDbId = new UserDbId("文火居", 1, 1, sellerId, sellerId, "TAO", null);
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("文火居"), any(), any())).thenReturn(userDbId);
//		when(jdpTbTradeService.checkIsNoExistJdpTbTrade(eq("263519143275638593"), eq(DateUtil.parseString("2019-01-24 21:19:19")))).thenReturn(jdpTbTrade);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(migrateRdsOrderService).putJdpTradeRecords(eq(sellerId), eq(jdpTbTrade), any(), anyBoolean());
//		verify(pullVIPRdsOrdersConsumer).rateLimiterAcquire();
//	}
//
//	@Test
//	public void consume4() throws Exception {
//		pullVIPRdsOrdersConsumer.ignoreUserNicks.add("文火居");
//		String content = JSON.toJSONString(generateRdsOrdersRequestProto());
//		JdpTbTrade jdpTbTrade = generateJdpTbTrade();
//		UserDbId userDbId = new UserDbId("文火居", 1, 1, sellerId, sellerId, "TAO", null);
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq("文火居"), any(), any())).thenReturn(userDbId);
//		when(jdpTbTradeService.checkIsNoExistJdpTbTrade(eq("263519143275638593"), eq(DateUtil.parseString("2019-01-24 21:19:19")))).thenReturn(jdpTbTrade);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(migrateRdsOrderService, never()).putJdpTradeRecords(eq(sellerId), eq(jdpTbTrade), any(), anyBoolean());
//		verify(pullVIPRdsOrdersConsumer, never()).rateLimiterAcquire();
//	}
//
//	@Test
//	public void configChangeLisenner() {
//		ConfigChangeEvent configChangeEvent = mock(ConfigChangeEvent.class);
//		when(configChangeEvent.getChange(anyString())).thenReturn(null);
//		pullVIPRdsOrdersConsumer.configChangeLisenner(configChangeEvent);
//		Assert.assertEquals(2000L, (long) pullVIPRdsOrdersConsumer.getRateLimiter());
//	}
//
//	@Test
//	public void configChangeLisenner1() {
//		ConfigChangeEvent configChangeEvent = mock(ConfigChangeEvent.class);
//		ConfigChange configChange = new ConfigChange("order.consumer", "test", "1000", "50", PropertyChangeType.DELETED);
//		when(configChangeEvent.getChange(anyString())).thenReturn(configChange);
//		pullVIPRdsOrdersConsumer.configChangeLisenner(configChangeEvent);
//		Assert.assertEquals(2000L, (long) pullVIPRdsOrdersConsumer.getRateLimiter());
//	}
//
//	@Test
//	public void consume5() {
//		ConsumeContext consumeContext = mock(ConsumeContext.class);
//		String body = "";
//		Message message = new Message("iyrstrade", "*", "PD_C1101", body.getBytes());
//		Action ret = pullVIPRdsOrdersConsumer.consume(message, consumeContext);
//		Assert.assertEquals(ret.name(), Action.CommitMessage.name());
//	}
//
//	@Test
//	public void consume6() throws Exception {
//		ConsumeContext consumeContext = mock(ConsumeContext.class);
//		String body = "xxxxxxx";
//		Message message = new Message("iyrstrade", "*", "PD_C1101", body.getBytes());
//		doNothing().when(pullVIPRdsOrdersConsumer).execute(any());
//		Action ret = pullVIPRdsOrdersConsumer.consume(message, consumeContext);
//		Assert.assertEquals(ret.name(), Action.CommitMessage.name());
//	}
//
//	/**
//	 * 处理退款订单推送（库里面数据存在）
//	 * @throws Exception
//	 */
//	@Test
//	public void consume7() throws Exception {
//		String content = JSON.toJSONString(generateRdsOrdersRefundRequestProto());
//		JdpTbRefund jdpTbRefund = generateJdpTbRefund();
//		UserDbId userDbId = new UserDbId(jdpTbRefund.getSellerNick(), 1, 1, sellerId, sellerId, "TAO", null);
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq(jdpTbRefund.getSellerNick()), any(), any())).thenReturn(userDbId);
//		when(jdpTbRefundService.checkIsNoExistJdpTbRefund(eq(jdpTbRefund.getTid().toString()), eq(jdpTbRefund.getOid().toString()), eq(jdpTbRefund.getModified())))
//				.thenReturn(jdpTbRefund);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(migrateRdsOrderService).putJdpRefundRecords(eq(sellerId), eq(jdpTbRefund), eq(CommonPlatformConstants.PLATFORM_TAO), eq(false));
//		verify(pullVIPRdsOrdersConsumer).rateLimiterAcquire();
//	}
//
//	/**
//	 * 处理退款订单推送（库里面数据不存在）
//	 * @throws Exception
//	 */
//	@Test
//	public void consume8() throws Exception {
//		String content = JSON.toJSONString(generateRdsOrdersRefundRequestProto());
//		JdpTbRefund jdpTbRefund = generateJdpTbRefund();
//
//		UserDbId userDbId = new UserDbId(jdpTbRefund.getSellerNick(), 1, 1, sellerId, sellerId, "TAO", null);
//		when(userProductionInfoExtService.getDbIdBySellerNick(eq(jdpTbRefund.getSellerNick()), any(), any())).thenReturn(userDbId);
//		when(jdpTbRefundService.checkIsNoExistJdpTbRefund(eq(jdpTbRefund.getTid().toString()), eq(jdpTbRefund.getOid().toString()), eq(jdpTbRefund.getModified())))
//				.thenReturn(null);
//		pullVIPRdsOrdersConsumer.execute(new AiyongMessageExt(content, false));
//		verify(migrateRdsOrderService, never()).putJdpRefundRecords(eq(sellerId), eq(jdpTbRefund), any(), anyBoolean());
//		verify(pullVIPRdsOrdersConsumer, never()).rateLimiterAcquire();
//	}
//
//	@Test
//	public void consumeMessage() throws Exception {
//		List<MessageExt> msgs = Lists.newArrayList();
//		MessageExt msg = new MessageExt();
//		msg.setBody("body".getBytes(StandardCharsets.UTF_8));
//		msg.setTopic("topic");
//		msg.setMsgId("msgId");
//		msgs.add(msg);
//		MessageExt msg2 = new MessageExt();
//		msg2.setBody("body2".getBytes(StandardCharsets.UTF_8));
//		msg2.setTopic("topic");
//		msg2.setMsgId("msgId2");
//		msgs.add(msg2);
//		ConsumeConcurrentlyContext context = mock(ConsumeConcurrentlyContext.class);
//
//		doNothing().when(pullVIPRdsOrdersConsumer).execute(any());
//		pullVIPRdsOrdersConsumer.consumeMessage(msgs, context);
//		verify(pullVIPRdsOrdersConsumer).execute(eq(new AiyongMessageExt()));
//		verify(pullVIPRdsOrdersConsumer).execute(eq(new AiyongMessageExt()));
//		verify(userProductionInfoExtService, times(2)).clearCacheDbId();
//	}
//
//	/**
//	 * ResendMessageException 异常重试
//	 *
//	 * @throws Exception
//	 */
//	@Test
//	public void consumeMessage2() throws Exception {
//		List<MessageExt> msgs = Lists.newArrayList();
//		MessageExt msg = new MessageExt();
//		msg.setBody("body".getBytes(StandardCharsets.UTF_8));
//		msg.setTopic("topic");
//		msg.setMsgId("msgId");
//		msg.setTags("*");
//		msgs.add(msg);
//		MessageExt msg2 = new MessageExt();
//		msg2.setBody("body2".getBytes(StandardCharsets.UTF_8));
//		msg2.setTopic("topic");
//		msg2.setMsgId("msgId2");
//		msg2.setTags("*");
//		msgs.add(msg2);
//
//		ConsumeConcurrentlyContext context = mock(ConsumeConcurrentlyContext.class);
//
//		doNothing().when(pullVIPRdsOrdersConsumer).execute(eq(new AiyongMessageExt()));
//		doThrow(ResendMessageException.class).when(pullVIPRdsOrdersConsumer).execute(eq(new AiyongMessageExt()));
//
//		pullVIPRdsOrdersConsumer.consumeMessage(msgs, context);
//		verify(pullVIPRdsOrdersConsumer).execute(new AiyongMessageExt("body", false));
//		verify(pullVIPRdsOrdersConsumer).execute(new AiyongMessageExt("body2", false));
//		verify(rocketMqQueueHelper).push(eq("topic"), eq("*"), eq("body2"), eq(defaultMQProducer), eq(0));
//		verify(userProductionInfoExtService, times(2)).clearCacheDbId();
//	}
//
//	private JdpTbTrade generateJdpTbTrade() {
//		JdpTbTrade jdpTbTrade = new JdpTbTrade();
//		jdpTbTrade.setTid(263519143275638593L);
//		jdpTbTrade.setStatus("TRADE_FINISHED");
//		jdpTbTrade.setType("fixed");
//		jdpTbTrade.setSellerNick("文火居");
//		jdpTbTrade.setBuyerNick("~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~");
//		jdpTbTrade.setCreated(DateUtil.parseString("2018-09-21 19:18:28"));
//		jdpTbTrade.setModified(DateUtil.parseString("2019-01-24 21:19:19"));
//		jdpTbTrade.setJdpHashcode("1847718010");
//		jdpTbTrade.setJdpResponse("{\"trade_fullinfo_get_response\":{\"trade\":{\"tid\":224473807924134741,\"tid_str\":\"224473807924134741\",\"status\":\"TRADE_FINISHED\",\"type\":\"fixed\",\"seller_nick\":\"小文思勇\",\"buyer_nick\":\"~CQi6tR5pzZh44oZpIEzunQ==~yTnkUpVax97QZSRTihp2u3MTUYrLLSUn~1~~\",\"created\":\"2018-09-21 19:18:28\",\"modified\":\"2018-11-11 00:33:58\",\"encrypt_alipay_id\":\"~NtkLP5vgaTSBLvk2cOx8BrEj69hZ8/ajUj0pLlXFo5s=~1~\",\"adjust_fee\":\"0.00\",\"alipay_id\":2088232330568785,\"alipay_no\":\"2018092122001168780589596425\",\"alipay_point\":0,\"available_confirm_fee\":\"0.00\",\"buyer_alipay_no\":\"~to3Mc5GUwfX18yqfIip8pg==~1~\",\"buyer_area\":\"陕西电信\",\"buyer_cod_fee\":\"0.00\",\"buyer_email\":\"\",\"buyer_ip\":\"MzYuNDYuMTAuMTM4\",\"buyer_message\":\"创意奖状:最佳吃货奖。   代写贺卡内容:永远的吃货！送你一个空投。(唉呀妈呀，真香！！！)\",\"buyer_obtain_point_fee\":0,\"buyer_rate\":true,\"cod_fee\":\"0.00\",\"cod_status\":\"NEW_CREATED\",\"coupon_fee\":0,\"commission_fee\":\"0.00\",\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"end_time\":\"2018-10-02 08:29:54\",\"has_post_fee\":true,\"has_yfx\":false,\"is_3D\":false,\"is_brand_sale\":false,\"is_daixiao\":false,\"is_force_wlb\":false,\"is_sh_ship\":false,\"is_lgtype\":false,\"is_part_consign\":false,\"is_wt\":false,\"is_gift\":false,\"num\":1,\"num_iid\":************,\"new_presell\":false,\"nr_shop_guide_id\":\"\",\"nr_shop_guide_name\":\"\",\"orders\":{\"order\":[{\"adjust_fee\":\"0.00\",\"buyer_rate\":true,\"cid\":50010535,\"consign_time\":\"2018-09-22 08:29:49\",\"discount_fee\":\"0.00\",\"divide_order_fee\":\"29.80\",\"end_time\":\"2018-10-02 08:29:54\",\"invoice_no\":\"3800540523278\",\"is_daixiao\":false,\"is_oversold\":false,\"logistics_company\":\"韵达快递\",\"num\":1,\"num_iid\":************,\"oid\":224473807924134741,\"oid_str\":\"224473807924134741\",\"order_from\":\"WAP,WAP\",\"payment\":\"29.80\",\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"price\":\"29.80\",\"refund_status\":\"NO_REFUND\",\"seller_rate\":true,\"seller_type\":\"C\",\"shipping_type\":\"express\",\"sku_id\":\"3861953163887\",\"sku_properties_name\":\"口味:空投箱礼盒\",\"snapshot_url\":\"n:224473807924134741_1\",\"status\":\"TRADE_FINISHED\",\"title\":\"抖音空投网红零食大礼包一箱整箱超大吃的休闲食品成人款组合混装\",\"total_fee\":\"29.80\"}]},\"pay_time\":\"2018-09-21 19:19:29\",\"payment\":\"29.80\",\"pcc_af\":0,\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i4/*********/TB21vYJXi6guuRjy0FmXXa0DXXa_!!*********.jpg\",\"platform_subsidy_fee\":\"0.00\",\"point_fee\":0,\"post_fee\":\"0.00\",\"price\":\"29.80\",\"real_point_fee\":0,\"received_payment\":\"29.80\",\"receiver_address\":\"建民街道安康大道安康学院江北新校区\",\"receiver_city\":\"安康市\",\"receiver_country\":\"\",\"receiver_district\":\"汉滨区\",\"receiver_mobile\":\"$75e7GGTpTbt8UUHJRxEyBg==$c2KVCN1iPe84tRP5NpQExA==$1$$\",\"receiver_name\":\"~NXErP2LLwlgbByngA8fDhw==~+W+3+CLX~1~~\",\"receiver_state\":\"陕西省\",\"receiver_town\":\"建民街道\",\"receiver_zip\":\"000000\",\"seller_alipay_no\":\"<EMAIL>\",\"seller_can_rate\":false,\"seller_cod_fee\":\"0.00\",\"seller_email\":\"<EMAIL>\",\"seller_flag\":0,\"seller_mobile\":\"18107002287\",\"seller_name\":\"刘小文\",\"seller_rate\":true,\"service_tags\":{\"logistics_tag\":[{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"origAreaId=610902;consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"},{\"service_tag\":\"lgType=-4\",\"service_type\":\"FAST\"}]},\"order_id\":\"224473807924134741\"},{\"logistic_service_tag_list\":{\"logistic_service_tag\":[{\"service_tag\":\"consignDate=48\",\"service_type\":\"TB_CONSIGN_DATE\"}]},\"order_id\":\"224473807924134741\"}]},\"service_type\":\"\",\"shipping_type\":\"express\",\"sid\":\"224473807924134741\",\"snapshot_url\":\"n:224473807924134741_1\",\"title\":\"小文思勇零食大礼包\",\"total_fee\":\"29.80\",\"trade_from\":\"WAP,WAP\",\"you_xiang\":false}}}");
//		jdpTbTrade.setJdpCreated(DateUtil.parseString("2018-11-11 04:59:09"));
//		jdpTbTrade.setJdpModified(DateUtil.parseString("2018-11-11 04:59:09"));
//		return jdpTbTrade;
//	}
//
//	private PullRdsOrdersRequestProto generateRdsOrdersRequestProto() {
//		PullRdsOrdersRequestProto pullRdsOrdersRequestProto = new PullRdsOrdersRequestProto();
//		pullRdsOrdersRequestProto.setSellerNick("文火居");
//		pullRdsOrdersRequestProto.setTid("263519143275638593");
//		pullRdsOrdersRequestProto.setModified(DateUtil.parseString("2019-01-24 21:19:19"));
//		return pullRdsOrdersRequestProto;
//	}
//
//	private JdpTbRefund generateJdpTbRefund() {
//		JdpTbRefund jdpTbRefund = new JdpTbRefund();
//		jdpTbRefund.setTid(1159772897615871945L);
//		jdpTbRefund.setOid(1159772897615871945L);
//		jdpTbRefund.setStatus("WAIT_SELLER_CONFIRM_GOODS");
//		jdpTbRefund.setSellerNick("赵东昊的测试店铺");
//		jdpTbRefund.setBuyerNick("~1pAM/D01zt/Re8/tsae9FxoesWyoYdFVHUd/tVCe69I=~wFgGxufqSVN9SP4ABosml/zN~1~~");
//		jdpTbRefund.setCreated(DateUtil.parseString("2020-08-03 20:18:06"));
//		jdpTbRefund.setModified(DateUtil.parseString("2020-08-03 20:19:25"));
//		jdpTbRefund.setJdpHashcode("-1019630503");
//		jdpTbRefund.setJdpResponse("{\"refund_get_response\":{\"refund\":{\"refund_id\":\"75360641881874519\",\"status\":\"WAIT_SELLER_CONFIRM_GOODS\",\"seller_nick\":\"赵东昊的测试店铺\",\"buyer_nick\":\"~1pAM/D01zt/Re8/tsae9FxoesWyoYdFVHUd/tVCe69I=~wFgGxufqSVN9SP4ABosml/zN~1~~\",\"tid\":1159772897615871945,\"oid\":1159772897615871945,\"created\":\"2020-08-03 20:18:06\",\"modified\":\"2020-08-03 20:19:25\",\"address\":\"赵东昊， 18344343678， 浙江省宁波市宁海县   测试一号， 000000\",\"advance_status\":0,\"alipay_no\":\"2020080322001145881431514678\",\"attribute\":\";enfunddetail:1;reason:401457;gaia:2;ee_trace_id:0b133b4615964571655884606e3fff;bizCode:taobao.general.refund;lastOrder:0;tod:*********;newRefund:rp2;leavesCat:50019776;logisticsOrderCode:ZJS000096554695;intentReturnGoodsType:RETURN_BY_SELF;opRole:buyer;prepaidFailure:TAOBAO_CREDIT_SCORE_NOT_MEET;apply_reason_text:拍错/多拍/效果不好/不喜欢;apply_init_refund_fee:1;itemBuyAmount:1;apply_text_id:401457;userCredit:0;sdkCode:ali.china.taobao;interceptStatus:0;seller_batch:true;logisticsCompanyName:宅急送;restartForXiaoer:1;rootCat:50025004;tos:1;ol_tf:1;ability:1;sku:*************|尺寸#3B40X40cm#3A颜色分类#3B天蓝色#3A材质#3B泰麂绒;sgr:1;bgmtc:2020-08-03 19#3B55#3B16;appName:refundplatform2;payMode:alipay;sellerDoRefundNick:赵东昊的测试店铺;workflowName:return_and_refund;rightsSuspend:1;shop_name:爱用交易测试店铺;ttid:h5;abnormal_dispute_status:0;seller_audit:0;rp3:1;seller_agreed_refund_fee:1;stopAgree:0;itemPrice:1;isVirtual:0;EXmrf:1;refundFrom:2;\",\"company_name\":\"宅急送\",\"cs_status\":1,\"desc\":\"\",\"good_return_time\":\"2020-08-03 20:19:26\",\"good_status\":\"BUYER_RETURNED_GOODS\",\"has_good_return\":true,\"num\":1,\"num_iid\":618780453838,\"operation_contraint\":\"null\",\"order_status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"outer_id\":\"还是睡觉手机\",\"payment\":\"0.00\",\"price\":\"0.01\",\"reason\":\"拍错/多拍/效果不好/不喜欢\",\"refund_fee\":\"0.01\",\"refund_phase\":\"onsale\",\"refund_remind_timeout\":{\"exist_timeout\":true,\"remind_type\":8,\"timeout\":\"2020-08-13 20:19:26\"},\"refund_version\":1596457086786,\"shipping_type\":\"express\",\"sid\":\"ZJS000094342382\",\"sku\":\"*************|尺寸:40X40cm;颜色分类:天蓝色;材质:泰麂绒\",\"title\":\"自动化测试专用商品1\",\"total_fee\":\"0.01\"}}}");
//		jdpTbRefund.setJdpCreated(DateUtil.parseString("2020-08-03 20:18:07"));
//		jdpTbRefund.setJdpModified(DateUtil.parseString("2020-08-03 20:19:26"));
//		return jdpTbRefund;
//	}
//
//	private PullRdsOrdersRefundRequestProto generateRdsOrdersRefundRequestProto() {
//		PullRdsOrdersRefundRequestProto proto = new PullRdsOrdersRefundRequestProto();
//		proto.setSellerNick("赵东昊的测试店铺");
//		proto.setTid("1159772897615871945");
//		proto.setOid("1159772897615871945");
//		proto.setModified(DateUtil.parseString("2020-08-03 20:19:25"));
//		return proto;
//	}
//
//}
