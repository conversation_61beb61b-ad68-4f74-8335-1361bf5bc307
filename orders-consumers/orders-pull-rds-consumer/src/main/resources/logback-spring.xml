<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<property name="LOG_INFO_FILE" value="logs/info.log"/>
	<property name="LOG_INFO_FILE_BACK" value="logs/info.%i.log"/>
	<property name="LOG_PATTERN" value="[%d{HH:mm:ss}] [%level] [%logger] %msg%n"/>
	<property name="MAX_FILE_SIZE" value="128MB"/>

	<springProperty scope="context" name="app.name" source="spring.application.name" defaultValue=""/>

	<!-- 控制台 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<!-- 全日志文件 -->
	<appender name="rollingFileInfo" class="ch.qos.logback.core.rolling.RollingFileAppender" >
		<file>${LOG_INFO_FILE}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>${LOG_INFO_FILE_BACK}</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>1</maxIndex>
		</rollingPolicy>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
		</triggeringPolicy>
		<encoder class="net.logstash.logback.encoder.LogstashEncoder">
			<shortenedLoggerNameLength>36</shortenedLoggerNameLength>
		</encoder>
	</appender>

	<!-- 异步输出全日志文件 -->
	<appender name ="asyncRollingFileInfo" class= "ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold >0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>1024</queueSize>
		<!-- 超过队列长度，扔掉信息，不阻塞应用线程-->
		<neverBlock>false</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref ="rollingFileInfo"/>
	</appender>

	<!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
	<!--<shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook" />-->

	<!-- 开发环境 -->
	<springProfile name="dev,local">
		<!-- logger 配置 -->
		<logger name="org.elasticsearch" level="debug" additivity="false" >
			<appender-ref ref="STDOUT"/>
		</logger>
		<logger name="cn.loveapp.orders.common" level="info" additivity="false" >
			<appender-ref ref="STDOUT"/>
		</logger>
		<logger name="cn.loveapp.orders.consumer.common.dao" level="info" additivity="false" >
			<appender-ref ref="STDOUT"/>
		</logger>
		<logger name="cn.loveapp.orders.pull.rds" level="info" additivity="false" >
			<appender-ref ref="STDOUT"/>
		</logger>
		<logger name="cn.loveapp" level="info" additivity="false" >
			<appender-ref ref="STDOUT"/>
		</logger>
        <logger name="RocketmqClient" level="error" additivity="false" >
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="RocketmqRemoting" level="error" additivity="false" >
            <appender-ref ref="STDOUT"/>
        </logger>
		<root level="info">
			<appender-ref ref="STDOUT"/>
            <appender-ref ref="asyncRollingFileInfo"/>
		</root>
	</springProfile>

	<!-- 生产环境 -->
	<springProfile name="prod">
		<!-- 阿里sls日志服务配置 -->
		<!--		<springProperty scope="context" name="endpoint" source="loveapp.logging.sls.endpoint" defaultValue=""/>-->
		<!--		<springProperty scope="context" name="accessKeyId" source="loveapp.logging.sls.accessKeyId" defaultValue=""/>-->
		<!--		<springProperty scope="context" name="accessKey" source="loveapp.logging.sls.accessKey" defaultValue=""/>-->
		<!--		<springProperty scope="context" name="projectName" source="loveapp.logging.sls.projectName" defaultValue=""/>-->
		<!--		<springProperty scope="context" name="logstore" source="loveapp.logging.sls.logstore" defaultValue=""/>-->

		<!--		<appender name="aliyun" class="cn.loveapp.common.sls.LoghubAppender">-->
		<!--			&lt;!&ndash;必选项&ndash;&gt;-->
		<!--			&lt;!&ndash; 账号及网络配置 &ndash;&gt;-->
		<!--			<endpoint>${endpoint}</endpoint>-->
		<!--			<accessKeyId>${accessKeyId}</accessKeyId>-->
		<!--			<accessKey>${accessKey}</accessKey>-->

		<!--			&lt;!&ndash; sls 项目配置 &ndash;&gt;-->
		<!--			<projectName>${projectName}</projectName>-->
		<!--			<logstore>${logstore}</logstore>-->
		<!--			&lt;!&ndash;必选项 (end)&ndash;&gt;-->

		<!--			&lt;!&ndash; 可选项 &ndash;&gt;-->
		<!--			&lt;!&ndash; 指定日志主题，默认为 "" &ndash;&gt;-->
		<!--			<topic>${topic}</topic>-->
		<!--			&lt;!&ndash;被缓存起来的日志的发送超时时间，如果缓存超时，则会被立即发送，单位是毫秒，默认值为3000，最小值为10，可选参数&ndash;&gt;-->
		<!--			<packageTimeoutInMS>3000</packageTimeoutInMS>-->
		<!--			&lt;!&ndash;每个缓存的日志包中包含日志数量的最大值，不能超过 4096，可选参数&ndash;&gt;-->
		<!--			<logsCountPerPackage>4096</logsCountPerPackage>-->
		<!--			&lt;!&ndash;每个缓存的日志包的大小的上限，不能超过 3MB，单位是字节，可选参数&ndash;&gt;-->
		<!--			<logsBytesPerPackage>3145728</logsBytesPerPackage>-->
		<!--			&lt;!&ndash;Appender 实例可以使用的内存的上限，单位是字节，默认是 100MB，可选参数&ndash;&gt;-->
		<!--			<memPoolSizeInByte>104857600</memPoolSizeInByte>-->
		<!--			&lt;!&ndash;指定I/O线程池最大线程数量，主要用于发送数据到日志服务，默认是8，可选参数&ndash;&gt;-->
		<!--			<maxIOThreadSizeInPool>1</maxIOThreadSizeInPool>-->
		<!--			&lt;!&ndash;指定发送失败时重试的次数，如果超过该值，会把失败信息记录到logback的StatusManager里，默认是3，可选参数&ndash;&gt;-->
		<!--			<retryTimes>1</retryTimes>-->

		<!--			&lt;!&ndash; 可选项 通过配置 encoder 的 pattern 自定义 message 的格式 &ndash;&gt;-->
		<!--			&lt;!&ndash;<encoder>&ndash;&gt;-->
		<!--			&lt;!&ndash;<pattern>${LOG_PATTERN}</pattern>&ndash;&gt;-->
		<!--			&lt;!&ndash;</encoder>&ndash;&gt;-->

		<!--			&lt;!&ndash; 可选项 设置 time 字段呈现的格式 &ndash;&gt;-->
		<!--			<timeFormat>yyyy-MM-dd HH:mm:ss.SSS</timeFormat>-->
		<!--			&lt;!&ndash; 可选项 设置 time 字段呈现的时区 &ndash;&gt;-->
		<!--			<timeZone>Asia/Shanghai</timeZone>-->

		<!--			<filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
		<!--				<level>DEBUG</level>-->
		<!--			</filter>-->
		<!--		</appender>-->

		<!--		&lt;!&ndash; 异步输出aliyun &ndash;&gt;-->
		<!--		<appender name ="asyncAliyun" class= "ch.qos.logback.classic.AsyncAppender">-->
		<!--			&lt;!&ndash; 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 &ndash;&gt;-->
		<!--			<discardingThreshold >10</discardingThreshold>-->
		<!--			&lt;!&ndash; 更改默认的队列的深度,该值会影响性能.默认值为256 &ndash;&gt;-->
		<!--			<queueSize>20000</queueSize>-->
		<!--			&lt;!&ndash; 超过队列长度，扔掉信息，不阻塞应用线程&ndash;&gt;-->
		<!--			<neverBlock>true</neverBlock>-->
		<!--			&lt;!&ndash; 添加附加的appender,最多只能添加一个 &ndash;&gt;-->
		<!--			<appender-ref ref ="aliyun"/>-->
		<!--		</appender>-->

		<!-- logger 配置 -->
        <logger name="com.doudian.open" level="warn" additivity="false" >
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="org.mongodb.driver.connection" level="warn" additivity="false" >
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
		<logger name="cn.loveapp.orders.consumer.common" level="info" additivity="false" >
			<!--<appender-ref ref="STDOUT"/>-->
			<appender-ref ref="asyncRollingFileInfo"/>
		</logger>
		<logger name="cn.loveapp.orders.pull.rds" level="info" additivity="false" >
			<!--			<appender-ref ref="STDOUT"/>-->
			<appender-ref ref="asyncRollingFileInfo"/>
		</logger>
		<logger name="cn.loveapp.common.utils" level="info" additivity="false" >
			<!--			<appender-ref ref="STDOUT"/>-->
			<appender-ref ref="asyncRollingFileInfo"/>
		</logger>
		<logger name="cn.loveapp.orders.common" level="info" additivity="false" >
			<!--			<appender-ref ref="STDOUT"/>-->
			<appender-ref ref="asyncRollingFileInfo"/>
		</logger>
		<logger name="cn.loveapp" level="info" additivity="false" >
			<appender-ref ref="asyncRollingFileInfo"/>
		</logger>
		<root level="info">
			<appender-ref ref="STDOUT"/>
			<appender-ref ref="asyncRollingFileInfo"/>
		</root>
	</springProfile>

</configuration>
