spring.application.name=orders-pull-rds-consumer
spring.profiles.active=dev

# \u662F\u5426\u5141\u8BB8apollo
loveapp.apollo.enabled=true
# apollo \u57FA\u7840\u914D\u7F6E
app.id=cn.loveapp.trade
apollo.bootstrap.enabled = ${loveapp.apollo.enabled}
# \u516C\u5171namespace\u5FC5\u987B\u653E\u540E\u9762
apollo.bootstrap.namespaces=tao-rdsconsumer,orders-push-1,orders-consumer,application,service-registry
env=${spring.profiles.active}

# dubbo
# \u662F\u5426\u5141\u8BB8dubbo\u6D88\u8D39\u8005
dubbo.enabled=false
dubbo.application.name=orders-consumer
dubbo.scan.base-packages=cn.loveapp.orders.consumer
dubbo.consumer.check=false


spring.cache.jcache.config=classpath:ehcache.xml

# \u7AEF\u53E3
server.port=8080

# \u6307\u6807\u76D1\u63A7
management.server.port=8455
management.metrics.tags.application=${spring.application.name}
management.endpoints.web.exposure.include=prometheus

management.metrics.export.logging.enabled = false
management.metrics.export.logging.step = 15s

management.health.defaults.enabled=false
management.endpoint.health.enabled=false

spring.elasticsearch.rest.uris=
spring.elasticsearch.rest.read-timeout=30s
spring.elasticsearch.rest.connection-timeout=10s
spring.elasticsearch.rest.username=
spring.elasticsearch.rest.password=
