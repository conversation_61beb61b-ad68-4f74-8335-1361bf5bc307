package cn.loveapp.orders.pull.rds.config;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoAppConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoRdsVipAppConfig;
import cn.loveapp.orders.common.config.rocketmq.aliyun.TaobaoAliyunRocketMQConfig;
import cn.loveapp.orders.common.service.JdpTbRefundService;
import cn.loveapp.orders.common.service.JdpTbTradeService;
import cn.loveapp.orders.common.service.impl.JdpTbRefundServiceImpl;
import cn.loveapp.orders.common.service.impl.JdpTbTradeServiceImpl;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.pull.rds.consumer.OrdersPullRdsConsumer;
import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragelyByCircle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

import java.util.Properties;

/**
 * ONSConfig
 *
 * <AUTHOR>
 * @date 2018/9/21
 */
@Configuration
public class OrdersPullRdsConsumerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(
		OrdersPullRdsConsumerConfig.class);

	@Autowired
	private RocketMQTaobaoAppConfig rocketMQTaobaoAppConfig;

	@Autowired
	private TaobaoAliyunRocketMQConfig taobaoAliyunRocketMQConfig;

	@Autowired
	private RocketMQTaobaoRdsVipAppConfig rocketMQTaobaoRdsVIPAppConfig;

	private DefaultMQPushConsumer consumer = null;

	private Consumer consumerAliyun = null;

    @Bean(destroyMethod = "", name = "ordersPullVIPRdsDatasOnsConsumer")
    public DefaultMQPushConsumer ordersPullVIPRdsDatasOnsConsumer(){
        //启动ONS消息队列
        try {
			consumer = new DefaultMQPushConsumer(rocketMQTaobaoRdsVIPAppConfig.getConsumerId());
			consumer.setNamesrvAddr(rocketMQTaobaoAppConfig.getNamesrvAddr());
			consumer.setConsumeThreadMax(rocketMQTaobaoRdsVIPAppConfig.getMaxThreadNum());
			consumer.setConsumeThreadMin(rocketMQTaobaoRdsVIPAppConfig.getMaxThreadNum());
			consumer.setAllocateMessageQueueStrategy(new AllocateMessageQueueAveragelyByCircle());
        } catch (Exception e) {
			LOGGER.logError("create order transfer ONS Consumer failed", e);
        }
        return consumer;
    }

	@ConditionalOnProperty(prefix = "orders.taobao.aliyun.rocketmq", name = "consumerEnable", havingValue = "true")
	@Bean(destroyMethod = "", name = "ordersPullVIPRdsDatasOnsConsumerAliyun")
	public Consumer ordersPullVIPRdsDatasOnsConsumerAliyun() {
		try {
			Properties properties = new Properties();
			properties.setProperty(PropertyKeyConst.AccessKey, taobaoAliyunRocketMQConfig.getAccessKey());
			properties.setProperty(PropertyKeyConst.SecretKey, taobaoAliyunRocketMQConfig.getSecretKey());
			properties.setProperty(PropertyKeyConst.GROUP_ID, rocketMQTaobaoRdsVIPAppConfig.getConsumerId());
			properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, taobaoAliyunRocketMQConfig.getNamesrvAddr());
			properties.setProperty(PropertyKeyConst.ConsumeThreadNums, String.valueOf(rocketMQTaobaoRdsVIPAppConfig.getMaxThreadNum()));
			properties.setProperty(PropertyKeyConst.OnsChannel, taobaoAliyunRocketMQConfig.getChannel());
			consumerAliyun = ONSFactory.createConsumer(properties);
		} catch (Exception e) {
			LOGGER.logError("create order transfer ONS Consumer failed", e);
		}
		return consumerAliyun;
	}

	@Bean
	public JdpTbTradeService jdpTbTradeServiceImpl(){
		return new JdpTbTradeServiceImpl();
	}

	@Bean
	public JdpTbRefundService jdpTbRefundServiceImpl() {
		return new JdpTbRefundServiceImpl();
	}

	@Bean(name = "ordersPullVIPRdsDatasOnsConsumerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager(){
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2018/11/9
	 */
    public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>, Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired(required = false)
		@Qualifier("ordersPullVIPRdsDatasOnsConsumer")
		private DefaultMQPushConsumer ordersPullVIPRdsDatasOnsConsumer;

		@Autowired(required = false)
		@Qualifier("ordersPullVIPRdsDatasOnsConsumerAliyun")
		private Consumer ordersPullVIPRdsDatasOnsConsumerAliyun;

		@Autowired(required = false)
		private OrdersPullRdsConsumer pullVIPRdsOrdersConsumer;

		@Autowired
		private RocketMQTaobaoRdsVipAppConfig rocketMQTaobaoRdsVIPAppConfig;

		@Autowired
		private RocketMqQueueHelper rocketMqQueueHelper;

		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS消费者
			if (ordersPullVIPRdsDatasOnsConsumer != null) {
				ordersPullVIPRdsDatasOnsConsumer
					.subscribe(rocketMQTaobaoRdsVIPAppConfig.getTopic(), rocketMQTaobaoRdsVIPAppConfig.getTag());
				ordersPullVIPRdsDatasOnsConsumer.registerMessageListener(pullVIPRdsOrdersConsumer);
				LOGGER.logInfo("order transfer Consumer is startting");
				ordersPullVIPRdsDatasOnsConsumer.start();
				LOGGER.logInfo("order transfer Consumer is started");
			}

			//启动阿里云订单ONS消费者 已废弃
            /*
			if (ordersPullVIPRdsDatasOnsConsumerAliyun != null) {
				ordersPullVIPRdsDatasOnsConsumerAliyun
						.subscribe(rocketMQTaobaoRdsVIPAppConfig.getTopic(), rocketMQTaobaoRdsVIPAppConfig.getTag(), pullVIPRdsOrdersConsumer);
				LOGGER.logInfo("order transfer Consumer aliyun is startting");
				ordersPullVIPRdsDatasOnsConsumerAliyun.start();
				LOGGER.logInfo("order transfer Consumer aliyun is started");
			}
             */
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
				return;
			}
			if(ordersPullVIPRdsDatasOnsConsumer != null){
				LOGGER.logInfo("正在关闭订单OnsConsumer...");
				try {
					pullVIPRdsOrdersConsumer.stop();
                    rocketMqQueueHelper.stopOnsConsumer(ordersPullVIPRdsDatasOnsConsumer);
                    // 阿里云mq 已废弃，不再使用
//					rocketMqQueueHelper.stopOnsConsumer(ordersPullVIPRdsDatasOnsConsumerAliyun);
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("订单OnsConsumer已关闭");
			}
		}

		@Override
		public int getOrder() {
			return Ordered.HIGHEST_PRECEDENCE;
		}
	}

}
