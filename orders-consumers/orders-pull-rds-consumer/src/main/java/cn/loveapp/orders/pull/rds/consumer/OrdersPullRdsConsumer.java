package cn.loveapp.orders.pull.rds.consumer;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.serialization.protobuf.ProtostuffSerializer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyRefund;
import cn.loveapp.orders.common.bo.TradeAfterSaveBo;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.maintain.MaintainConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoRefundGetConfig;
import cn.loveapp.orders.common.constant.OnsRateLimitConstant;
import cn.loveapp.orders.common.constant.OrderPullRdsTypeConstant;
import cn.loveapp.orders.common.consumer.BaseDbIdOnsConsumer;
import cn.loveapp.orders.common.dao.rds.JdpExchangeRefundDao;
import cn.loveapp.orders.common.entity.JdpExchangeRefund;
import cn.loveapp.orders.common.entity.JdpTbRefund;
import cn.loveapp.orders.common.entity.JdpTbTrade;
import cn.loveapp.orders.common.exception.ResendMessageException;
import cn.loveapp.orders.common.proto.PullRdsBaseRequestProto;
import cn.loveapp.orders.common.proto.PullRdsExchangeRefundRequestProto;
import cn.loveapp.orders.common.proto.PullRdsOrdersRefundRequestProto;
import cn.loveapp.orders.common.proto.PullRdsOrdersRequestProto;
import cn.loveapp.orders.common.proto.PullRefundGetRequestProto;
import cn.loveapp.orders.common.service.JdpTbRefundService;
import cn.loveapp.orders.common.service.JdpTbTradeService;
import cn.loveapp.orders.common.service.TradeSendOrderHandleService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.consumer.common.bo.TradeBo;
import cn.loveapp.orders.consumer.common.service.MigrateRdsOrderService;
import cn.loveapp.orders.consumer.common.service.TradeRefundHandleService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: orders-services-group
 * @description: VIP存单消费者
 * @author: Jason
 * @create: 2018-11-19 13:46
 **/
@Component
public class OrdersPullRdsConsumer extends BaseDbIdOnsConsumer {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersPullRdsConsumer.class);

	@Value("${orders.consumer.rds.ignoreUserNicks:}")
	protected List<String> ignoreUserNicks = Lists.newArrayList();

	@Autowired
	private MigrateRdsOrderService migrateRdsOrderService;

	@Autowired
	private JdpTbTradeService jdpTbTradeService;

	@Autowired
	private JdpTbRefundService jdpTbRefundService;

	@Autowired
	private JdpExchangeRefundDao jdpExchangeRefundDao;

	@Autowired
	private UserProductionInfoExtService userProductionInfoExtService;

    @Autowired
    private TradeRefundHandleService tradeRefundHandleService;

    @Autowired
    private TaobaoRefundGetConfig taobaoRefundGetConfig;

    @Autowired
    private TradeSendOrderHandleService tradeSendOrderHandleService;

    /**
     * 订单数据留存校验开关
     */
    @Value("${orders.rds.consumer.retention.check.enabled:true}")
    private boolean retentionCheckEnabled;

    @Autowired
    private MaintainConfig maintainConfig;


	public OrdersPullRdsConsumer(MeterRegistry registry, Environment environment){
        super(registry, "拉取rds订单ONS接收.QPS", OnsRateLimitConstant.ROCKETMQ_ORDERS_RATELIMIT_PULL_RDS,
            environment, true, false);
    }

    /**
     * 业务处理：真正处理的数据类
     *
     * @param message
     * @param messageDeserializationResult
     * @throws Exception
     */
    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws Exception {
		boolean forceHandleFlag = getForceHandleFlag(message.getProperties());
        Object content = messageDeserializationResult.getContent();
        PullRdsBaseRequestProto pullRdsBaseRequestProto = (PullRdsBaseRequestProto) content;
		String tid = pullRdsBaseRequestProto.getTid();
		String sellerNick = pullRdsBaseRequestProto.getSellerNick();
        String appName =
            OrderUtil.defaultAppName(CommonPlatformConstants.PLATFORM_TAO, pullRdsBaseRequestProto.getAppName());
        MDC.put("tid", tid);
		MDC.put("sellerNick", sellerNick);
		MDC.put("rds_type", message.getUserProperty(OrderPullRdsTypeConstant.RDS_TYPE));

		if(ignoreUserNicks.contains(sellerNick)){
			LOGGER.logInfo(sellerNick, tid, "忽略的用户, 退出");
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		}

        UserDbId userDbId =
            userProductionInfoExtService.getDbIdBySellerNick(sellerNick, CommonPlatformConstants.PLATFORM_TAO,
                OrderUtil.defaultAppName(CommonPlatformConstants.PLATFORM_TAO, appName));
		if(userDbId == null || StringUtils.isEmpty(userDbId.getSellerId())){
			LOGGER.logInfo(sellerNick, "", "未开户用户, 退出");
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		}
		String sellerId = userDbId.getSellerId();
		String corpId = userDbId.getCorpId();
		try {
            if (content instanceof PullRdsOrdersRefundRequestProto) {
                // 处理退款rds推送
                PullRdsOrdersRefundRequestProto pullRdsOrdersRefundRequestProto = (PullRdsOrdersRefundRequestProto) messageDeserializationResult.getContent();
                AyRefund ayRefund = handlePullRdsRefund(sellerId, pullRdsOrdersRefundRequestProto, forceHandleFlag, appName);
                // 淘宝换货推送至refundget通过天猫换货接口获取换货信息
                if (ayRefund != null && taobaoRefundGetConfig.getThenTmallExchangeGetDisputeTypes()
                    .contains(ayRefund.getDisputeType())) {

                    LOGGER.logInfo(sellerNick, ayRefund.getRefundId(), "rds淘宝换货推送至refundget获取换货信息");
                    sendTaoExchange2Refund(tid, ayRefund.getRefundId(), sellerNick, sellerId, LocalDateTime.now(),
                        CommonPlatformConstants.PLATFORM_TAO, appName);
                }
            } else if (content instanceof PullRdsExchangeRefundRequestProto) {
				// 处理换货rds推送
				handlePullRdsExchange(sellerId, corpId, messageDeserializationResult, forceHandleFlag);
            } else {
                // 处理订单rds推送
                PullRdsOrdersRequestProto pullRdsOrdersRequestProto = (PullRdsOrdersRequestProto)messageDeserializationResult.getContent();
                TradeBo tradeBo = handlePullRdsTrade(sellerId, pullRdsOrdersRequestProto, appName);
                if (tradeBo != null) {
                    // 发送refundGet消息
                    TradeAfterSaveBo tradeAfterSaveBo = new TradeAfterSaveBo(tradeBo.getTrade(),
                        tradeBo.getLastAyTradeMain(), tradeBo.getLastTcSubOrder(), tradeBo.getTcOrder(),
                        tradeBo.getTcSubOrder(), tradeBo.getIsModifyFlagOrMemo());
                    tradeRefundHandleService.fullinfoMesageForwardRefund(tradeAfterSaveBo, LocalDateTime.now(),
                        sellerNick, sellerId, CommonPlatformConstants.PLATFORM_TAO, appName);
                }
            }
		} catch (ResendMessageException e) {
			throw e;
		} catch (Exception e) {
			if(e instanceof DeadlockLoserDataAccessException){
				LOGGER.logError(sellerNick, tid, "并发更新, 退回重试=>" + e.getMessage(), e);
			}else{
				LOGGER.logError(sellerNick, tid, "程序出错=>" + e.getMessage(), e);
			}
			throw e;
		}
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}

    @Override
    protected  MessageDeserializationResult deserializeMessage(MessageExt message) {
        String rdsType = message.getUserProperty(OrderPullRdsTypeConstant.RDS_TYPE);
        if (StringUtils.isEmpty(rdsType)) {
            PullRdsBaseRequestProto pullRdsBaseRequestProto = null;
            try {
                String content = new String(message.getBody(), StandardCharsets.UTF_8);
                pullRdsBaseRequestProto = JSON.parseObject(content, PullRdsBaseRequestProto.class);
            } catch (Exception e) {
                LOGGER.logError("-", message.getMsgId(), "尝试protostuff序列化：message properties RDS_TYPE 字段缺失,json序列化异常" + e.getMessage());
                pullRdsBaseRequestProto = ProtostuffSerializer.deserialize(message.getBody(), PullRdsBaseRequestProto.class);
            }
            message.putUserProperty(OrderPullRdsTypeConstant.RDS_TYPE, pullRdsBaseRequestProto.getRdsType());
        }
        return super.deserializeMessage(message);
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt message) {
        String rdsType = message.getUserProperty(OrderPullRdsTypeConstant.RDS_TYPE);
        if (PullRdsOrdersRefundRequestProto.RDS_TYPE.equals(rdsType)) {
            return PullRdsOrdersRefundRequestProto.class;
        }
        else if (PullRdsExchangeRefundRequestProto.RDS_TYPE.equals(rdsType)) {
            return PullRdsExchangeRefundRequestProto.class;
        } else {
            return PullRdsOrdersRequestProto.class;
        }
    }

    /**
     * 淘宝换货推送至refund获取换货信息。
     * 淘宝换货缺少换货物流信息，只能通过天猫换货api获取，所以需要推到refund中获取换货信息。
     */
    private void sendTaoExchange2Refund(String tid, String refundId, String sellerNick, String sellerId,
        LocalDateTime modified, String platform, String appName) {

	    PullRefundGetRequestProto refundGetRequestProto = new PullRefundGetRequestProto();
        refundGetRequestProto.setSellerNick(sellerNick);
        refundGetRequestProto.setTid(tid);
        refundGetRequestProto.setPlatformId(platform);
        refundGetRequestProto.setRefundId(refundId);
        refundGetRequestProto.setModified(modified);
        refundGetRequestProto.setSellerId(sellerId);
        refundGetRequestProto.setIsOnlyGetExchangeMessage(Boolean.TRUE);
        refundGetRequestProto.setAppName(appName);
        tradeSendOrderHandleService.pushRefundGetMessage(refundGetRequestProto, 0);
    }

	/**
	 * 处理订单rds推送
	 * @param pullRdsOrdersRequestProto
	 */
    private TradeBo handlePullRdsTrade(String sellerId, PullRdsOrdersRequestProto pullRdsOrdersRequestProto,
        String appName) throws Exception {
        String tid = pullRdsOrdersRequestProto.getTid();
        String sellerNick = pullRdsOrdersRequestProto.getSellerNick();
        LocalDateTime modified = pullRdsOrdersRequestProto.getModified();
        String platformId = CommonPlatformConstants.PLATFORM_TAO;

		JdpTbTrade jdpTbTrade = jdpTbTradeService.checkIsNoExistJdpTbTrade(tid, modified);
		if (null == jdpTbTrade) {
			LOGGER.logWarn(sellerNick, tid, modified + " jdpTbTrade记录不存在");
			return null;
		}
		LOGGER.logInfo(sellerNick, tid, "jdpTbTrade 原始数据为 => " + JSON.toJSONString(jdpTbTrade));

        //判断订单创建时间是否远于mongo、es最大存库时间
        if (checkIsExpire2SaveDB(jdpTbTrade.getCreated(), sellerNick, tid)) {
            //将过期的订单从list中移除
            return null;
        }

        rateLimiterAcquire();
        return migrateRdsOrderService.putJdpTradeRecords(sellerId, jdpTbTrade, platformId,
            OrderUtil.defaultAppName(platformId, appName, CommonAppConstants.APP_TRADE), false);
    }

    /**
     * 检查是否是存库过期订单
     *
     * @param sellerNick
     * @param tid
     * @return
     */
   private boolean checkIsExpire2SaveDB(LocalDateTime dbCreated, String sellerNick, String tid) {
        // 判断是否开启数据过期检查
        if (!retentionCheckEnabled) {
            return false;
        }

        // 判断数据是否超过最长留存时间
        if (dbCreated != null && dbCreated.isBefore(LocalDateTime.now().minusDays(maintainConfig.getRetentionDays()))) {
            LOGGER.logInfo(sellerNick, tid, "入库丢弃：订单超过数据最长留存时间，当前订单创建时间：" + dbCreated);
            return true;
        }
        return false;
    }

	/**
	 * 处理退款rds推送
	 * @param pullRdsOrdersRefundRequestProto
	 */
    private AyRefund handlePullRdsRefund(String sellerId,
        PullRdsOrdersRefundRequestProto pullRdsOrdersRefundRequestProto, boolean forceHandleFlag, String appName)
        throws Exception {

        String tid = pullRdsOrdersRefundRequestProto.getTid();
        String oid = pullRdsOrdersRefundRequestProto.getOid();
        String sellerNick = pullRdsOrdersRefundRequestProto.getSellerNick();
        LocalDateTime modified = pullRdsOrdersRefundRequestProto.getModified();
        JdpTbRefund jdpTbRefund = jdpTbRefundService.checkIsNoExistJdpTbRefund(tid, oid, modified);
        if (null == jdpTbRefund) {
            LOGGER.logWarn(sellerNick, tid, modified + " jdpTbRefund记录不存在");
            return null;
        }

        LOGGER.logInfo(sellerNick, tid, "jdpTbRefund 原始数据为 => " + JSON.toJSONString(jdpTbRefund));
        rateLimiterAcquire();
        return migrateRdsOrderService.putJdpRefundRecords(sellerId, jdpTbRefund, CommonPlatformConstants.PLATFORM_TAO,
            appName, forceHandleFlag);
    }

	/**
	 * 处理换货rds推送
	 * @param messageDeserializationResult
	 */
    private void handlePullRdsExchange(String sellerId, String corpId, MessageDeserializationResult messageDeserializationResult,
                                       boolean forceHandleFlag) throws Exception {
        PullRdsExchangeRefundRequestProto pullRdsExchangeRefundRequestProto = (PullRdsExchangeRefundRequestProto) messageDeserializationResult.getContent();
        String disputeId = pullRdsExchangeRefundRequestProto.getDisputeId();
        LocalDateTime modified = pullRdsExchangeRefundRequestProto.getModified();
        String appName = OrderUtil.defaultAppName(CommonPlatformConstants.PLATFORM_TAO,
            pullRdsExchangeRefundRequestProto.getAppName());

        JdpExchangeRefund jdpExchangeRefund = jdpExchangeRefundDao.queryByJdpModifiedOne(disputeId, modified);
		if (null == jdpExchangeRefund) {
			LOGGER.logWarn("", disputeId, modified + "JdpExchangeRefund记录不存在");
			return;
		}
		LOGGER.logInfo("", disputeId, "JdpExchangeRefund 原始数据为 => " + JSON.toJSONString(jdpExchangeRefund));

		rateLimiterAcquire();
        migrateRdsOrderService.putJdpExchangeRefundRecords(sellerId, corpId, jdpExchangeRefund,
            CommonPlatformConstants.PLATFORM_TAO, appName, forceHandleFlag);
    }

}
