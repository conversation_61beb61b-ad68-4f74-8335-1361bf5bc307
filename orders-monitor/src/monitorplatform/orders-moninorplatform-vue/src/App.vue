<template>
  <div id="app">
    <AdminHome/>
    <router-view></router-view>

  </div>
</template>

<script>
import AdminHome from "./view/admin/AdminHome";

export default {
  name: 'App',
  components: {
    AdminHome,
  },

  data() {
    return {
      showUserPage: true,
      userAuth: ''
    }
  },
  created() {
    // this.getAuth();
  },

  methods: {

    getAuth(data) {
      console.log(data);
      this.userAuth = data
    },

    goAdmin() {
      this.showUserPage = false
      debugger
      this.$router.push({name: 'AdminHome'})
    }
  }
}
</script>

<style>

html, body {
  margin: 0;
  height: 100%;

}

.el-container {
  border: 0px solid rgb(238, 238, 238) !important;
}

a {
  text-decoration: none;
  color: white;
}

.container {
  height: 100%
}

.menu {
  height: 100%;
}

/**
全局button颜色和字体颜色
 */
.button {
  background-color: #545c64 !important;
  border: #545c64;
  color: white !important;
}

.button:hover {
  background-color: #666a73 !important;
  color: white;
}


.el-header {
  background-color: #545c64;
  color: #ffffff;
  line-height: 60px;
}

.el-aside {
  color: #333;
}

.left-menu {
  height: 99.95vh;
}

/*.el-main{*/
/*  padding: 0px 0px 0px 0px  !important;*/
/*}*/

.el-icon-setting {
  color: white;
}

/*进行修改背景*/
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #545c64 !important;
}
</style>
