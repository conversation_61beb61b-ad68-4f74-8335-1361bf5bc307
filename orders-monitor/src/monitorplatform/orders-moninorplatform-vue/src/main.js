// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'

import axios from 'axios'
import VueAxios from 'vue-axios'
import Element<PERSON> from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import router from './router'

Vue.use(VueAxios, axios)
Vue.use(ElementUI);


Vue.config.productionTip = false

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  template: '<App/>',
  render: h => h(App)
})

axios.defaults.withCredentials=true;

// /**
//  * 响应拦截
//  */
// axios.interceptors.response.use(res =>{
//   let data = res.data;
//   if (data.code === 200) {
//      return res;
//   }else {
//     console.log(res.data);
//     ElementUI.Message.error(data.sub_message);
//     return Promise.reject(data.sub_message);
//   }
// }, error => {
//   return Promise.reject(error)
// })
