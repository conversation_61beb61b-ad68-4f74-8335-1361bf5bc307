import http from "../utils/http";
// 本地测试打开(解决不同端口跨域,打包时注释掉)
// let request = '/apis/monitor/operations/degradation'

let request = 'monitor/operations/degradation'

/**
 * 获取降级任务列表
 * @param params
 * @returns {AxiosPromise}
 */
export function getDegradationTaskListAPI(params) {
  return http.post(`${request}/degradation.list.get`, params)
}

/**
 * 新增降级任务
 * @param params
 * @returns {AxiosPromise}
 */
export function addDegradationTaskAPI(params) {
  return http.post(`${request}/degradation.task.add`, params)
}

/**
 * 删除降级任务
 * @param params
 * @returns {AxiosPromise}
 */
export function deleteDegradationTaskAPI(params) {
  return http.post(`${request}/degradation.task.delete`, params)
}

/**
 * 降级任务控制器（开启、关闭降级任务）
 * @param params
 * @returns {AxiosPromise}
 */
export function degradationTaskControl(params) {
  return http.post(`${request}/degradation.task.control`, params)
}

/**
 * 设置定时任务
 * @param params
 * @returns {AxiosPromise}
 */
export function degradationTaskTimer(params) {
  return http.post(`${request}/degradation.task.timer`, params)
}

/**
 * 取消任务定时器
 * @param params
 * @returns {AxiosPromise}
 */
export function cancelDegradationTaskTimer(params) {
  return http.post(`${request}/degradation.task.canceltimer`, params)
}

/**
 * 降级日志获取
 * @param params
 * @returns {AxiosPromise}
 */
export function getDegradationTaskLog(params) {
  return http.post(`${request}/degradation.log.get`, params)
}
