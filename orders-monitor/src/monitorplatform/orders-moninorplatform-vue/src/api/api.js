import http from "../utils/http";
// 本地测试打开(解决不同端口跨域,打包时注释掉)
// let request = '/apis/monitor/operations'

let request = '/monitor/operations'

export function loginAPI(params) {
  return http.post(`${request}/login`, params)
}

// 用户信息相关API
/**
 * EXT表请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getExInfoDataAPI(params) {
  return http.post(`${request}/userinfo/extinfo.list.get`, params)
}

/**
 * 用户信息表请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getUserProductDataAPI(params) {
  return http.post(`${request}/userinfo/productinfo.list.get`, params)
}

/**
 * 订购信息获取请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getOrderSearchDataAPI(params) {
  return http.post(`${request}/userinfo/ordersearchinfo.list.get`, params)
}

/**
 * 用户开户记录请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getOpenUserDataAPI(params) {
  return http.post(`${request}/userinfo/openuserinfolog.list.get`, params)
}

// 订单相关的API
/**
 * ES数据请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getEsInfoAPI(params) {
  return http.post(`${request}/orderinfo/esorderinfo.list.get`, params)
}

/**
 * 主单数据请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getMainOrderInfoAPI(params) {
  return http.post(`${request}/orderinfo/mongoomainoderinfo.list.get`, params)
}

/**
 * 子单数据请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getSuOrderInfoAPI(params) {
  return http.post(`${request}/orderinfo/mongosuborderinfo.list.get`, params)
}

/**
 * 淘宝FullInfo请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getFullInfoAPI(params) {
  return http.post(`${request}/orderinfo/orderfullinfo.list.get`, params)
}

/**
 * 退款订单数据请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getRefundOrderInfoAPI(params) {
  return http.post(`${request}/orderinfo/refundorderinfo.list.get`, params)
}

/**
 * 淘宝退款请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getRefundGetInfoAPI(params) {
  return http.post(`${request}/orderinfo/refundorderapiinfo.list.get`, params)
}

// 问题分析
/**
 * 用户分析结果请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getUserAnalysisResultAPI(params) {
  return http.post(`${request}/analysis/user.problem.analysis`, params)
}

/**
 * 订单分析结果请求API
 * @param params
 * @returns {AxiosPromise}
 */
export function getOrderAnalysisResultAPI(params) {
  return http.post(`${request}/analysis/order.problem.analysis`, params)
}
