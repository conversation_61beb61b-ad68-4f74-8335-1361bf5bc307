import Vue from "vue";
import VueRouter from "vue-router"


import AdminHome from "../view/admin/AdminHome";
import UserInquiry from "../view/problemquery/UserInquiry";
import OrderInquiry from "../view/problemquery/OrderInquiry";

Vue.use(VueRouter)

export default new VueRouter({
    mode: 'history',
    routes: [
      // {
      //   path: '/login',
      //   name: 'Login',
      //   component: Login
      // }
      // // 普通用户
      // // {
      // //   path: '/home',
      // //   name: UserHome,
      // //   component: UserHome,
      //   // children: [
      //   //   {
      //   //     path: '/userInquiry',
      //   //     name: 'UserInquiry',
      //   //     component: UserInquiry
      //   //   },
      //   //   {
      //   //     path: 'orderInquiry',
      //   //     name: 'OrderInquiry',
      //   //     component: OrderInquiry,
      //   //
      //   //   }
      //   // ]
      // },

      // 管理员
      {
        path: '/admin',
        name: 'AdminHome',
        component: AdminHome,
        children: [
          {
            path: 'userInquiry',
            name: 'UserInquiry',
            component: UserInquiry,
          },
          {
            path: 'orderInquiry',
            name: 'OrderInquiry',
            component: OrderInquiry,

          }
        ]
      },
    ]
  }
)
