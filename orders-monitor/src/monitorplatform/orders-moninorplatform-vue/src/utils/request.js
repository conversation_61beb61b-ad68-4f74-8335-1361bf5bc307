import axios from "axios";
import <PERSON>ement<PERSON> from "element-ui";
import {config} from "shelljs";

const service = axios.create({
  baseURL: process.env.BASE_API,
  timeout: 60 * 1000
})
//请求拦截器
service.interceptors.request.use(config => {
  let localToken = localStorage.getItem('token');
  if (localToken != null) {
    config.headers['token'] = localStorage.getItem('token')
  } else {
    let sessionToken = sessionStorage.getItem('token');
    if (sessionToken != null) {
      config.headers['token'] = sessionStorage.getItem('token')
    }
  }
  return config;
})
// 响应拦截器
service.interceptors.response.use(res =>{
  let data = res.data;
  if (data.code === 200) {
    return res;
  }else {
    ElementUI.Message.error(data.sub_message);
    return Promise.reject(data.sub_message);
  }
}, error => {
  return Promise.reject(error)
})


export default service
