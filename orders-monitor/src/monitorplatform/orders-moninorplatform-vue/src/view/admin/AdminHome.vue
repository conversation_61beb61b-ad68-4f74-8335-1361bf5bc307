<template>
  <el-container style="height: 100%; border: 1px solid #eee">
    <!--      管理页面-->
    <template v-if="userAuth == '1'">
      <el-aside width="250px" style="background-color: rgb(84,92,100) ;">
        <el-menu class="left-menu" :default-openeds="['1', '3']"
                 default-active="1-1"
                 background-color="#545c64"
                 text-color="#fff"
                 active-text-color="#ffd04b">
          <el-submenu index="1">
            <template slot="title">
              <i class="el-icon-s-order"></i>
              <span>用户问题查询</span>
            </template>
            <el-menu-item-group>
              <el-menu-item index="1-1" @click="toUserInquiry">
                <i class="el-icon-search"></i>
                <span slot="title">
                用户信息查询
              </span>
              </el-menu-item>
              <el-menu-item index="1-2" @click="toOrderInquiry">
                <i class="el-icon-search"></i>
                <span slot="title">
                用户订单查询查询
              </span>
              </el-menu-item>
            </el-menu-item-group>
          </el-submenu>
          <el-menu-item index="2" @click="toDegradationList">
            <!--      图标    -->
            <i class="el-icon-menu"></i>
            <span slot="title">大促降级管理</span>
          </el-menu-item>
          <!--<el-menu-item index="3">-->
          <!--  &lt;!&ndash;      图标    &ndash;&gt;-->
          <!--  <i class="el-icon-s-custom"></i>-->
          <!--  <span slot="title">-->
          <!--  <router-link to="/user/control/list">用户管理</router-link>-->
          <!--</span>-->
          <!--</el-menu-item>-->
        </el-menu>
      </el-aside>
    </template>
    <!--      普通页面-->
    <template v-else>
      <el-aside width="250px" style="background-color: rgb(84,92,100) ;">
        <el-menu class="left-menu" :default-openeds="['1', '3']"
                 default-active="2"
                 background-color="#545c64"
                 text-color="#fff"
                 active-text-color="#ffd04b">
          <el-submenu index="1">
            <template slot="title">
              <i class="el-icon-s-order"></i>
              <span>用户问题查询</span>
            </template>
            <el-menu-item-group>
              <el-menu-item index="1-1" @click="toUserInquiry">
                <i class="el-icon-search"></i>
                <span slot="title">
                用户信息查询
              </span>
              </el-menu-item>
              <el-menu-item index="1-2" @click="toOrderInquiry">
                <i class="el-icon-search"></i>
                <span slot="title">
                用户订单查询查询
              </span>
              </el-menu-item>
            </el-menu-item-group>
          </el-submenu>
        </el-menu>
      </el-aside>
    </template>

    <el-container>
      <!-- 顶栏 -->
      <el-header style="text-align: right; font-size: 15px">
        <el-row>
          <el-col :span=13>
            <div class="grid-content bg-purple-dark">
              <span style="font-size: 25px; color: white; text-align: center;">中台运维系统</span>
            </div>
          </el-col>
          <el-col :span=11>
            <el-dropdown style="text-align: left;">
              <i class="el-icon-setting" style="margin-right: 15px"></i>
              <el-dropdown-menu slot="dropdown">
                <template v-if="userAuth != ''">
                  <el-dropdown-item @click.native="exitLogin">
                    退出
                  </el-dropdown-item>
                </template>
                <template v-else>
                  <el-dropdown-item @click.native="login">
                    登录
                  </el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
            <template v-if="userAuth != ''">
              <span>{{ userName }}</span>
            </template>
            <template v-else>
              <span>爱用科技</span>
            </template>
          </el-col>
        </el-row>
      </el-header>

      <!--     登录弹窗-->
      <login v-if="isOpenLogin" @cancelLogin="cancelLogin" @initHtml="initHtml"></login>

      <!-- 显示内容 -->
      <el-main>
        <template v-if="sonComponentName === 'UserInquiry'">
          <UserInquiry/>
        </template>
        <template v-if="sonComponentName === 'OrderInquiry'">
          <OrderInquiry/>
        </template>
        <template v-if="sonComponentName === 'Degradation'">
          <Degradation/>
        </template>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>

import OrderInquiry from "../problemquery/OrderInquiry";
import Login from "../Login";
import Degradation from "../degradation/Degradation";
import UserInquiry from "../problemquery/UserInquiry";

export default {
  name: "AdminHome",
  components: {
    Degradation,
    Login,
    UserInquiry,
    OrderInquiry
  },

  created() {
    this.initHtml()
  },

  data() {
    return {
      sonComponentName: '',
      userAuth: '',
      isOpenLogin: false,
      userName: ''
    }
  },

  methods: {
    // 初始化页面
    initHtml() {
      let userAuth
      let userInfo
      let checked = JSON.parse(localStorage.getItem("checked"));
      if (checked) {
        userAuth = localStorage.getItem('userAuth')
        userInfo = localStorage.getItem('userLoginInfo');
      } else {
        userAuth = sessionStorage.getItem('userAuth')
        userInfo = sessionStorage.getItem('userLoginInfo');
      }
      let user = JSON.parse(userInfo);
      if (user != null) {
        this.userName = user.username
      }
      if (userAuth != null) {
        this.userAuth = userAuth
      } else {
        if (userInfo != null) {
          this.login()
        }
      }
      this.toUserInquiry()
    },

    // 退出登录
    exitLogin() {
      localStorage.clear()
      sessionStorage.clear()
      this.userAuth = ''
      this.initHtml()
    },

    // 打开登录弹窗
    login() {
      this.isOpenLogin = !this.isOpenLogin
    },
    // 关闭登录弹窗
    cancelLogin(res) {
      this.isOpenLogin = !this.isOpenLogin
    },


    toUserInquiry() {
      this.sonComponentName = 'UserInquiry'
    },

    toOrderInquiry() {
      this.sonComponentName = 'OrderInquiry'
    },

    toDegradationList() {
      this.sonComponentName = 'Degradation'
    }
  }
}
</script>

<style scoped>
.el-icon-setting {
  color: white;
}
</style>
