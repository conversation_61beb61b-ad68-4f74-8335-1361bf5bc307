<template>
  <el-tabs type="border-card">
    <el-tab-pane>
      <div slot="label" @click="getUserExtInfo">ext用户存单状态表</div>
      <ExtTable ref="extParams"/>
    </el-tab-pane>
    <el-tab-pane label="user_productinfo用户信息表">
      <div slot="label" @click="getUserProductInfo">user_productinfo用户信息表</div>
      <UserProductInfoTable ref="userProductParams"></UserProductInfoTable>
    </el-tab-pane>
    <el-tab-pane label="order_search订购记录表">
      <div slot="label" @click="getOrderSearchInfo">order_search订购记录表</div>
      <OrderSearchTable ref="OrderSearchParams"/>
    </el-tab-pane>
    <el-tab-pane label="open_user开户操作日志表">
      <div slot="label" @click="getOpenUserInfo">open_user开户操作日志表</div>
      <OpenUserTable ref="openUserParams"/>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import Table from "@/components/Table";
import {getExInfoDataAPI, getUserProductDataAPI, getOrderSearchDataAPI, getOpenUserDataAPI} from "@/api/api";
import ExtTable from "./ExtTable";
import UserProductInfoTable from "./UserProductInfoTable";
import OrderSearchTable from "./OrderSearchTable";
import OpenUserTable from "./OpenUserTable";

export default {
  name: "UserInfoList",
  components: {
    Table, ExtTable, UserProductInfoTable, OrderSearchTable, OpenUserTable
  },

  props: {
    userInfo: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      userResultList: [],
    }
  },

  created() {
    this.getUserExtInfo()
  },

  methods: {
    /**
     * 获取用户开户记录
     */
    getOpenUserInfo() {
      let userInfo = this.userInfo;
      if (userInfo == null) {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {

        getOpenUserDataAPI(userInfo).then(res => {
          let body = res.data.body;
          this.userResultList = body
          this.$refs.openUserParams.getData(body)
        })
      }
    },

    /**
     * 获取用户订购信息
     */
    getOrderSearchInfo() {
      let userInfo = this.userInfo;
      if (userInfo == null) {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {
        getOrderSearchDataAPI(userInfo).then(res => {
          let body = res.data.body;
          this.userResultList = body
          this.$refs.OrderSearchParams.getData(body)
        })
      }
    },

    /**
     * 获取用户信息
     */
    getUserProductInfo() {
      let userInfo = this.userInfo;
      if (userInfo == null) {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {
        getUserProductDataAPI(userInfo).then(res => {
          let body = res.data.body;
          this.userResultList = body
          this.$refs.userProductParams.getData(body)
        })
      }
    },

    /**
     * 获取EXT表信息
     */
    getUserExtInfo() {
      let userInfo = this.userInfo;
      if (userInfo == null) {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {
        getExInfoDataAPI(userInfo).then(res => {
          let body = res.data.body;
          this.userResultList = body
          this.$refs.extParams.getData(body)
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
