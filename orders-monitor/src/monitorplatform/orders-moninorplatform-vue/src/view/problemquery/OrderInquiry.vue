<template>
  <div>
    <!-- 用户问题分析查询入口 -->
    <el-form :inline="true" :model="userInfo" class="demo-form-inline" align="center">
      <el-form-item label="卖家昵称">
        <el-input v-model="userInfo.sellerNick" placeholder="请输入卖家昵称"></el-input>
      </el-form-item>
      <el-form-item label="订单号">
        <el-input v-model="userInfo.tid" placeholder="请输入订单号"></el-input>
      </el-form-item>
      <el-form-item label="平台ID">
        <el-select v-model="userInfo.storeId" placeholder="平台昵称">
          <el-option label="淘宝" value="TAO"></el-option>
          <el-option label="拼多多" value="PDD"></el-option>
          <el-option label="1688" value="1688"></el-option>
          <el-option label="抖店" value="DOUDIAN"></el-option>
          <el-option label="微信" value="WXSHOP"></el-option>
          <el-option label="快手" value="KUAISHOU"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="AppName">
        <el-select v-model="userInfo.appName" placeholder="产品名称">
          <el-option label="爱用交易" value="trade"></el-option>
          <el-option label="爱用商品" value="item"></el-option>
          <el-option label="管店" value="guanDian"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="button"  @click="toAnalysisResult" >订单问题分析</el-button>
        <el-button class="button" @click="toTable">订单信息查询</el-button>
      </el-form-item>
    </el-form>
    <el-divider content-position="center">结果展示</el-divider>
    <template v-if="sonComponentName=== 'problemAnalysisResults' ">
      <ProblemAnalysisResults :userInfo = "userInfo" :analyseType="type"/>
    </template>

    <template v-if="sonComponentName === 'orderInfoList'">
      <OrderInfoList :userInfo = "userInfo" />
    </template>
  </div>
</template>

<script>
import ProblemAnalysisResults from "./ProblemAnalysisResults";
import OrderInfoList from "./OrderInfoList";
export default {
  name: "OrderInquiry",

  components:{
    ProblemAnalysisResults,
    OrderInfoList
  },

  data() {
    return {
      type: 'order',
      userInfo: {
        sellerNick : '',
        tid: '',
        storeId:'TAO',
        appName:'trade',
      },
      sonComponentName: ''
    }
  },

  methods: {
    toAnalysisResult() {
      if (this.userInfo.sellerNick === '') {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {
        this.sonComponentName = 'problemAnalysisResults';
      }
    },

    toTable(){
      if (this.userInfo.tid === '') {
        this.$message({
          type: 'warning',
          message: '请输入查询参数',
          center: true
        });
      } else {
        this.sonComponentName = 'orderInfoList';
      }
    }
  }
}
</script>

<style scoped>

</style>
