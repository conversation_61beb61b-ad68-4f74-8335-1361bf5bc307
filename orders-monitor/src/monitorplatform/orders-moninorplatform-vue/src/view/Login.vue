<template>
  <el-dialog title="登录" :visible.sync="openLogin" @close="handleClose" center width="28%">
    <el-form :model="userInfo" style="width: 80%">
      <el-form-item
        label="用户名: "
        prop="username"
        :rules="[
          {required: true, message: '用户名不能为空', trigger: 'blur'},
        ]"
        :label-width="formLabelWidth">
        <el-input
          type="name"
          prefix-icon="el-icon-s-custom"
          placeholder="请输入用户名"
          v-model="userInfo.username"
          autocomplete="off">
        </el-input>
      </el-form-item>
      <el-form-item
        label="密码: "
        prop="password"
        :rules="[
          {required: true, message: '密码不能为空', trigger: 'blur'},
        ]"
        :label-width="formLabelWidth">
        <el-input
          show-password
          prefix-icon="el-icon-s-claim"
          placeholder="请输入密码"
          v-model="userInfo.password"
          autocomplete="on">
        </el-input>
      </el-form-item>
      <el-checkbox v-model="userInfo.checked" style="padding-left: 70%">下次自动登录</el-checkbox>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="openLogin = false">取 消</el-button>
      <el-button class="button" type="primary" @click="login">登 录</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {loginAPI} from "../api/api";

export default {
  name: "Login",
  data() {
    return {
      openLogin: true,
      userInfo: {
        username: '',
        password: '',
        checked: false,
      },
      formLabelWidth: '120px',
      checkLoginInfo: []
    }
  },


  created() {
    let userLoginInfo = localStorage.getItem("userLoginInfo");
    if (userLoginInfo != null) {
      let userInfo = JSON.parse(userLoginInfo);
      this.userInfo = userInfo;
      this.login()
    }
  },

  methods: {
    // 关闭父组件的窗口打开状态
    handleClose() {
      this.$emit('cancelLogin', 'false')
    },

    login() {
      let that = this
      let userInfo = this.userInfo;
      loginAPI(userInfo).then(res => {
        if (res.data.code == 200) {
          if (!userInfo.checked) {
            sessionStorage.setItem('userLoginInfo', JSON.stringify(userInfo))
            sessionStorage.setItem('token', res.headers.token)
            sessionStorage.setItem('userAuth', "1")
          } else {
            localStorage.setItem('userLoginInfo', JSON.stringify(userInfo))
            localStorage.setItem('token', res.headers.token);
            localStorage.setItem('userAuth', "1")
          }
          this.openLogin = false
          localStorage.setItem("checked", userInfo.checked)
          that.$emit('initHtml')
        } else {
          this.$message({
            type: 'error',
            message: '用户名或密码错误',
            center: true
          })
          localStorage.removeItem("userLoginInfo")
        }
      })
    },
  }
}
</script>
