<template>
  <el-container style="height: 100%; border: 1px solid #eee">
    <el-aside width="250px" style="background-color: rgb(84,92,100) ;">
          <template v-if="userAuth == 'admin'">
            <AdminHome/>
          </template>
          <template v-else="userAuth == 'user'" >
            <UserHome/>
          </template>
    </el-aside>
    <el-container>
      <!-- 顶栏 -->
      <el-header style="text-align: right; font-size: 15px">
        <el-row>
          <el-col :span=13>
            <div class="grid-content bg-purple-dark">
              <span style="font-size: 25px; color: white; text-align: center;">中台运维系统</span>
            </div>
          </el-col>
          <el-col :span=11>
            <el-dropdown style="text-align: left;">
              <i class="el-icon-setting" style="margin-right: 15px"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="goAdmin">
                  <!--                  <router-link to="{path: 'admin'}">管理员登录</router-link>-->
                  去管理员页面
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span>爱用科技</span>
          </el-col>
        </el-row>
      </el-header>
      <!-- 显示内容 -->
      <el-main>
        <template v-if="sonComponentName === 'UserInquiry'">
          <UserInquiry/>
        </template>
        <template v-if="sonComponentName === 'OrderInquiry'">
          <OrderInquiry/>
        </template>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import UserHome from "../view/problemquery/UserHome";
import AdminHome from "../view/admin/AdminHome";

export default {
  name: "Header",

  components:{
    UserHome,
    AdminHome,
  },

  data(){
      return{
        userAuth:'user',
        sonComponentName: ''
      }
  },

  methods:{
    getAuth(){

    }
  }
}
</script>

<style scoped>
.el-icon-setting {
  color: white;
}
.left-menu {
  height: 99.9vh;
}
</style>
