<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="UTF-8">
	<!--<meta name="viewport" id="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">-->
	<title>订单 soldGet 校验报告</title>
	<style>
		.body{
			min-width: 800px;
		}
		div{
			margin: auto 0;
		}

		.desc {
			text-align: center;
			color: crimson;
		}

		.outTable{
			margin: auto 0;
			width: 100%;
			border-collapse: collapse;
			display: table;
			text-align: center;
		}

		.innerTable{
			display: table;
			text-align: left;
		}

		.tr{
			display: table-row;
		}

		.td{
			display: table-cell;
			padding: 4px;
		}

		.diff{
			max-width: 650px;
		}

		.bolder {
			font-weight: bolder
		}

		.border{
			border: 1px solid #5f9ea0;
		}


	</style>
</head>
<body class="body">
	<div class="desc bolder">
		soldGet 合格率: <span th:text="${#numbers.formatPercent(result.getAccuracyRate(), 2, 4)}"></span>
	</div>
	<div class="desc bolder" th:object="${result.getOrderMissCheckResult()}">
		有效样本数: <span th:text="*{totalCount.longValue()}"></span>
	</div>
	<div class="desc bolder">
		校验的订单状态: <span th:text="*{result.orderStatus}"></span>
	</div>
	<div class="outTable" th:if="${result.getAccuracyRate() < 1}">
		<div class="tr">
			<div class="td border">seller id</div>
			<div class="td border">seller nick</div>
			<div class="td border">startTime</div>
			<div class="td border">endTime</div>
			<div class="td border">dbId</div>
			<div class="td border">差异节点</div>
		</div>
		<div class="tr" th:each="order:${result.getFailedOrders()}">
			<div class="td border" th:text="${order.getSellerId()}" >sellerID</div>
			<div class="td border" th:text="${order.getSellerNick()}" >sellerNick</div>
			<div class="td border" th:text="${order.getStartTime()}" >startTime</div>
			<div class="td border" th:text="${order.getEndTime()}" >endTime</div>
			<div class="td border" th:text="${order.getDbId()}" >dbId</div>
			<div class="td border">
				<div class="innerTable">
					<div class="tr" th:each="diffs:${order.getDiffs().entrySet()}">
						<div class="tr" th:each="diff:${diffs.getValue()}">
							<div >
								<div class="td bolder" th:text="${diffs.getKey()}">property</div>
								<div class="td diff" th:text="${diff}">diff</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
