webpackJsonp([1],{"+avR":function(e,t){},"0BBC":function(e,t){},"2FKn":function(e,t){},"4FRn":function(e,t){},"9YeL":function(e,t){},CaWX:function(e,t){},ETGH:function(e,t){},FsJg:function(e,t,a){var l={"./cat":"MGUO","./cat.js":"MGUO","./cd":"OkV7","./cd.js":"OkV7","./chmod":"thPH","./chmod.js":"thPH","./common":"8y2q","./common.js":"8y2q","./cp":"kEp2","./cp.js":"kEp2","./dirs":"lK6y","./dirs.js":"lK6y","./echo":"IRJ8","./echo.js":"IRJ8","./error":"HxTw","./error.js":"HxTw","./exec":"VQ1x","./exec-child":"7FO9","./exec-child.js":"7FO9","./exec.js":"VQ1x","./find":"r+ID","./find.js":"r+ID","./grep":"T3mR","./grep.js":"T3mR","./head":"jMmx","./head.js":"jMmx","./ln":"4Bxg","./ln.js":"4Bxg","./ls":"MULN","./ls.js":"MULN","./mkdir":"3ORl","./mkdir.js":"3ORl","./mv":"PcVt","./mv.js":"PcVt","./popd":"h3Dv","./popd.js":"h3Dv","./pushd":"PGBa","./pushd.js":"PGBa","./pwd":"+UCj","./pwd.js":"+UCj","./rm":"EaF0","./rm.js":"EaF0","./sed":"/j+X","./sed.js":"/j+X","./set":"c0cj","./set.js":"c0cj","./sort":"yc2b","./sort.js":"yc2b","./tail":"i//j","./tail.js":"i//j","./tempdir":"RvNk","./tempdir.js":"RvNk","./test":"U9dR","./test.js":"U9dR","./to":"xlaY","./to.js":"xlaY","./toEnd":"Lk2e","./toEnd.js":"Lk2e","./touch":"0Qa2","./touch.js":"0Qa2","./uniq":"lTxy","./uniq.js":"lTxy","./which":"GiAy","./which.js":"GiAy"};function s(e){return a(r(e))}function r(e){var t=l[e];if(!(t+1))throw new Error("Cannot find module '"+e+"'.");return t}s.keys=function(){return Object.keys(l)},s.resolve=r,e.exports=s,s.id="FsJg"},GqOn:function(e,t){},GrjQ:function(e,t){},IHGi:function(e,t){},IsKW:function(e,t){},JgIW:function(e,t){},LW3y:function(e,t){},Mgrp:function(e,t){},NHnr:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("7+uW"),s=a("//Fk"),r=a.n(s),n=a("mtWM"),o=a.n(n),i=a("zL8q"),c=a.n(i),u=(a("YW59"),o.a.create({baseURL:Object({NODE_ENV:"production"}).BASE_API,timeout:6e4}));u.interceptors.request.use(function(e){null!=localStorage.getItem("token")?e.headers.token=localStorage.getItem("token"):null!=sessionStorage.getItem("token")&&(e.headers.token=sessionStorage.getItem("token"));return e}),u.interceptors.response.use(function(e){var t=e.data;return 200===t.code?e:(c.a.Message.error(t.sub_message),r.a.reject(t.sub_message))},function(e){return r.a.reject(e)});var m=u,d={get:function(e,t){var a={method:"get",url:e};return t&&(a.params=t),m(a)},post:function(e,t){var a={method:"post",url:e,header:"application/json"};return t&&(a.data=t),m(a)},put:function(e,t){var a={method:"put",url:e};return t&&(a.params=t),m(a)},delete:function(e,t){var a={method:"delete",url:e};return t&&(a.params=t),m(a)}},f="/monitor/operations";var p={name:"ProblemAnalysisResults",data:function(){return{tableData:[],loading:!0}},created:function(){},mounted:function(){this.getData()},props:{userInfo:{type:Object,default:null},analyseType:{type:String,default:""}},methods:{tableRowClassName:function(e){var t=e.row;e.rowIndex;return 0===t.paramStatusTab?"error-row":"success-row"},getData:function(e){var t,a=this;null==this.userInfo?this.loading=!1:(this.loading=!0,"user"==this.analyseType?(t=this.userInfo,d.post(f+"/analysis/user.problem.analysis",t)).then(function(e){var t=e.data.body.params;a.tableData=t}).finally(function(){a.loading=!1}):"order"==this.analyseType&&function(e){return d.post(f+"/analysis/order.problem.analysis",e)}(this.userInfo).then(function(e){var t=e.data.body.params;a.tableData=t}).finally(function(){a.loading=!1}))}}},v={render:function(){var e=this.$createElement,t=this._self._c||e;return t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:this.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{"header-align":"center",align:"center",data:this.tableData,"max-height":"740px","row-class-name":this.tableRowClassName}},[t("el-table-column",{attrs:{prop:"paramStatusName",label:"状态名",width:"180"}}),this._v(" "),t("el-table-column",{attrs:{prop:"paramStatusValue",label:"状态结果","min-width":"180px"}}),this._v(" "),this._e()],1)},staticRenderFns:[]};var _=a("VU/8")(p,v,!1,function(e){a("IsKW"),a("LW3y")},"data-v-44f2cac0",null).exports,b=a("fZjL"),g=a.n(b),h={name:"Table",data:function(){return{tableTitle:[],tableData:[]}},methods:{getData:function(e){if(null==e&&this.$message.warning("数据不存在"),null!=e[0]){var t=g()(e[0]);this.addTableTitle(t),this.addTableData(e)}else this.$message.warning("数据不存在")},addTableTitle:function(e){this.tableTitle=[];for(var t=0;t<e.length;t++)this.tableTitle.push(e[t])},addTableData:function(e){this.tableData=e},dateFormatData:function(e,t,a,l){if(a instanceof Date&&null!=a){var s=new Date(a);return s.getFullYear()+"-"+(s.getMonth()+1<10?"0"+(s.getMonth()+1):s.getMonth()+1)+"-"+(s.getDate()<10?"0"+s.getDate():s.getDate())+" "+(s.getHours()<10?"0"+s.getHours():s.getHours())+":"+(s.getMinutes()<10?"0"+s.getMinutes():s.getMinutes())+":"+(s.getSeconds()<10?"0"+s.getSeconds():s.getSeconds())}}}},w={render:function(){var e=this.$createElement,t=this._self._c||e;return t("el-col",[t("el-table",{staticStyle:{width:"100%",height:"100%","align-items":"center"},attrs:{data:this.tableData,stripe:"",height:"645px",formatter:this.dateFormatData}},this._l(this.tableTitle,function(e,a){return t("el-table-column",{key:a,staticStyle:{"white-space":"pre-line"},attrs:{align:"center",prop:e,label:e,"min-width":"160px",resizable:"","show-overflow-tooltip":!0}})}),1)],1)},staticRenderFns:[]};var I=a("VU/8")(h,w,!1,function(e){a("9YeL")},"data-v-11c2ca92",null).exports,k={name:"EsOrderInfoTable",data:function(){return{esOrderInfo:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.esOrderInfo=e},dateFormat:function(e,t,a,l){if(a instanceof Date&&null!=a){var s=new Date(a);return s.getFullYear()+"-"+(s.getMonth()+1<10?"0"+(s.getMonth()+1):s.getMonth()+1)+"-"+(s.getDate()<10?"0"+s.getDate():s.getDate())+" "+(s.getHours()<10?"0"+s.getHours():s.getHours())+":"+(s.getMinutes()<10?"0"+s.getMinutes():s.getMinutes())+":"+(s.getSeconds()<10?"0"+s.getSeconds():s.getSeconds())}}}},y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.esOrderInfo,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单状态 : "}},[a("span",[e._v(e._s(t.row.taoStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款状态 : "}},[a("span",[e._v(e._s(t.row.refundStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"售后状态 : "}},[a("span",[e._v(e._s(t.row.afterSaleRefundStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家昵称 : "}},[a("span",[e._v(e._s(t.row.buyerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品标题 : "}},[a("span",[e._v(e._s(t.row.title))])]),e._v(" "),a("el-form-item",{attrs:{label:"子单信息 : "}},[a("span",[e._v(e._s(t.row.subOrders))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人信息 : "}},[a("span",[e._v(e._s(t.row.receiver))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人姓名 : "}},[a("span",[e._v(e._s(t.row.receiverName))])]),e._v(" "),a("el-form-item",{attrs:{label:"SKU名称 : "}},[a("span",[e._v(e._s(t.row.skuName))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家是否评论 : "}},[a("span",[e._v(e._s(t.row.sellerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家是否评论 : "}},[a("span",[e._v(e._s(t.row.buyerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否风控订单 : "}},[a("span",[e._v(e._s(t.row.isRiskControl))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否手动合单 : "}},[a("span",[e._v(e._s(t.row.isManual))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否合单主单 : "}},[a("span",[e._v(e._s(t.row.mergeTradeStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否合单子单 : "}},[a("span",[e._v(e._s(t.row.isCombine))])]),e._v(" "),a("el-form-item",{attrs:{label:"合单Tid : "}},[a("span",[e._v(e._s(t.row.mergeTid))])]),e._v(" "),a("el-form-item",{attrs:{label:"合单爱用Tid : "}},[a("span",[e._v(e._s(t.row.mergeAyTid))])]),e._v(" "),a("el-form-item",{attrs:{label:"Oid : "}},[a("span",[e._v(e._s(t.row.oid))])]),e._v(" "),a("el-form-item",{attrs:{label:"物流公司 : "}},[a("span",[e._v(e._s(t.row.logisticsCompany))])]),e._v(" "),a("el-form-item",{attrs:{label:"运单号 : "}},[a("span",[e._v(e._s(t.row.invoiceNo))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家 ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"tid",prop:"tid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"创建时间",prop:"created"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"修改时间",prop:"modified"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"是否退款订单",prop:"isRefund"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"taoStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款状态",prop:"refundStatus"}})],1)},staticRenderFns:[]};var D={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.fullInfoData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"Oid : "}},[a("span",[e._v(e._s(t.row.oid))])]),e._v(" "),a("el-form-item",{attrs:{label:"oidStr : "}},[a("span",[e._v(e._s(t.row.oidStr))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品价格 : "}},[a("span",[e._v(e._s(t.row.price))])]),e._v(" "),a("el-form-item",{attrs:{label:"折扣费用 : "}},[a("span",[e._v(e._s(t.row.discountFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"分摊之后的实付金额 : "}},[a("span",[e._v(e._s(t.row.divideOrderFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"支付金额 : "}},[a("span",[e._v(e._s(t.row.payment))])]),e._v(" "),a("el-form-item",{attrs:{label:"总费用 : "}},[a("span",[e._v(e._s(t.row.totalFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"发票编号 : "}},[a("span",[e._v(e._s(t.row.invoiceNo))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品数字ID : "}},[a("span",[e._v(e._s(t.row.numIid))])]),e._v(" "),a("el-form-item",{attrs:{label:"子订单预计发货时间 : "}},[a("span",[e._v(e._s(t.row.estimateConTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否代销 : "}},[a("span",[e._v(e._s(t.row.isDaixiao))])]),e._v(" "),a("el-form-item",{attrs:{label:"子订单交易结束时间 : "}},[a("span",[e._v(e._s(t.row.endTime))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Oid",prop:"oid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"status"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款状态",prop:"refundStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"商品标题",prop:"title"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"快递公司",prop:"logisticsCompany"}})],1)},staticRenderFns:[]};var T={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.subOrderData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"公司 ID : "}},[a("span",[e._v(e._s(t.row.corpId))])]),e._v(" "),a("el-form-item",{attrs:{label:"商店编号 : "}},[a("span",[e._v(e._s(t.row.storeId))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单创建时间 : "}},[a("span",[e._v(e._s(t.row.created))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单结束时间 : "}},[a("span",[e._v(e._s(t.row.endTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"爱用Tid : "}},[a("span",[e._v(e._s(t.row.ayTid))])]),e._v(" "),a("el-form-item",{attrs:{label:"爱用Oid : "}},[a("span",[e._v(e._s(t.row.ayOid))])]),e._v(" "),a("el-form-item",{attrs:{label:"爱用状态 : "}},[a("span",[e._v(e._s(t.row.ayStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"交易商品对应类目 : "}},[a("span",[e._v(e._s(t.row.cid))])]),e._v(" "),a("el-form-item",{attrs:{label:"支付金额 : "}},[a("span",[e._v(e._s(t.row.payment))])]),e._v(" "),a("el-form-item",{attrs:{label:"单价 : "}},[a("span",[e._v(e._s(t.row.price))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款ID : "}},[a("span",[e._v(e._s(t.row.refundId))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家评论 : "}},[a("span",[e._v(e._s(t.row.sellerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家评论 : "}},[a("span",[e._v(e._s(t.row.buyerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"SKU ID : "}},[a("span",[e._v(e._s(t.row.skuId))])]),e._v(" "),a("el-form-item",{attrs:{label:"SKU值 : "}},[a("span",[e._v(e._s(t.row.skuPropertiesName))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品标题 : "}},[a("span",[e._v(e._s(t.row.title))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单来源 : "}},[a("span",[e._v(e._s(t.row.taoOrderFrom))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否超卖 : "}},[a("span",[e._v(e._s(t.row.isOversold))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否拆单发货 : "}},[a("span",[e._v(e._s(t.row.isSplit))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否赠品 : "}},[a("span",[e._v(e._s(t.row.isGift))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否商家承担手续费 : "}},[a("span",[e._v(e._s(t.row.isFqgSFee))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家 ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Tid",prop:"tid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Oid",prop:"oid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"taoStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款状态",prop:"refundStatus"}})],1)},staticRenderFns:[]};var x={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.refundOrderData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单创建时间 : "}},[a("span",[e._v(e._s(t.row.created))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单修改时间 : "}},[a("span",[e._v(e._s(t.row.modified))])]),e._v(" "),a("el-form-item",{attrs:{label:"不需要客服介入 : "}},[a("span",[e._v(e._s(t.row.csStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款说明 : "}},[a("span",[e._v(e._s(t.row.desc))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家是否需要退款 : "}},[a("span",[e._v(e._s(t.row.hasGoodReturn))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品数字ID : "}},[a("span",[e._v(e._s(t.row.numIid))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品状态 : "}},[a("span",[e._v(e._s(t.row.goodStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品外部商家编码 : "}},[a("span",[e._v(e._s(t.row.outerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"支付给卖家的金额 : "}},[a("span",[e._v(e._s(t.row.payment))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品价格 : "}},[a("span",[e._v(e._s(t.row.price))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款原因 : "}},[a("span",[e._v(e._s(t.row.reason))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款金额 : "}},[a("span",[e._v(e._s(t.row.refundFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款阶段 : "}},[a("span",[e._v(e._s(t.row.refundPhase))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款版本号 : "}},[a("span",[e._v(e._s(t.row.refundVersion))])]),e._v(" "),a("el-form-item",{attrs:{label:"SKU名称 : "}},[a("span",[e._v(e._s(t.row.sku))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品标题 : "}},[a("span",[e._v(e._s(t.row.title))])]),e._v(" "),a("el-form-item",{attrs:{label:"总金额 : "}},[a("span",[e._v(e._s(t.row.totalFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"地址 : "}},[a("span",[e._v(e._s(t.row.address))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家 ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Tid",prop:"tid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Oid",prop:"oid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款状态",prop:"status"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"orderStatus"}}),e._v("\r\n    、"),a("el-table-column",{attrs:{align:"center",label:"退款ID",prop:"refundId"}})],1)},staticRenderFns:[]};var S={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.refundOrderData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"Oid : "}},[a("span",[e._v(e._s(t.row.oid))])]),e._v(" "),a("el-form-item",{attrs:{label:"oidStr : "}},[a("span",[e._v(e._s(t.row.oidStr))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单创建时间 : "}},[a("span",[e._v(e._s(t.row.created))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单修改时间 : "}},[a("span",[e._v(e._s(t.row.modified))])]),e._v(" "),a("el-form-item",{attrs:{label:"不需要客服介入 : "}},[a("span",[e._v(e._s(t.row.csStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款说明 : "}},[a("span",[e._v(e._s(t.row.desc))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家是否需要退款 : "}},[a("span",[e._v(e._s(t.row.hasGoodReturn))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品数字ID : "}},[a("span",[e._v(e._s(t.row.numIid))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品状态 : "}},[a("span",[e._v(e._s(t.row.goodStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品外部商家编码 : "}},[a("span",[e._v(e._s(t.row.outerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"支付给卖家的金额 : "}},[a("span",[e._v(e._s(t.row.payment))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品价格 : "}},[a("span",[e._v(e._s(t.row.price))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款原因 : "}},[a("span",[e._v(e._s(t.row.reason))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款金额 : "}},[a("span",[e._v(e._s(t.row.refundFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款阶段 : "}},[a("span",[e._v(e._s(t.row.refundPhase))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款版本号 : "}},[a("span",[e._v(e._s(t.row.refundVersion))])]),e._v(" "),a("el-form-item",{attrs:{label:"SKU名称 : "}},[a("span",[e._v(e._s(t.row.sku))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品标题 : "}},[a("span",[e._v(e._s(t.row.title))])]),e._v(" "),a("el-form-item",{attrs:{label:"总金额 : "}},[a("span",[e._v(e._s(t.row.totalFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"地址 : "}},[a("span",[e._v(e._s(t.row.address))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Oid",prop:"oid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款状态",prop:"refundStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"status"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款ID",prop:"refundId"}})],1)},staticRenderFns:[]};var O={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.tcOrderData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单创建时间 : "}},[a("span",[e._v(e._s(t.row.created))])]),e._v(" "),a("el-form-item",{attrs:{label:"Tid : "}},[a("span",[e._v(e._s(t.row.tid))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家 ID : "}},[a("span",[e._v(e._s(t.row.buyerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"爱用状态 : "}},[a("span",[e._v(e._s(t.row.ayStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否退款 : "}},[a("span",[e._v(e._s(t.row.isRefund))])]),e._v(" "),a("el-form-item",{attrs:{label:"付款时间 : "}},[a("span",[e._v(e._s(t.row.payTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人姓名 : "}},[a("span",[e._v(e._s(t.row.receiverName))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人地址 : "}},[a("span",[e._v(e._s(t.row.receiverAddress))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人城市 : "}},[a("span",[e._v(e._s(t.row.receiverCity))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人国家 : "}},[a("span",[e._v(e._s(t.row.receiverCountry))])]),e._v(" "),a("el-form-item",{attrs:{label:"收件人城镇 : "}},[a("span",[e._v(e._s(t.row.receiverDistrict))])]),e._v(" "),a("el-form-item",{attrs:{label:"货到付款状态 : "}},[a("span",[e._v(e._s(t.row.codStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家评论状态 : "}},[a("span",[e._v(e._s(t.row.sellerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"买家评论状态 : "}},[a("span",[e._v(e._s(t.row.buyerRate))])]),e._v(" "),a("el-form-item",{attrs:{label:"承诺服务类型 : "}},[a("span",[e._v(e._s(t.row.promiseServiceType))])]),e._v(" "),a("el-form-item",{attrs:{label:"平台补贴费用 : "}},[a("span",[e._v(e._s(t.row.platformSubsidyFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否信用支付 : "}},[a("span",[e._v(e._s(t.row.isCreditPay))])]),e._v(" "),a("el-form-item",{attrs:{label:"爱用链路状态 : "}},[a("span",[e._v(e._s(t.row.ayOrderType))])]),e._v(" "),a("el-form-item",{attrs:{label:"服务标签 : "}},[a("span",[e._v(e._s(t.row.serviceTags))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"买家ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Tid",prop:"tid"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单状态",prop:"taoStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"创建时间",prop:"created"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"修改时间",prop:"modified"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"平台ID",prop:"storeId"}})],1)},staticRenderFns:[]};var N={name:"OrderInfoList",components:{Table:I,EsOrderInfoTable:a("VU/8")(k,y,!1,function(e){a("jvwQ")},"data-v-05050c0e",null).exports,FullInfoTable:a("VU/8")({name:"FullInfoTable",data:function(){return{fullInfoData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.fullInfoData=e}}},D,!1,function(e){a("gx1J")},"data-v-2f068610",null).exports,SubOrderTable:a("VU/8")({name:"SubOrderTable",data:function(){return{subOrderData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.subOrderData=e}}},T,!1,function(e){a("RMmM")},"data-v-2437fea4",null).exports,RefundOrderTable:a("VU/8")({name:"RefundOrderTable",data:function(){return{refundOrderData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.refundOrderData=e}}},x,!1,function(e){a("2FKn")},"data-v-89c440e0",null).exports,RefundApiTable:a("VU/8")({name:"RefundApiTable",data:function(){return{refundOrderData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.refundOrderData=e}}},S,!1,function(e){a("i7fE")},"data-v-92d45bae",null).exports,TcOrderTable:a("VU/8")({name:"TcOrderTable",data:function(){return{tcOrderData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.tcOrderData=e}}},O,!1,function(e){a("JgIW")},"data-v-04df12a9",null).exports},props:{userInfo:{tcpType:Object,default:null}},data:function(){return{orderResultList:[]}},created:function(){this.getEsOrderInfo()},methods:{getEsOrderInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/orderinfo/esorderinfo.list.get",e)).then(function(e){var a=e.data.body;t.orderResultList=a,t.$refs.esOrderData.getData(a)})},getMongoMainOrderInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/orderinfo/mongoomainoderinfo.list.get",e)).then(function(e){var a=e.data.body;t.orderResultList=a,t.$refs.mangoMainOrderData.getData(a)})},getOrderFullInfoApiInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/orderinfo/orderfullinfo.list.get",e)).then(function(e){var a=e.data.body;t.orderResultList=a,t.$refs.OrderFullInfoData.getData(a)})},getMongoSubOrderInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/orderinfo/mongosuborderinfo.list.get",e)).then(function(e){var a=e.data.body;t.orderResultList=a,t.$refs.mangoSubOrderData.getData(a)})},getRefundOrder:function(){var e,t=this,a=this.userInfo;if(null==a)this.$message({type:"warning",message:"请输入查询参数",center:!0});else{var l=[];(e=a,d.post(f+"/orderinfo/refundorderinfo.list.get",e)).then(function(e){l=e.data.body,t.orderResultList=l,t.$refs.refundOrderData.getData(l)})}},getOrderRefundApiInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/orderinfo/refundorderapiinfo.list.get",e)).then(function(e){var a=e.data.body;t.orderResultList=a,t.$refs.refundAPIData.getData(a)})}}},$={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tabs",{staticStyle:{height:"100%"},attrs:{type:"border-card"}},[a("el-tab-pane",{attrs:{label:"ES存单数据库"}},[a("div",{attrs:{slot:"label"},on:{click:e.getEsOrderInfo},slot:"label"},[e._v("ES存单数据库")]),e._v(" "),a("EsOrderInfoTable",{ref:"esOrderData"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Mongo主单数据库"}},[a("div",{attrs:{slot:"label"},on:{click:e.getMongoMainOrderInfo},slot:"label"},[e._v("Mongo主单数据库")]),e._v(" "),a("TcOrderTable",{ref:"mangoMainOrderData"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"FullInfoAPI返回信息"}},[a("div",{attrs:{slot:"label"},on:{click:e.getOrderFullInfoApiInfo},slot:"label"},[e._v("FullInfoAPI返回信息")]),e._v(" "),a("FullInfoTable",{ref:"OrderFullInfoData"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Mongo子单数据库"}},[a("div",{attrs:{slot:"label"},on:{click:e.getMongoSubOrderInfo},slot:"label"},[e._v("Mongo子单数据库")]),e._v(" "),a("SubOrderTable",{ref:"mangoSubOrderData"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"退款订单库"}},[a("div",{attrs:{slot:"label"},on:{click:e.getRefundOrder},slot:"label"},[e._v("退款订单库")]),e._v(" "),a("RefundOrderTable",{ref:"refundOrderData"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"退款API结果"}},[a("div",{attrs:{slot:"label"},on:{click:e.getOrderRefundApiInfo},slot:"label"},[e._v("退款API结果")]),e._v(" "),a("RefundApiTable",{ref:"refundAPIData"})],1)],1)},staticRenderFns:[]};var R={name:"OrderInquiry",components:{ProblemAnalysisResults:_,OrderInfoList:a("VU/8")(N,$,!1,function(e){a("GqOn")},"data-v-2b4da546",null).exports},data:function(){return{type:"order",userInfo:{sellerNick:"",tid:"",storeId:"TAO",appName:"trade"},sonComponentName:""}},methods:{toAnalysisResult:function(){""===this.userInfo.sellerNick?this.$message({type:"warning",message:"请输入查询参数",center:!0}):this.sonComponentName="problemAnalysisResults"},toTable:function(){""===this.userInfo.tid?this.$message({type:"warning",message:"请输入查询参数",center:!0}):this.sonComponentName="orderInfoList"}}},C={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.userInfo,align:"center"}},[a("el-form-item",{attrs:{label:"卖家昵称"}},[a("el-input",{attrs:{placeholder:"请输入卖家昵称"},model:{value:e.userInfo.sellerNick,callback:function(t){e.$set(e.userInfo,"sellerNick",t)},expression:"userInfo.sellerNick"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.userInfo.tid,callback:function(t){e.$set(e.userInfo,"tid",t)},expression:"userInfo.tid"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"平台ID"}},[a("el-select",{attrs:{placeholder:"平台昵称"},model:{value:e.userInfo.storeId,callback:function(t){e.$set(e.userInfo,"storeId",t)},expression:"userInfo.storeId"}},[a("el-option",{attrs:{label:"淘宝",value:"TAO"}}),e._v(" "),a("el-option",{attrs:{label:"拼多多",value:"PDD"}}),e._v(" "),a("el-option",{attrs:{label:"1688",value:"1688"}}),e._v(" "),a("el-option",{attrs:{label:"抖店",value:"DOUDIAN"}}),e._v(" "),a("el-option",{attrs:{label:"微信",value:"WXSHOP"}}),e._v(" "),a("el-option",{attrs:{label:"快手",value:"KUAISHOU"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"AppName"}},[a("el-select",{attrs:{placeholder:"产品名称"},model:{value:e.userInfo.appName,callback:function(t){e.$set(e.userInfo,"appName",t)},expression:"userInfo.appName"}},[a("el-option",{attrs:{label:"爱用交易",value:"trade"}}),e._v(" "),a("el-option",{attrs:{label:"爱用商品",value:"item"}}),e._v(" "),a("el-option",{attrs:{label:"管店",value:"guanDian"}})],1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"button",on:{click:e.toAnalysisResult}},[e._v("订单问题分析")]),e._v(" "),a("el-button",{staticClass:"button",on:{click:e.toTable}},[e._v("订单信息查询")])],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"center"}},[e._v("结果展示")]),e._v(" "),"problemAnalysisResults"===e.sonComponentName?[a("ProblemAnalysisResults",{attrs:{userInfo:e.userInfo,analyseType:e.type}})]:e._e(),e._v(" "),"orderInfoList"===e.sonComponentName?[a("OrderInfoList",{attrs:{userInfo:e.userInfo}})]:e._e()],2)},staticRenderFns:[]};var L=a("VU/8")(R,C,!1,function(e){a("0BBC")},"data-v-5e56a353",null).exports,P=a("mvHQ"),U=a.n(P),A={name:"Login",data:function(){return{openLogin:!0,userInfo:{username:"",password:"",checked:!1},formLabelWidth:"120px",checkLoginInfo:[]}},created:function(){var e=localStorage.getItem("userLoginInfo");if(null!=e){var t=JSON.parse(e);this.userInfo=t,this.login()}},methods:{handleClose:function(){this.$emit("cancelLogin","false")},login:function(){var e,t=this,a=this,l=this.userInfo;(e=l,d.post(f+"/login",e)).then(function(e){200==e.data.code?(l.checked?(localStorage.setItem("userLoginInfo",U()(l)),localStorage.setItem("token",e.headers.token),localStorage.setItem("userAuth","1")):(sessionStorage.setItem("userLoginInfo",U()(l)),sessionStorage.setItem("token",e.headers.token),sessionStorage.setItem("userAuth","1")),t.openLogin=!1,localStorage.setItem("checked",l.checked),a.$emit("initHtml")):(t.$message({type:"error",message:"用户名或密码错误",center:!0}),localStorage.removeItem("userLoginInfo"))})}}},F={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"登录",visible:e.openLogin,center:"",width:"28%"},on:{"update:visible":function(t){e.openLogin=t},close:e.handleClose}},[a("el-form",{staticStyle:{width:"80%"},attrs:{model:e.userInfo}},[a("el-form-item",{attrs:{label:"用户名: ",prop:"username",rules:[{required:!0,message:"用户名不能为空",trigger:"blur"}],"label-width":e.formLabelWidth}},[a("el-input",{attrs:{type:"name","prefix-icon":"el-icon-s-custom",placeholder:"请输入用户名",autocomplete:"off"},model:{value:e.userInfo.username,callback:function(t){e.$set(e.userInfo,"username",t)},expression:"userInfo.username"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"密码: ",prop:"password",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}],"label-width":e.formLabelWidth}},[a("el-input",{attrs:{"show-password":"","prefix-icon":"el-icon-s-claim",placeholder:"请输入密码",autocomplete:"on"},model:{value:e.userInfo.password,callback:function(t){e.$set(e.userInfo,"password",t)},expression:"userInfo.password"}})],1),e._v(" "),a("el-checkbox",{staticStyle:{"padding-left":"70%"},model:{value:e.userInfo.checked,callback:function(t){e.$set(e.userInfo,"checked",t)},expression:"userInfo.checked"}},[e._v("下次自动登录")])],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.openLogin=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{staticClass:"button",attrs:{type:"primary"},on:{click:e.login}},[e._v("登 录")])],1)],1)},staticRenderFns:[]},E=a("VU/8")(A,F,!1,null,null,null).exports,j=a("INCx"),q=a.n(j),M="monitor/operations/degradation";var V={name:"DegradationTimerDialog",props:{taskName:{type:String,default:""}},data:function(){return{isOpenTimeDialog:!0,openTime:"",closeTime:"",timedInfo:{taskName:this.taskName,startTime:"",endTime:""},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()}},value:[]}},methods:{closeDialog:function(){this.$emit("timerDialogController")},refresh:function(){this.$emit("refreshTable")},setTimedTask:function(){var e,t=this;this.timedInfo.startTime=this.openTime,this.timedInfo.endTime=this.closeTime,(e=this.timedInfo,d.post(M+"/degradation.task.timer",e)).then(function(e){console.log(e),200===e.data.code?(t.$message({type:"success",message:e.data.body,center:!0}),t.closeDialog(),t.refresh()):t.$message({type:"error",message:e.data.message,center:!0})})},getLastTime:function(){},cancelTaskTimer:function(){var e=this;this.$confirm("取消定时任务, 是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t;(t=e.timedInfo,d.post(M+"/degradation.task.canceltimer",t)).then(function(t){200===t.data.code?(e.$message({type:"success",message:t.data.message,center:!0}),e.refresh()):e.$message({type:"error",message:t.data.sub_message,center:!0})})}).catch(function(){e.$message({type:"info",message:"已取消操作",center:!0})})}}},H={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"定时降级",visible:e.isOpenTimeDialog,width:"30%"},on:{"update:visible":function(t){e.isOpenTimeDialog=t},close:e.closeDialog}},[a("el-form",{attrs:{model:e.timedInfo}},[a("el-row",[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{label:"任务名称："}},[a("el-input",{staticStyle:{width:"35%"},attrs:{disabled:"",autocomplete:"off"},model:{value:e.timedInfo.taskName,callback:function(t){e.$set(e.timedInfo,"taskName",t)},expression:"timedInfo.taskName"}})],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:18}},[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{label:"开启降级时间："}},[a("div",{staticClass:"block"},[a("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":e.pickerOptions,placeholder:"选择日期时间"},model:{value:e.openTime,callback:function(t){e.openTime=t},expression:"openTime"}})],1)])],1),e._v(" "),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"button",attrs:{type:"info"},on:{click:e.cancelTaskTimer}},[e._v("取消定时任务")])],1)],1),e._v(" "),a("el-row",[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{label:"恢复降级时间："}},[a("div",{staticClass:"block"},[a("el-date-picker",{attrs:{type:"datetime","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期时间"},model:{value:e.closeTime,callback:function(t){e.closeTime=t},expression:"closeTime"}})],1)])],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),e._v(" "),a("el-button",{staticClass:"button",attrs:{type:"primary"},on:{click:e.setTimedTask}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var z=a("VU/8")(V,H,!1,function(e){a("W1gd")},"data-v-0625ae24",null).exports,W={name:"Paging",props:{total:{type:Number,default:0}},watch:{total:function(e,t){this.nowTotal=e}},data:function(){return{nowTotal:0,nowCurrentPage:0,pageRequest:{startIndex:0,pageSize:10}}},created:function(){this.$emit("getPageRequest",U()(this.pageRequest))},methods:{currentChange:function(e){this.nowCurrentPage=e,this.pageRequest.startIndex=(e-1)*this.pageRequest.pageSize,this.$emit("getPageRequest",U()(this.pageRequest))},clearCurrentPage:function(e){e&&(this.nowCurrentPage=this.nowCurrentPage-1)}}},B={render:function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-pagination",{attrs:{background:"","current-page":e.nowCurrentPage,"page-size":this.pageRequest.pageSize,layout:"prev, pager, next",total:e.nowTotal},on:{"update:currentPage":function(t){e.nowCurrentPage=t},"update:current-page":function(t){e.nowCurrentPage=t},"current-change":e.currentChange}})},staticRenderFns:[]};var G=a("VU/8")(W,B,!1,function(e){a("IHGi")},"data-v-78f14562",null).exports,K={name:"DegradationLogDialog",props:{taskName:{type:String,default:""}},components:{Paging:G},data:function(){return{isOpenLogDialog:!0,nowTotal:0,taskInfo:{taskName:this.taskName,startIndex:0,pageSize:0},logList:[]}},methods:{closeDialog:function(){this.$emit("logDialogController")},getPageRequest:function(e){var t=JSON.parse(e);this.taskInfo.startIndex=t.startIndex,this.taskInfo.pageSize=t.pageSize,this.getDegradationTaskLogRecord()},getDegradationTaskLogRecord:function(){var e,t=this;(e=this.taskInfo,d.post(M+"/degradation.log.get",e)).then(function(e){t.logList=e.data.body.content,t.nowTotal=e.data.body.total})}}},J={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"降级日志",visible:e.isOpenLogDialog,width:"45%"},on:{"update:visible":function(t){e.isOpenLogDialog=t},close:e.closeDialog}},[a("el-row",{staticStyle:{"text-align":"left"}},[e._v("\r\n      任务名称\r\n      "),a("el-input",{staticStyle:{width:"25%"},attrs:{disabled:"",autocomplete:"off"},model:{value:e.taskInfo.taskName,callback:function(t){e.$set(e.taskInfo,"taskName",t)},expression:"taskInfo.taskName"}})],1),e._v(" "),a("el-row",{staticStyle:{"text-align":"left"}},[a("el-table",{attrs:{size:"small","current-row-key":"id",data:e.logList,stripe:"","highlight-current-row":""}},[e._v("--\x3e\r\n        "),a("el-table-column",{attrs:{prop:"gmtCreate",label:"任务时间",align:"left",width:"200%"}}),e._v(" "),a("el-table-column",{attrs:{prop:"content",label:"任务日志",align:"left"}})],1),e._v(" "),a("Paging",{ref:"paging",attrs:{total:e.nowTotal},on:{getPageRequest:e.getPageRequest}})],1)],1)},staticRenderFns:[]};var Q={name:"DegradationTable",components:{DegradationLogDialog:a("VU/8")(K,J,!1,function(e){a("CaWX")},"data-v-0a52bf54",null).exports,DegradationTimerDialog:z,Paging:G},data:function(){return{degradationList:[],isOpenTimerDialog:!1,isOpenLogDialog:!1,currentRowIndex:0,taskInfo:{taskName:"",switchStatus:""}}},props:{taskList:{default:[]}},watch:{taskList:function(e,t){this.degradationList=e;for(var a=0;a<this.degradationList.length;a++)if(null==e[a].startTime&&null==e[a].endTime)this.degradationList[a].timerStatus=0;else{var l=new Date(e[a].startTime.replace(/-/g,"/")).getTime(),s=new Date(e[a].endTime.replace(/-/g,"/")).getTime();if(null!=l&&null!=s)l>Date.now()?this.degradationList[a].timerStatus=2:l<Date.now()&&s>Date.now()?this.degradationList[a].timerStatus=2:l<Date.now()&&s<Date.now()&&(this.degradationList[a].timerStatus=0);else{(null!=l?l:s)<Date.now()?this.degradationList[a].timerStatus=2:this.degradationList[a].timerStatus=0}}}},methods:{refreshTable:function(e){this.$emit("queryTask",e)},deleteDegradationTask:function(e){var t=this;this.$confirm("此操作将删除该任务, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a;t.taskInfo.taskName=e,(a=t.taskInfo,d.post(M+"/degradation.task.delete",a)).then(function(e){200==e.data.code?(t.$message({message:e.data.body,type:"success",center:!0}),t.refreshTable(t.currentRowIndex)):t.$message({message:e.data.message,type:"error",center:!0})}).catch(function(){t.$message({type:"info",message:"已取消操作"}),e.switchStatus=+!e.switchStatus})})},degradationControl:function(e){this.openMessageBox(e)},openMessageBox:function(e){var t=this;this.$confirm("执行降级操作, 是否继续?","告警",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a;t.taskInfo.switchStatus=e.switchStatus,t.taskInfo.taskName=e.name,(a=t.taskInfo,d.post(M+"/degradation.task.control",a)).then(function(a){200==a.data.code?(t.$message({message:a.data.message,type:"success",center:!0}),t.refreshTable(t.currentRowIndex)):(t.$message({message:a.data.message,type:"error",center:!0}),e.switchStatus=+!e.switchStatus)})}).catch(function(){t.$message({type:"info",message:"已取消操作",center:!0}),e.switchStatus=+!e.switchStatus})},timerDialogController:function(e){this.taskInfo.taskName=e,this.isOpenTimerDialog=!this.isOpenTimerDialog},logDialogController:function(e){this.taskInfo.taskName=e,this.isOpenLogDialog=!this.isOpenLogDialog},tableRow:function(e){var t=e.row,a=e.rowIndex;t.row_index=a},onRowClick:function(e,t,a){this.currentRowIndex=e.row_index}}},Y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.degradationList,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center"},"row-class-name":e.tableRow,stripe:""},on:{"row-click":e.onRowClick}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"100%"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"任务名称",width:"200%"}}),e._v(" "),a("el-table-column",{attrs:{prop:"description",label:"描述",width:"500%"}}),e._v(" "),a("el-table-column",{attrs:{prop:"timerStatus",label:"定时任务启用状态",width:"250%"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{staticClass:"degradation-tag",attrs:{type:0===t.row.timerStatus?"info":1===t.row.timerStatus?"success":"warning"}},[e._v("\r\n            "+e._s(0===t.row.timerStatus?"无定时任务":1===t.row.timerStatus?"定时任务已开启":"定时任务准备中")+"\r\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isOpenDegradationTask",label:"是否降级",width:"150%"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-text":"开启","active-value":1,"active-color":"#13ce66","inactive-text":"恢复","inactive-value":0,"inactive-color":"#f4f4f5"},on:{change:function(a){return e.degradationControl(t.row)}},model:{value:t.row.switchStatus,callback:function(a){e.$set(t.row,"switchStatus",a)},expression:"scope.row.switchStatus"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"degradationOperation",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"button",attrs:{type:"success"},on:{click:function(a){return e.timerDialogController(t.row.name)}}},[e._v("定时降级")]),e._v(" "),a("el-button",{staticClass:"button",attrs:{type:"success"},on:{click:function(a){return e.logDialogController(t.row.name)}}},[e._v("降级日志")]),e._v(" "),a("el-button",{attrs:{type:"danger"},on:{click:function(a){return e.deleteDegradationTask(t.row.name)}}},[e._v("删除任务")])]}}])})],1),e._v(" "),[a("el-button",{attrs:{type:"text"},on:{click:e.openMessageBox}})],e._v(" "),e.isOpenTimerDialog?[a("DegradationTimerDialog",{ref:"degradationTimerDialog",attrs:{taskName:e.taskInfo.taskName},on:{timerDialogController:e.timerDialogController,refreshTable:e.refreshTable}})]:e._e(),e._v(" "),e.isOpenLogDialog?[a("DegradationLogDialog",{ref:"log",attrs:{taskName:e.taskInfo.taskName},on:{logDialogController:e.logDialogController}})]:e._e()],2)},staticRenderFns:[]};var X={name:"AddDegradation",data:function(){return{isOpenAddDialog:!0,taskInfo:{taskName:"",configurationInterface:"apollo",projectId:"",userName:"",addressName:"",nameSpace:"",openStatusParameter:"",closeStatusParameter:"",key:""},options:[{value:"userRedis",label:"UserRedis"},{value:"stringRedis",label:"StringRedis"},{value:"apollo",label:"Apollo"},{value:"mysql",label:"mysql"}]}},methods:{addTask:function(){var e,t=this,a=this.taskInfo;(e=a,d.post(M+"/degradation.task.add",e)).then(function(e){console.log(e),200==e.data.code?(t.$message({message:e.data.body,type:"success",center:!0}),t.$emit("queryTask"),t.$emit("addDialogController")):t.$message({message:e.data.message,type:"error",center:!0})})},cancel:function(){this.$emit("addDialogController")}}},Z={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"添加任务",visible:e.isOpenAddDialog},on:{"update:visible":function(t){e.isOpenAddDialog=t},close:e.cancel}},[a("el-form",{ref:"dataAddForm",attrs:{model:e.taskInfo,"label-position":"right","label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务名称"}},[a("el-input",{model:{value:e.taskInfo.taskName,callback:function(t){e.$set(e.taskInfo,"taskName",t)},expression:"taskInfo.taskName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"配置地址"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.taskInfo.configurationInterface,callback:function(t){e.$set(e.taskInfo,"configurationInterface",t)},expression:"taskInfo.configurationInterface"}},e._l(e.options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.taskInfo.description,callback:function(t){e.$set(e.taskInfo,"description",t)},expression:"taskInfo.description"}})],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"项目ID"}},[a("el-input",{attrs:{placeholder:"apollo必填，其他不填"},model:{value:e.taskInfo.projectId,callback:function(t){e.$set(e.taskInfo,"projectId",t)},expression:"taskInfo.projectId"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"操作用户"}},[a("el-input",{attrs:{placeholder:"apollo必填，其他不填"},model:{value:e.taskInfo.userName,callback:function(t){e.$set(e.taskInfo,"userName",t)},expression:"taskInfo.userName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"集群名称"}},[a("el-input",{attrs:{placeholder:"apollo必填，其他不填"},model:{value:e.taskInfo.addressName,callback:function(t){e.$set(e.taskInfo,"addressName",t)},expression:"taskInfo.addressName"}})],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"命名空间"}},[a("el-input",{attrs:{placeholder:"apollo必填，redis对应fild"},model:{value:e.taskInfo.nameSpace,callback:function(t){e.$set(e.taskInfo,"nameSpace",t)},expression:"taskInfo.nameSpace"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"配置Key值"}},[a("el-input",{attrs:{placeholder:"mysql对应SQL语句，redis和Apollo对应Key"},model:{value:e.taskInfo.key,callback:function(t){e.$set(e.taskInfo,"key",t)},expression:"taskInfo.key"}})],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"自定义开启参数"}},[a("el-input",{attrs:{placeholder:"true、on，mysql不填"},model:{value:e.taskInfo.openStatusParameter,callback:function(t){e.$set(e.taskInfo,"openStatusParameter",t)},expression:"taskInfo.openStatusParameter"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"自定义关闭参数"}},[a("el-input",{attrs:{placeholder:"false、off，mysql不填"},model:{value:e.taskInfo.closeStatusParameter,callback:function(t){e.$set(e.taskInfo,"closeStatusParameter",t)},expression:"taskInfo.closeStatusParameter"}})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.cancel()}}},[e._v("取消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.addTask}},[e._v("确定")])],1)],1)},staticRenderFns:[]};var ee={name:"Degradation",components:{DegradationTable:a("VU/8")(Q,Y,!1,function(e){a("Qd2w")},"data-v-0b55cd40",null).exports,AddDegradation:a("VU/8")(X,Z,!1,function(e){a("+avR")},"data-v-310c121e",null).exports,Paging:G},data:function(){return{taskList:{},name:"",taskInfo:{taskName:"",startIndex:0,pageSize:0},nowTotal:0,isOpenAddDialog:!1}},methods:{getPageRequest:function(e){var t=JSON.parse(e);this.taskInfo.startIndex=t.startIndex,this.taskInfo.pageSize=t.pageSize,this.getTaskList()},queryTask:function(e){if(this.taskInfo.taskName=this.name,0===e){var t=q()(Math.floor(this.taskInfo.startIndex/this.taskInfo.pageSize));this.taskInfo.startIndex=t-1<0?0:t-1,this.$refs.paging.clearCurrentPage(!0)}else this.$refs.paging.clearCurrentPage();this.getTaskList()},getTaskList:function(){var e,t=this,a=this.taskInfo;(e=a,d.post(M+"/degradation.list.get",e)).then(function(e){t.taskList=e.data.body.degradationTaskList,t.nowTotal=e.data.body.total})},addDialogController:function(){this.isOpenAddDialog=!this.isOpenAddDialog}}},te={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{attrs:{inline:!0,model:e.taskInfo}},[a("el-form-item",{attrs:{label:"",prop:"taskName"}},[a("el-input",{attrs:{"prefix-icon":"el-icon-s-promotion",clearable:"",type:"name",placeholder:"任务名称"},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"button",attrs:{type:"primary"},on:{click:e.queryTask}},[e._v("查询")])],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"button",attrs:{type:"primary"},on:{click:e.addDialogController}},[e._v("新增")])],1)],1),e._v(" "),a("DegradationTable",{attrs:{taskList:e.taskList},on:{queryTask:e.queryTask}}),e._v(" "),a("Paging",{ref:"paging",attrs:{total:e.nowTotal},on:{getPageRequest:e.getPageRequest}}),e._v(" "),e.isOpenAddDialog?[a("AddDegradation",{on:{addDialogController:e.addDialogController,queryTask:e.queryTask}})]:e._e()],2)},staticRenderFns:[]};var ae=a("VU/8")(ee,te,!1,function(e){a("fu8h")},"data-v-11f44ea4",null).exports,le={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%","align-items":"center"},attrs:{data:e.extData,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"数据库 ID : "}},[a("span",[e._v(e._s(t.row.dbId))])]),e._v(" "),a("el-form-item",{attrs:{label:"数据库状态 : "}},[a("span",[e._v(e._s(t.row.dbStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"Top状态 : "}},[a("span",[e._v(e._s(t.row.topStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"拉单状态 : "}},[a("span",[e._v(e._s(t.row.pullStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"拉单开始时间 : "}},[a("span",[e._v(e._s(t.row.pullStartDateTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"拉单结束时间 : "}},[a("span",[e._v(e._s(t.row.pullEndDateTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"公司 ID : "}},[a("span",[e._v(e._s(t.row.corpId))])]),e._v(" "),a("el-form-item",{attrs:{label:"降级标签 : "}},[a("span",[e._v(e._s(t.row.downgradeTag))])]),e._v(" "),a("el-form-item",{attrs:{label:"创建时间 : "}},[a("span",[e._v(e._s(t.row.gmtCreate))])]),e._v(" "),a("el-form-item",{attrs:{label:"最后修改时间 : "}},[a("span",[e._v(e._s(t.row.gmtModified))])]),e._v(" "),a("el-form-item",{attrs:{label:"灰度标识 : "}},[a("span",[e._v(e._s(t.row.grayLevel))])]),e._v(" "),a("el-form-item",{attrs:{label:"表 ID : "}},[a("span",[e._v(e._s(t.row.listId))])]),e._v(" "),a("el-form-item",{attrs:{label:"打开应用程序昵称 : "}},[a("span",[e._v(e._s(t.row.openedAppNames))])]),e._v(" "),a("el-form-item",{attrs:{label:"拉单端点 : "}},[a("span",[e._v(e._s(t.row.pullEndPoint))])]),e._v(" "),a("el-form-item",{attrs:{label:"搜索库 ID : "}},[a("span",[e._v(e._s(t.row.searchdbId))])]),e._v(" "),a("el-form-item",{attrs:{label:"店铺 ID : "}},[a("span",[e._v(e._s(t.row.storeId))])]),e._v(" "),a("el-form-item",{attrs:{label:"近三个月订单数 : "}},[a("span",[e._v(e._s(t.row.topTradeCount))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家 ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数据库 ID",prop:"dbId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数据库状态",prop:"dbStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"Top状态",prop:"topStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"拉单状态",prop:"pullStatus"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"拉单开始时间",prop:"pullStartDateTime"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"拉单结束时间",prop:"pullEndDateTime"}})],1)},staticRenderFns:[]};var se={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userProductInfo,"default-expand-all":!0,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.sellerNick))])]),e._v(" "),a("el-form-item",{attrs:{label:"卖家 ID : "}},[a("span",[e._v(e._s(t.row.sellerId))])]),e._v(" "),a("el-form-item",{attrs:{label:"公司 ID : "}},[a("span",[e._v(e._s(t.row.corpId))])]),e._v(" "),a("el-form-item",{attrs:{label:"VIP等级 : "}},[a("span",[e._v(e._s(t.row.vipflag))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否主店 : "}},[a("span",[e._v(e._s(t.row.isMain))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否多店 : "}},[a("span",[e._v(e._s(t.row.isMany))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否需要授权 : "}},[a("span",[e._v(e._s(t.row.isNeedauth))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否沉默 : "}},[a("span",[e._v(e._s(t.row.isSilent))])]),e._v(" "),a("el-form-item",{attrs:{label:"上一次付费时间 : "}},[a("span",[e._v(e._s(t.row.lastPaidTime))])]),e._v(" "),a("el-form-item",{attrs:{label:"上一次活动时间 : "}},[a("span",[e._v(e._s(t.row.lastactivedt))])]),e._v(" "),a("el-form-item",{attrs:{label:"上一次更新时间 : "}},[a("span",[e._v(e._s(t.row.lastupdatetime))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单结束时间 : "}},[a("span",[e._v(e._s(t.row.orderCycleEnd))])]),e._v(" "),a("el-form-item",{attrs:{label:"最后修改时间 : "}},[a("span",[e._v(e._s(t.row.revivalDate))])]),e._v(" "),a("el-form-item",{attrs:{label:"子时间 : "}},[a("span",[e._v(e._s(t.row.subdatetime))])]),e._v(" "),a("el-form-item",{attrs:{label:"表 ID : "}},[a("span",[e._v(e._s(t.row.listId))])]),e._v(" "),a("el-form-item",{attrs:{label:"W1到期时间 : "}},[a("span",[e._v(e._s(t.row.w1Deadline))])]),e._v(" "),a("el-form-item",{attrs:{label:"访问令牌 : "}},[a("span",[e._v(e._s(t.row.accessToken))])]),e._v(" "),a("el-form-item",{attrs:{label:"刷新令牌 : "}},[a("span",[e._v(e._s(t.row.refreshToken))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"sellerNick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家ID",prop:"sellerId"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"第一次使用爱用时间",prop:"createDate"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"VIP等级",prop:"vipflag"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"是否多店",prop:"isMany"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"是否主店铺",prop:"isMain"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"w1授权到期时间",prop:"w1Deadline"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"w2授权到期时间",prop:"w2Deadline"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订购到期时间",prop:"orderCycleEnd"}})],1)},staticRenderFns:[]};var re={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userProductInfo,height:"645px"}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"卖家昵称 : "}},[a("span",[e._v(e._s(t.row.nick))])]),e._v(" "),a("el-form-item",{attrs:{label:"应用昵称 : "}},[a("span",[e._v(e._s(t.row.articleName))])]),e._v(" "),a("el-form-item",{attrs:{label:"商品昵称 : "}},[a("span",[e._v(e._s(t.row.articleItemName))])]),e._v(" "),a("el-form-item",{attrs:{label:"订单 ID : "}},[a("span",[e._v(e._s(t.row.orderId))])]),e._v(" "),a("el-form-item",{attrs:{label:"应用收费代码 : "}},[a("span",[e._v(e._s(t.row.bizOrderId))])]),e._v(" "),a("el-form-item",{attrs:{label:"创建时间 : "}},[a("span",[e._v(e._s(t.row.createDate))])]),e._v(" "),a("el-form-item",{attrs:{label:"事件名 : "}},[a("span",[e._v(e._s(t.row.eventName))])]),e._v(" "),a("el-form-item",{attrs:{label:"推广位 : "}},[a("span",[e._v(e._s(t.row.extension))])]),e._v(" "),a("el-form-item",{attrs:{label:"原价(分) : "}},[a("span",[e._v(e._s(t.row.fee))])]),e._v(" "),a("el-form-item",{attrs:{label:"来自应用 : "}},[a("span",[e._v(e._s(t.row.from))])]),e._v(" "),a("el-form-item",{attrs:{label:"优惠金额(分) : "}},[a("span",[e._v(e._s(t.row.promFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"是否为活动 : "}},[a("span",[e._v(e._s(t.row.hasAct))])]),e._v(" "),a("el-form-item",{attrs:{label:"实付金额(分) : "}},[a("span",[e._v(e._s(t.row.totalPayFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款金额(分) : "}},[a("span",[e._v(e._s(t.row.refundFee))])]),e._v(" "),a("el-form-item",{attrs:{label:"创意 ID : "}},[a("span",[e._v(e._s(t.row.openCid))])]),e._v(" "),a("el-form-item",{attrs:{label:"推广位 : "}},[a("span",[e._v(e._s(t.row.originality))])]),e._v(" "),a("el-form-item",{attrs:{label:"提示类型 : "}},[a("span",[e._v(e._s(t.row.secondaryClass))])]),e._v(" "),a("el-form-item",{attrs:{label:"一级分类 : "}},[a("span",[e._v(e._s(t.row.primaryClass))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卖家昵称",prop:"nick"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"应用昵称",prop:"articleName"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"商品模型名称",prop:"articleItemName"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订购周期",prop:"orderCycle"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订购开始时间",prop:"orderCycleStart"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订购结束时间",prop:"orderCycleEnd"}})],1)},staticRenderFns:[]};var ne={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.openUserInfo,stripe:"",height:"645px"}},[a("el-table-column",{attrs:{align:"center",prop:"openUserId",label:"开户ID"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"sellerNick",label:"用户昵称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"status",label:"开户状态"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"ruleId",label:"开通规则"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"type",label:"类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"appName",label:"应用昵称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"gmtCreate",label:"创建时间"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"gmtModify",label:"修改时间"}})],1)},staticRenderFns:[]};var oe={name:"UserInfoList",components:{Table:I,ExtTable:a("VU/8")({name:"ExtTable",data:function(){return{extData:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.extData=e}}},le,!1,function(e){a("ETGH")},"data-v-8f8cebba",null).exports,UserProductInfoTable:a("VU/8")({name:"UserProductInfoTable",data:function(){return{userProductInfo:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.userProductInfo=e}}},se,!1,function(e){a("Mgrp")},"data-v-68a4ac12",null).exports,OrderSearchTable:a("VU/8")({name:"OrderSearchTable",data:function(){return{userProductInfo:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.userProductInfo=e}}},re,!1,function(e){a("Rf2z")},"data-v-b54705e6",null).exports,OpenUserTable:a("VU/8")({name:"OpenUserTable",data:function(){return{openUserInfo:[]}},methods:{getData:function(e){null==e&&this.$message.warning("数据不存在"),null!=e[0]?this.addTableData(e):this.$message.warning("数据不存在")},addTableData:function(e){this.openUserInfo=e}}},ne,!1,function(e){a("fkVE")},"data-v-f37038e2",null).exports},props:{userInfo:{type:Object,default:null}},data:function(){return{userResultList:[]}},created:function(){this.getUserExtInfo()},methods:{getOpenUserInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/userinfo/openuserinfolog.list.get",e)).then(function(e){var a=e.data.body;t.userResultList=a,t.$refs.openUserParams.getData(a)})},getOrderSearchInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/userinfo/ordersearchinfo.list.get",e)).then(function(e){var a=e.data.body;t.userResultList=a,t.$refs.OrderSearchParams.getData(a)})},getUserProductInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/userinfo/productinfo.list.get",e)).then(function(e){var a=e.data.body;t.userResultList=a,t.$refs.userProductParams.getData(a)})},getUserExtInfo:function(){var e,t=this,a=this.userInfo;null==a?this.$message({type:"warning",message:"请输入查询参数",center:!0}):(e=a,d.post(f+"/userinfo/extinfo.list.get",e)).then(function(e){var a=e.data.body;t.userResultList=a,t.$refs.extParams.getData(a)})}}},ie={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tabs",{attrs:{type:"border-card"}},[a("el-tab-pane",[a("div",{attrs:{slot:"label"},on:{click:e.getUserExtInfo},slot:"label"},[e._v("ext用户存单状态表")]),e._v(" "),a("ExtTable",{ref:"extParams"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"user_productinfo用户信息表"}},[a("div",{attrs:{slot:"label"},on:{click:e.getUserProductInfo},slot:"label"},[e._v("user_productinfo用户信息表")]),e._v(" "),a("UserProductInfoTable",{ref:"userProductParams"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"order_search订购记录表"}},[a("div",{attrs:{slot:"label"},on:{click:e.getOrderSearchInfo},slot:"label"},[e._v("order_search订购记录表")]),e._v(" "),a("OrderSearchTable",{ref:"OrderSearchParams"})],1),e._v(" "),a("el-tab-pane",{attrs:{label:"open_user开户操作日志表"}},[a("div",{attrs:{slot:"label"},on:{click:e.getOpenUserInfo},slot:"label"},[e._v("open_user开户操作日志表")]),e._v(" "),a("OpenUserTable",{ref:"openUserParams"})],1)],1)},staticRenderFns:[]};var ce={name:"UserInquiry",components:{UserInfoList:a("VU/8")(oe,ie,!1,function(e){a("GrjQ")},"data-v-4e77f70e",null).exports,ProblemAnalysisResults:_},data:function(){return{type:"user",userInfo:{sellerNick:"",tid:"",storeId:"TAO",appName:"trade"},analysisResult:"",sonComponentName:""}},methods:{toAnalysisResult:function(){""===this.userInfo.sellerNick?this.$message({type:"warning",message:"请输入查询参数",center:!0}):this.sonComponentName="problemAnalysisResults"},toTable:function(){""===this.userInfo.sellerNick?this.$message({type:"warning",message:"请输入查询参数",center:!0}):this.sonComponentName="userInfoList"}}},ue={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.userInfo,align:"center"}},[a("el-form-item",{attrs:{label:"卖家昵称"}},[a("el-input",{attrs:{placeholder:"sellerNick"},model:{value:e.userInfo.sellerNick,callback:function(t){e.$set(e.userInfo,"sellerNick",t)},expression:"userInfo.sellerNick"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"平台ID"}},[a("el-select",{attrs:{placeholder:"平台昵称"},model:{value:e.userInfo.storeId,callback:function(t){e.$set(e.userInfo,"storeId",t)},expression:"userInfo.storeId"}},[a("el-option",{attrs:{label:"淘宝",value:"TAO"}}),e._v(" "),a("el-option",{attrs:{label:"拼多多",value:"PDD"}}),e._v(" "),a("el-option",{attrs:{label:"1688",value:"1688"}}),e._v(" "),a("el-option",{attrs:{label:"抖店",value:"DOUDIAN"}}),e._v(" "),a("el-option",{attrs:{label:"微信",value:"WXSHOP"}}),e._v(" "),a("el-option",{attrs:{label:"快手",value:"KUAISHOU"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"AppName"}},[a("el-select",{attrs:{placeholder:"产品名称"},model:{value:e.userInfo.appName,callback:function(t){e.$set(e.userInfo,"appName",t)},expression:"userInfo.appName"}},[a("el-option",{attrs:{label:"爱用交易",value:"trade"}}),e._v(" "),a("el-option",{attrs:{label:"爱用商品",value:"item"}}),e._v(" "),a("el-option",{attrs:{label:"管店",value:"guanDian"}})],1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"button",on:{click:e.toAnalysisResult}},[e._v("用户问题分析")]),e._v(" "),a("el-button",{staticClass:"button",on:{click:e.toTable}},[e._v("用户信息查询")])],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"center"}},[e._v("结果展示")]),e._v(" "),"problemAnalysisResults"===e.sonComponentName?[a("ProblemAnalysisResults",{attrs:{userInfo:this.userInfo,analyseType:e.type}})]:e._e(),e._v(" "),"userInfoList"===e.sonComponentName?[a("UserInfoList",{attrs:{userInfo:this.userInfo}})]:e._e(),e._v(" "),a("router-view")],2)},staticRenderFns:[]};var me=a("VU/8")(ce,ue,!1,function(e){a("4FRn")},"data-v-8a64ec0a",null).exports,de={name:"AdminHome",components:{Degradation:ae,Login:E,UserInquiry:me,OrderInquiry:L},created:function(){this.initHtml()},data:function(){return{sonComponentName:"",userAuth:"",isOpenLogin:!1,userName:""}},methods:{initHtml:function(){var e=void 0,t=void 0;JSON.parse(localStorage.getItem("checked"))?(e=localStorage.getItem("userAuth"),t=localStorage.getItem("userLoginInfo")):(e=sessionStorage.getItem("userAuth"),t=sessionStorage.getItem("userLoginInfo"));var a=JSON.parse(t);null!=a&&(this.userName=a.username),null!=e?this.userAuth=e:null!=t&&this.login(),this.toUserInquiry()},exitLogin:function(){localStorage.clear(),sessionStorage.clear(),this.userAuth="",this.initHtml()},login:function(){this.isOpenLogin=!this.isOpenLogin},cancelLogin:function(e){this.isOpenLogin=!this.isOpenLogin},toUserInquiry:function(){this.sonComponentName="UserInquiry"},toOrderInquiry:function(){this.sonComponentName="OrderInquiry"},toDegradationList:function(){this.sonComponentName="Degradation"}}},fe={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticStyle:{height:"100%",border:"1px solid #eee"}},["1"==e.userAuth?[a("el-aside",{staticStyle:{"background-color":"rgb(84,92,100)"},attrs:{width:"250px"}},[a("el-menu",{staticClass:"left-menu",attrs:{"default-openeds":["1","3"],"default-active":"1-1","background-color":"#545c64","text-color":"#fff","active-text-color":"#ffd04b"}},[a("el-submenu",{attrs:{index:"1"}},[a("template",{slot:"title"},[a("i",{staticClass:"el-icon-s-order"}),e._v(" "),a("span",[e._v("用户问题查询")])]),e._v(" "),a("el-menu-item-group",[a("el-menu-item",{attrs:{index:"1-1"},on:{click:e.toUserInquiry}},[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",{attrs:{slot:"title"},slot:"title"},[e._v("\r\n                用户信息查询\r\n              ")])]),e._v(" "),a("el-menu-item",{attrs:{index:"1-2"},on:{click:e.toOrderInquiry}},[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",{attrs:{slot:"title"},slot:"title"},[e._v("\r\n                用户订单查询查询\r\n              ")])])],1)],2),e._v(" "),a("el-menu-item",{attrs:{index:"2"},on:{click:e.toDegradationList}},[a("i",{staticClass:"el-icon-menu"}),e._v(" "),a("span",{attrs:{slot:"title"},slot:"title"},[e._v("大促降级管理")])])],1)],1)]:[a("el-aside",{staticStyle:{"background-color":"rgb(84,92,100)"},attrs:{width:"250px"}},[a("el-menu",{staticClass:"left-menu",attrs:{"default-openeds":["1","3"],"default-active":"2","background-color":"#545c64","text-color":"#fff","active-text-color":"#ffd04b"}},[a("el-submenu",{attrs:{index:"1"}},[a("template",{slot:"title"},[a("i",{staticClass:"el-icon-s-order"}),e._v(" "),a("span",[e._v("用户问题查询")])]),e._v(" "),a("el-menu-item-group",[a("el-menu-item",{attrs:{index:"1-1"},on:{click:e.toUserInquiry}},[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",{attrs:{slot:"title"},slot:"title"},[e._v("\r\n                用户信息查询\r\n              ")])]),e._v(" "),a("el-menu-item",{attrs:{index:"1-2"},on:{click:e.toOrderInquiry}},[a("i",{staticClass:"el-icon-search"}),e._v(" "),a("span",{attrs:{slot:"title"},slot:"title"},[e._v("\r\n                用户订单查询查询\r\n              ")])])],1)],2)],1)],1)],e._v(" "),a("el-container",[a("el-header",{staticStyle:{"text-align":"right","font-size":"15px"}},[a("el-row",[a("el-col",{attrs:{span:13}},[a("div",{staticClass:"grid-content bg-purple-dark"},[a("span",{staticStyle:{"font-size":"25px",color:"white","text-align":"center"}},[e._v("中台运维系统")])])]),e._v(" "),a("el-col",{attrs:{span:11}},[a("el-dropdown",{staticStyle:{"text-align":"left"}},[a("i",{staticClass:"el-icon-setting",staticStyle:{"margin-right":"15px"}}),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[""!=e.userAuth?[a("el-dropdown-item",{nativeOn:{click:function(t){return e.exitLogin.apply(null,arguments)}}},[e._v("\r\n                    退出\r\n                  ")])]:[a("el-dropdown-item",{nativeOn:{click:function(t){return e.login.apply(null,arguments)}}},[e._v("\r\n                    登录\r\n                  ")])]],2)],1),e._v(" "),""!=e.userAuth?[a("span",[e._v(e._s(e.userName))])]:[a("span",[e._v("爱用科技")])]],2)],1)],1),e._v(" "),e.isOpenLogin?a("login",{on:{cancelLogin:e.cancelLogin,initHtml:e.initHtml}}):e._e(),e._v(" "),a("el-main",["UserInquiry"===e.sonComponentName?[a("UserInquiry")]:e._e(),e._v(" "),"OrderInquiry"===e.sonComponentName?[a("OrderInquiry")]:e._e(),e._v(" "),"Degradation"===e.sonComponentName?[a("Degradation")]:e._e()],2)],1)],2)},staticRenderFns:[]};var pe=a("VU/8")(de,fe,!1,function(e){a("vkr3")},"data-v-c36cf8c4",null).exports,ve={name:"App",components:{AdminHome:pe},data:function(){return{showUserPage:!0,userAuth:""}},created:function(){},methods:{getAuth:function(e){console.log(e),this.userAuth=e},goAdmin:function(){this.showUserPage=!1,this.$router.push({name:"AdminHome"})}}},_e={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{attrs:{id:"app"}},[t("AdminHome"),this._v(" "),t("router-view")],1)},staticRenderFns:[]};var be=a("VU/8")(ve,_e,!1,function(e){a("u1hV")},null,null).exports,ge=a("aLYK"),he=(a("tvR6"),a("/ocq"));l.default.use(he.a);var we=new he.a({mode:"history",routes:[{path:"/admin",name:"AdminHome",component:pe,children:[{path:"userInquiry",name:"UserInquiry",component:me},{path:"orderInquiry",name:"OrderInquiry",component:L}]}]});l.default.use(ge.a,o.a),l.default.use(c.a),l.default.config.productionTip=!1,new l.default({el:"#app",router:we,template:"<App/>",render:function(e){return e(be)}}),o.a.defaults.withCredentials=!0},Qd2w:function(e,t){},RMmM:function(e,t){},Rf2z:function(e,t){},W1gd:function(e,t){},fkVE:function(e,t){},fu8h:function(e,t){},gx1J:function(e,t){},i7fE:function(e,t){},jvwQ:function(e,t){},tvR6:function(e,t){},u1hV:function(e,t){},vkr3:function(e,t){}},["NHnr"]);
//# sourceMappingURL=app.5c4191669240bce4e4a9.js.map