## apollo \u670D\u52A1\u5668meta\u5730\u5740
apollo.meta=http://************:18080

orders.monitor.service.url = http://localhost:18084
orders.monitor.service.name = \u8BA2\u5355service

## \u662F\u5426\u542F\u7528\u76D1\u63A7\u4EFB\u52A1
orders.monitor.scheduling.enable = false
## \u662F\u5426\u542F\u7528\u544A\u8B66
orders.monitor.alarm.enable = false
## \u53D6\u6837\u65F6\u6BCF\u5F20\u8868\u7684limit\u9650\u5236
orders.monitor.query.limit = 500
## \u76D1\u63A7\u7EBF\u7A0B\u6C60\u5927\u5C0F
orders.monitor.thread-pool.size = 20

## api\u6821\u9A8C\u9650\u6D41
orders.monitor.api.check.limit = 50

## \u662F\u5426\u542F\u7528\u81EA\u52A8\u5173\u95ED\u8BA2\u5355\u5BF9\u5916\u670D\u52A1\u7684\u529F\u80FD
orders.monitor.stopOrder.enable = false


## fullinfo\u76D1\u63A7\u8BA1\u5212
orders.monitor.fullinfo.cron = 0 20 0/2 * * ?
## \u8BA2\u5355\u6570\u91CF\u76D1\u63A7\u8BA1\u5212
orders.monitor.soldGet.cron = 0 50 1/2 * * ?
## rds\u63A8\u9001\u76D1\u63A7\u8BA1\u5212
orders.monitor.rds.cron = 0 0/10 * * * ?
## ons\u5806\u79EF\u76D1\u63A7\u8BA1\u5212
orders.monitor.ons.cron = 3/10 * * * * ?
## \u8BA2\u5355\u5EF6\u65F6\u76D1\u63A7\u8BA1\u5212
orders.monitor.orderDelay.cron = 0 5/10 * * * ?

## fulinfo
# \u544A\u8B66\u9608\u503C, \u6210\u529F\u7387\u4F4E\u4E8E\u9608\u503C\u544A\u8B66
orders.monitor.fullinfo.threshold = 0.999
# \u4E0D\u9700\u8981\u6BD4\u8F83\u7684\u5B57\u6BB5
orders.monitor.fullinfo.ignores = buyer_email,receiver_name,receiver_address,buyer_nick,receiver_phone,receiver_mobile,request_id,you_xiang,num_iid,buyer_open_uid,is_daixiao,nr_outer_iid,divide_order_fee,part_mjz_discount,service_tags
# \u8BF7\u6C42\u7684fields
orders.monitor.fullinfo.fields = seller_nick,tid,pic_path,payment,seller_rate,post_fee,receiver_name,receiver_state,receiver_address,receiver_zip,receiver_mobile,receiver_phone,consign_time,received_payment,promotion_details,est_con_time,invoice_kind,receiver_country,receiver_town,order_tax_fee,paid_coupon_fee,shop_pick,num,num_iid,status,title,type,price,discount_fee,has_post_fee,total_fee,created,pay_time,modified,end_time,buyer_message,buyer_memo,buyer_flag,seller_memo,seller_flag,invoice_name,invoice_type,buyer_nick,trade_attr,credit_card_fee,step_trade_status,step_paid_fee,mark_desc,shipping_type,buyer_cod_fee,adjust_fee,trade_from,service_orders,buyer_rate,receiver_city,receiver_district,service_tags,orders,trade_ext,logistics_infos
# \u9A8C\u8BC1\u8BA2\u5355\u7684\u65F6\u95F4\u8303\u56F4\u4E3AtimeRange\u5230\u5F53\u524D\u65F6\u95F4\u4E4B\u95F4 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.fullinfo.timeRange = 1H
# \u8981\u67E5\u8BE2\u8BA2\u5355\u7684\u8BA2\u5355\u72B6\u6001
orders.monitor.fullinfo.status = ALL
# \u9A8C\u8BC1\u8BA2\u5355\u7684\u6570\u91CF\u4E0A\u9650
orders.monitor.fullinfo.number = 10

## \u7528\u6237\u8BA2\u5355\u6570\u91CF
# \u544A\u8B66\u9608\u503C, \u6210\u529F\u7387\u4F4E\u4E8E\u9608\u503C\u544A\u8B66
orders.monitor.soldGet.threshold = 0.999
# \u9A8C\u8BC1\u8BA2\u5355\u7684\u65F6\u95F4\u8303\u56F4\u4E3AtimeRange\u5230\u5F53\u524D\u65F6\u95F4\u4E4B\u95F4 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.soldGet.timeRange = 1H
# \u8981\u9A8C\u8BC1\u6570\u91CF\u7684\u8BA2\u5355\u7C7B\u578B
orders.monitor.soldGet.status = WAIT_SELLER_SEND_GOODS
# \u9A8C\u8BC1\u7528\u6237\u7684\u6570\u91CF\u4E0A\u9650
orders.monitor.soldGet.number = 2000
# soldget\u8BF7\u6C42\u7684fields
orders.monitor.soldGet.fields = tid
# soldget\u8BF7\u6C42\u7684pageSize
orders.monitor.soldGet.pageSize = 50

#\u5F85\u53D1\u8D27
orders.monitor.soldGet.pdd.status = 1
#\u552E\u540E\u72B6\u6001 5 \u5168\u90E8
orders.monitor.soldGet.pdd.refund.status = 5
#\u8FD4\u56DE\u9875\u7801
orders.monitor.soldGet.pdd.pageNo = 1
#\u8FD4\u56DE\u6570\u91CF
orders.monitor.soldGet.pdd.pageSize = 100

## rds\u63A8\u9001
# \u544A\u8B66\u9608\u503C, \u63A8\u9001\u91CF\u4F4E\u4E8E\u9608\u503C\u544A\u8B66
orders.monitor.rds.countThreshold = 1000
# \u544A\u8B66\u9608\u503C, \u5EF6\u65F6\u6821\u9A8C\u6210\u529F\u7387\u4F4E\u4E8E\u9608\u503C\u544A\u8B66
orders.monitor.rds.delayThreshold = 0.999
# \u9A8C\u8BC1rds\u63A8\u9001\u7684\u65F6\u95F4\u8303\u56F4\u4E3AtimeRange\u5230\u5F53\u524D\u65F6\u95F4\u4E4B\u95F4 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.rds.timeRange = 10M
# \u5EF6\u65F6\u8D85\u65F6\u65F6\u95F4,\u8D85\u8FC7\u6B64\u65F6\u95F4\u7B97\u8D85\u65F6 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.rds.delayTime = 30S
# \u5EF6\u65F6\u8D85\u65F6\u68C0\u9A8C\u9700\u8981\u68C0\u9A8C\u7684\u6700\u5927\u8BA2\u5355\u6570
orders.monitor.rds.delayNumber = 100


## \u8BA2\u5355\u5EF6\u65F6
# \u544A\u8B66\u9608\u503C, \u672A\u8D85\u65F6\u6210\u529F\u7387\u4F4E\u4E8E\u9608\u503C\u544A\u8B66
orders.monitor.orderDelay.threshold = 0.7
# \u9A8C\u8BC1\u8BA2\u5355\u7684\u65F6\u95F4\u8303\u56F4\u4E3AtimeRange\u5230\u5F53\u524D\u65F6\u95F4\u4E4B\u95F4 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.orderDelay.timeRange = 10M
# \u8981\u9A8C\u8BC1\u7684\u8BA2\u5355\u7C7B\u578B
orders.monitor.orderDelay.status = ALL
# \u8BA2\u5355\u8D85\u65F6\u65F6\u95F4 (\u6700\u5C0F\u5355\u4F4D\u79D2)
orders.monitor.orderDelay.timeout = 10S
# \u8BA2\u5355\u5EF6\u65F6\u6821\u9A8C\u6700\u5C0F\u6837\u672C\u6570, \u4F4E\u4E8E\u6B64\u6570\u91CF\u4E0D\u6821\u9A8C
orders.monitor.orderDelay.minQuantity = 10

##\u8BA2\u5355\u5E93-old-search
#loveapp.datasources.orderOldSearchDataSource.url = ******************************************
#loveapp.datasources.orderOldSearchDataSource.username = aiyong
#loveapp.datasources.orderOldSearchDataSource.password = Zx891Ajf
#loveapp.datasources.orderOldSearchDataSource.driverClassName = org.postgresql.Driver
#loveapp.datasources.orderOldSearchDataSource.min-idle = 1
#loveapp.datasources.orderOldSearchDataSource.initial-size = 1
#loveapp.datasources.orderOldSearchDataSource.max-active = 50
#loveapp.datasources.orderOldSearchDataSource.max-wait = 60000
#loveapp.datasources.orderOldSearchDataSource.name = orderOldSearchDataSource

#loveapp.datasources.orderSearchDataSource2.url = ******************************************
#loveapp.datasources.orderSearchDataSource2.username = aiyong
#loveapp.datasources.orderSearchDataSource2.password = Zx891Ajf
#loveapp.datasources.orderSearchDataSource2.driverClassName = org.postgresql.Driver
#loveapp.datasources.orderSearchDataSource2.min-idle = 0
#loveapp.datasources.orderSearchDataSource2.initial-size = 1
#loveapp.datasources.orderSearchDataSource2.max-active = 10
#loveapp.datasources.orderSearchDataSource2.max-wait = 60000
#loveapp.datasources.orderSearchDataSource2.name = orderOldSearchDataSource2

## custom mybatis
loveapp.mybatis.rds.mapper-package = cn.loveapp.orders.monitor.dao.rds
loveapp.mybatis.rds.datasource = rdsDataSource
loveapp.mybatis.dream.mapper-package = cn.loveapp.orders.monitor.dao.dream
loveapp.mybatis.dream.datasource = dreamDataSource
#loveapp.mybatis.oldSearch.mapper-package = cn.loveapp.orders.monitor.dao.old
#loveapp.mybatis.oldSearch.datasource = orderOldSearchDataSource
#loveapp.mybatis.batchSearch.mapper-package = cn.loveapp.orders.monitor.dao.batch
#loveapp.mybatis.batchSearch.datasource = orderSearchDataSource

## spring scheduling
spring.task.scheduling.thread-name-prefix = spring-scheduling-
spring.task.scheduling.pool.size = 5

## \u90AE\u4EF6
#orders.mail.to=${spring.mail.username}
#orders.mail.from=${spring.mail.username}

#spring.mail.default-encoding=utf-8
#spring.mail.host=smtp.mxhichina.com
#spring.mail.port=465
#spring.mail.username=XXXXXXX.XXXX
#spring.mail.password=XXXXX
#spring.mail.properties.mail.smtp.connectiontimeout=10000
#spring.mail.properties.mail.smtp.timeout=10000
#spring.mail.properties.mail.smtp.writetimeout=10000
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true
#spring.mail.properties.mail.smtp.starttls.required=true
#spring.mail.properties.mail.smtp.ssl.enable=true
#spring.mail.properties.mail.imap.ssl.socketFactory.fallback=false
#spring.mail.properties.mail.smtp.ssl.socketFactory.class=com.sun.mail.util.MailSSLSocketFactory

