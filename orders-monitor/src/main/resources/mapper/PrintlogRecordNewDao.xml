<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.print.PrintlogRecordNewDao">

    <resultMap type="cn.loveapp.orders.monitor.entity.PrintlogRecordNew" id="PrintlogRecordNewMap">
		<result property="id" column="id"/>
		<result property="sellernick" column="sellernick"/>
		<result property="tid" column="tid"/>
		<result property="voice" column="voice"/>
		<result property="operatetime" column="Operatetime"/>
		<result property="delivercompany" column="Delivercompany"/>
		<result property="courier" column="courier"/>
		<result property="invoice" column="invoice"/>
		<result property="surface" column="surface"/>
    </resultMap>

	<!--查询指定行数据-->
	<select id="queryAllByLimit" resultMap="PrintlogRecordNewMap">
		SELECT a.id, a.sellernick, a.tid, a.Operatetime, a.Delivercompany, a.voice, a.Elefacestatus, a.Expfacestatus, b.courier, b.invoice, b.surface
			FROM printlog_record_new AS a
			LEFT JOIN  print_histroy AS b
			ON a.tid = b.tid AND a.sellernick=b.sellernick
			<where>
				<if test="sellerNick != null and sellerNick != ''">
					and a.sellernick = #{sellerNick}
				</if>
				AND Operatetime >= #{startTime} AND Operatetime &lt; #{endTime} ORDER BY Operatetime LIMIT #{limit} OFFSET #{offset}
			</where>
	</select>

</mapper>
