<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.dream.PromotionActivityTradeDao">
	<resultMap type="cn.loveapp.orders.monitor.entity.PromotionActivityTrade" id="PromotionActivityTradeMap">
		<!-- <result property="id" column="id" jdbcType="INTEGER"/> -->
		<result property="id" column="id"/>
		<!-- <result property="sellernick" column="sellernick" jdbcType="VARCHAR"/> -->
		<result property="sellernick" column="sellernick"/>
		<!-- <result property="actCycle" column="act_cycle" jdbcType="INTEGER"/> -->
		<result property="actCycle" column="act_cycle"/>
		<!-- <result property="optime" column="optime" jdbcType="TIMESTAMP"/> -->
		<result property="optime" column="optime"/>
		<!-- <result property="actflag" column="actflag" jdbcType="VARCHAR"/> -->
		<result property="actflag" column="actflag"/>
		<!-- <result property="sender" column="sender" jdbcType="VARCHAR"/> -->
		<result property="sender" column="sender"/>
		<!-- <result property="promotionCode" column="promotion_code" jdbcType="VARCHAR"/> -->
		<result property="promotionCode" column="promotion_code"/>
		<!-- <result property="isused" column="isused" jdbcType="INTEGER"/> -->
		<result property="isused" column="isused"/>
	</resultMap>

	<!--查找指定用户最新数据-->
	<select id="queryBySellerNick" resultType="cn.loveapp.orders.monitor.entity.PromotionActivityTrade">
		select id,
			   sellernick,
			   act_cycle,
			   optime,
			   actflag,
			   sender,
			   promotion_code,
			   isused
		from lacrm.promotion_activity_trade
		where sellerNick = #{sellerNick}
		order by optime desc limit 1
	</select>

</mapper>
