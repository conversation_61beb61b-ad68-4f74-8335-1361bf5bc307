<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.dream.UserOrderMigrateProgressDao">

	<resultMap type="cn.loveapp.orders.common.entity.UserOrderMigrateProgress" id="UserOrderMigrateProgressMap">
		<result property="id" column="id"/>
		<result property="sellerId" column="seller_id"/>
		<result property="sellerNick" column="seller_nick"/>
		<result property="fromDbId" column="from_db_id"/>
		<result property="fromSearchDbId" column="from_searchdb_id"/>
		<result property="toDbId" column="to_db_id"/>
		<result property="orderTotal" column="order_total"/>
		<result property="subOrderTotal" column="sub_order_total"/>
		<result property="dbOrderTotal" column="db_order_total"/>
		<result property="progress" column="progress"/>
		<result property="gmtCreate" column="gmt_create"/>
		<result property="gmtModified" column="gmt_modified"/>
	</resultMap>

	<sql id="tablename">
		user_order_migrate_progress
	</sql>

	<insert id="migrateOrderInsert">
		insert into user_order_migrate_progress(`seller_id`, `seller_nick`, `order_total`, `sub_order_total`, `db_order_total`,
												`from_db_id`, `from_searchdb_id`, `to_db_id`, `gmt_create`, `gmt_modified`)
		values (#{sellerId}, #{sellerNick}, #{orderTotal}, #{subOrderTotal}, #{dbOrderTotal}, #{fromDbId}, #{fromSearchDbId}, #{toDbId}, #{gmtCreate},
				#{gmtModified})
	</insert>

	<update id="migrateOrderUpdate">
		update user_order_migrate_progress
		<trim prefix="SET" suffixOverrides=",">
			<if test="orderTotal != null">
				order_total = #{orderTotal},
			</if>
			<if test="subOrderTotal != null">
				sub_order_total = #{subOrderTotal},
			</if>
			<if test="dbOrderTotal != null">
				db_order_total = #{dbOrderTotal},
			</if>
			<if test="fromDbId != null">
				from_db_id = #{fromDbId},
			</if>
			<if test="fromSearchDbId != null">
				from_searchdb_id = #{fromSearchDbId},
			</if>
			<if test="toDbId != null">
				to_db_id = #{toDbId},
			</if>
			<if test="progress != null and progress != ''">
				progress = #{progress},
			</if>
			<if test="gmtModified != null">
				gmt_modified = #{gmtModified},
			</if>
		</trim>
		where seller_id = #{sellerId}
	</update>

	<update id="updateToDbId">
		update user_order_migrate_progress
		<trim prefix="SET" suffixOverrides=",">
			<if test="toDbId != null">
				to_db_id = #{toDbId},
			</if>
			<if test="gmtModified != null">
				gmt_modified = #{gmtModified},
			</if>
		</trim>
		where seller_id = #{sellerId}
	</update>

	<update id="updateProgress">
		update user_order_migrate_progress
		<trim prefix="SET" suffixOverrides=",">
			progress = #{progress}
		</trim>
		where seller_id = #{sellerId}
	</update>

	<select id="queryBySellerId" resultMap="UserOrderMigrateProgressMap">
		select `seller_id`, `seller_nick`, `order_total`, `sub_order_total`, `db_order_total`, `from_db_id`, `from_searchdb_id`, `to_db_id`
		from user_order_migrate_progress
		where seller_id = #{sellerId}
	</select>

	<select id="queryByGroupByDbId" resultMap="UserOrderMigrateProgressMap">
		select sum(order_total) as order_total, `from_db_id`
		from user_order_migrate_progress
		group by from_db_id
		order by from_db_id
	</select>

	<select id="queryBySumOrderTotal" resultType="long">
		select sum(order_total) as order_total_sum
		from user_order_migrate_progress
	</select>


	<select id="queryByDbId" resultMap="UserOrderMigrateProgressMap">
		select `id`, `seller_id`, `seller_nick`, `order_total`, `sub_order_total`, `db_order_total`, `from_db_id`, `from_searchdb_id`, `to_db_id`
		from user_order_migrate_progress
		where from_db_id = #{fromDbId}
		  and to_db_id is null
		order by order_total desc
	</select>

	<select id="queryAll" resultMap="UserOrderMigrateProgressMap">
		select `id`,
		`seller_id`,
		`seller_nick`,
		`order_total`,
		`sub_order_total`,
		`db_order_total`,
		`from_db_id`,
	    `from_searchdb_id`,
		`to_db_id`,
		`progress`
		from user_order_migrate_progress
		limit #{limit} offset #{offset};
	</select>

	<select id="queryAllNeedMigrate" resultMap="UserOrderMigrateProgressMap">
		select `id`,
			   `seller_id`,
			   `seller_nick`,
			   `order_total`,
			   `sub_order_total`,
			   `db_order_total`,
			   `from_db_id`,
			   `from_searchdb_id`,
			   `to_db_id`,
			   `progress`
		from user_order_migrate_progress
		<where>
			from_db_id != to_db_id
		<if test="toDbId != null">
			and to_db_id = #{toDbId}
		</if>
			and (progress != 'end' or progress is null)
			and id > #{lastId}
		</where>
		ORDER BY id
		limit #{limit};
	</select>

</mapper>
