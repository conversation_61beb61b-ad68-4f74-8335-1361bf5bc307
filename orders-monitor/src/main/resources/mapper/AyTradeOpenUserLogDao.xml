<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.dream.AyTradeOpenUserLogDao">

	<resultMap type="cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog" id="JdpTbTradeMap">
		<result property="id" column="id"/>
		<result property="openUserId" column="open_user_id"/>
		<result property="sellerNick" column="seller_nick"/>
		<result property="status" column="status"/>
		<result property="type" column="type"/>
		<result property="ruleId" column="rule_id"/>
		<result property="reason" column="reason"/>
		<result property="remark" column="remark"/>
		<result property="gmtCreate" column="gmt_create"/>
		<result property="gmtModify" column="gmt_modify"/>
		<result property="appName" column="app_name"/>
	</resultMap>

	<sql id="tableName">
		ay_trade_open_user_log
	</sql>
	<sql id="fileds">
		id
		,open_user_id,seller_nick,status,`type`,rule_id,reason,remark,gmt_create,gmt_modify,app_name
	</sql>
	<select id="getAllBySellerNick" resultType="cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog">
		select
		<include refid="fileds"></include>
		from
		<include refid="tableName"/>
		where seller_nick = #{sellerNick}
		order by gmt_create desc
	</select>
</mapper>
