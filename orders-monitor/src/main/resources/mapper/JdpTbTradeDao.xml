<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.rds.MonitorJdpTbTradeDao">

	<resultMap type="cn.loveapp.orders.common.entity.JdpTbTrade" id="JdpTbTradeMap">
		<result property="tid" column="tid"/>
		<result property="status" column="status"/>
		<result property="type" column="type"/>
		<result property="sellerNick" column="seller_nick"/>
		<result property="buyerNick" column="buyer_nick"/>
		<result property="created" column="created"/>
		<result property="modified" column="modified"/>
		<result property="jdpHashcode" column="jdp_hashcode"/>
		<result property="jdpResponse" column="jdp_response"/>
		<result property="jdpCreated" column="jdp_created"/>
		<result property="jdpModified" column="jdp_modified"/>
	</resultMap>

	<select id="queryByJdpModified" resultMap="JdpTbTradeMap">
		SELECT seller_nick, jdp_modified, modified, tid from jdp_tb_trade
		where jdp_modified &gt;= #{minJdpModified} and jdp_modified &lt;= #{maxJdpModified} ORDER BY jdp_modified limit #{page.pageSize} offset #{page.offset}
	</select>

	<select id="queryByTid" resultMap="JdpTbTradeMap">
		SELECT jdp_modified, modified from jdp_tb_trade
		where tid = #{tid}
	</select>

	<!--新增所有列-->
	<insert id="insert" keyProperty="tid" useGeneratedKeys="false">
		insert into jdp_tb_trade(tid, status, type, seller_nick, buyer_nick, created, modified, jdp_hashcode, jdp_response, jdp_created, jdp_modified)
		values (#{tid}, #{status}, #{type}, #{sellerNick}, #{buyerNick}, #{created}, #{modified}, #{jdpHashcode}, #{jdpResponse}, #{jdpCreated}, #{jdpModified})
		ON DUPLICATE KEY UPDATE tid=#{tid},
		                        status=#{status},
								type=#{type},
								seller_nick=#{sellerNick},
								buyer_nick=#{buyerNick},
								created=#{created},
								modified=#{modified},
								jdp_hashcode=#{jdpHashcode},
								jdp_response=#{jdpResponse},
								jdp_created=#{jdpCreated},
								jdp_modified=#{jdpModified};
	</insert>

</mapper>
