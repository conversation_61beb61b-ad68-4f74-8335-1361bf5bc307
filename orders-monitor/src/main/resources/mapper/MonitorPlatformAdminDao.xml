<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.orders.monitor.dao.dream.MonitorPlatformAdminDao">

	<resultMap type="cn.loveapp.orders.monitor.entity.operations.MonitorPlatformAdmin" id="MonitorPlatformAdminMap">
		<result property="username" column="username" jdbcType="VARCHAR"/>
		<result property="password" column="password" jdbcType="VARCHAR"/>
		<result property="userLevel" column="user_level" jdbcType="INTEGER"/>
		<result property="enable" column="enable" jdbcType="INTEGER"/>
	</resultMap>

    <sql id="tableName">
        monitor_platform_admin
    </sql>
	<!--查询单个-->
	<select id="queryByUsername" resultMap="MonitorPlatformAdminMap">
		select username,
			   password,
               user_level,
			   enable
		from <include refid="tableName"></include>
		where username = #{username}
	</select>


</mapper>

