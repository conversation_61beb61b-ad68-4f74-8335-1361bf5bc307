spring.application.name=orders-monitor
spring.profiles.active=dev

server.port=8082

# \u662F\u5426\u5141\u8BB8apollo
loveapp.apollo.enabled=true
# apollo \u57FA\u7840\u914D\u7F6E
app.id=cn.loveapp.trade
apollo.bootstrap.enabled = ${loveapp.apollo.enabled}
# \u516C\u5171namespace\u5FC5\u987B\u653E\u540E\u9762
apollo.bootstrap.namespaces=orders-monitor,orders-tmc,orders-push-1,application,service-registry
env=${spring.profiles.active}

loveapp.logging.web.enable=false

spring.cache.jcache.config=classpath:ehcache.xml

# \u6307\u6807\u76D1\u63A7
management.server.port=8455
management.metrics.tags.application=${spring.application.name}
management.endpoints.web.exposure.include=prometheus,loggers

management.metrics.export.logging.enabled = true
management.metrics.export.logging.step = 15s

spring.mvc.static-path-pattern = /**
