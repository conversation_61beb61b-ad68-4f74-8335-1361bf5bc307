package cn.loveapp.orders.monitor.migrate;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.mongo.TcOrderSellerIdIndex;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dao.mongo.MonitorTcOrderSellerIdIndexDao;
import com.google.common.util.concurrent.RateLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.util.DoubleFormat;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.SearchHit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2022/7/26 10:53 AM
 **/
@Component
public class OrderMongoIndexRenew implements ApplicationRunner, ApplicationListener<ContextClosedEvent>, Ordered {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderMongoIndexRenew.class);

    private String NAME = "Mongo索引同步，";

    private final static String KEY_LAST_TIME = "order:renewOpenUid:mongoIndex:lastTime";

    @Value("${order.renew.mongoIndex.auto-stop.enable:false}")
    private volatile boolean enableAutoStop;

    @Value("${order.renew.mongoIndex.auto-stop.beginTime:-1}")
    private int beginEnableTime;

    @Value("${order.renew.mongoIndex.auto-stop.endTime:1000}")
    private int endEnableTime;

    /**
     * 迁移ES中数据的起始修改时间
     */
    @Value("${order.renew.mongoIndex.startModifiedTime:}")
    private String startModifiedTime;

    /**
     * 迁移ES中 时间增量大小(秒)
     */
    @Value("${order.renew.mongoIndex.stepSize:1}")
    private int stepSize;

    @Value("${order.renew.mongoIndex.pageSize:5}")
    private int queryPageSize;

    @Value("${order.renew.mongoIndex.limit:5}")
    private void listenLimit(double limit) {
        rateLimiter.setRate(limit);
    }

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MonitorAyTradeSearchESDao esDao;

    @Autowired
    private MonitorTcOrderSellerIdIndexDao tcOrderSellerIdIndexDao;

    private RateLimiter rateLimiter = RateLimiter.create(Double.MAX_VALUE);

    /**
     * 是否已停止
     */
    private volatile boolean stopped;

    /**
     * 是否已开始
     */
    private volatile boolean started;

    /**
     * 上次同步的时间
     */
    private LocalDateTime lastModifiedTime;

    private volatile long speed;
    private Timer timer;

    public OrderMongoIndexRenew(MeterRegistry registry) {
        timer = registry.timer("migrate_mongoindex_timer");
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!args.containsOption("orders.renew.mongo.index")) {
            return;
        }
        CompletableFuture.runAsync(this::start);
    }

    private void start() {
        if (started) {
            LOGGER.logInfo(NAME + "忽略重复运行");
            return;
        }
        started = true;
        while (true) {
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;
            try {
                if (stopped) {
                    LOGGER.logInfo(NAME + "迁移暂停, startTime=" + startTime + ", endTime=" + endTime);
                    TimeUnit.MINUTES.sleep(1);
                    continue;
                }
                startTime = loadLastTime();
                endTime = startTime.plusSeconds(stepSize);
                int page = 0;
                int pageSize = queryPageSize;
                boolean isEnd;
                do {
                    Timer.Sample sample = Timer.start();
                    List<SearchHit> searchHits =
                        esDao.queryWaitSellerSendGoodsByGmtModified(startTime, endTime, page, pageSize);
                    List<TcOrderSellerIdIndex> tcOrderSellerIdIndices = new ArrayList<>();
                    for (SearchHit searchHit : searchHits) {
                        Map<String, Object> sourceAsMap = searchHit.getSourceAsMap();
                        if (Objects.isNull(sourceAsMap)) {
                            continue;
                        }
                        if (Objects.isNull(sourceAsMap.get("id")) || Objects.isNull(sourceAsMap.get("sellerId"))
                            || Objects.isNull(sourceAsMap.get("buyerOpenUid"))) {
                            continue;
                        }
                        String id = String.valueOf(sourceAsMap.get("id"));
                        String sellerId = String.valueOf(sourceAsMap.get("sellerId"));
                        String buyerOpenUid = String.valueOf(sourceAsMap.get("buyerOpenUid"));
                        if (!StringUtils.isAnyEmpty(id, sellerId, buyerOpenUid)) {
                            TcOrderSellerIdIndex tcOrderSellerIdIndex = new TcOrderSellerIdIndex();
                            tcOrderSellerIdIndex.setId(id);
                            tcOrderSellerIdIndex.setBuyerOpenUid(buyerOpenUid);
                            tcOrderSellerIdIndex.setSellerId(sellerId);
                            tcOrderSellerIdIndices.add(tcOrderSellerIdIndex);
                        }
                    }
                    int size = tcOrderSellerIdIndexDao.updateBuyerOpenUidById(tcOrderSellerIdIndices);
                    if (size > 0){
                        LOGGER.logInfo("更新：" + size + "条");
                    }
                    isEnd = size < queryPageSize;
                    page++;
                    speed += size;
                    if (size > 0) {
                        rateLimiter.acquire(size);
                    }
                } while (!isEnd);
                saveLastTime(endTime);
            } catch (Exception e) {
                LOGGER.logError(NAME + "startTime=" + startTime + ", endTime=" + endTime + ": " + e.getMessage(), e);
            }

        }
    }

    private void saveLastTime(LocalDateTime lastTime) {
        LocalDateTime now = LocalDateTime.now();
        if (!lastTime.isBefore(now)) {
            lastTime = now.withNano(0).minusSeconds(10);
            LOGGER.logInfo(NAME + "保存进度, 已超过当前时间, 回退几秒: " + lastTime + ", stepSize=" + stepSize);
        }
        lastModifiedTime = lastTime;
        stringRedisTemplate.opsForValue().set(KEY_LAST_TIME, lastTime.toString(), 1, TimeUnit.DAYS);
    }

    private LocalDateTime loadLastTime() {
        LocalDateTime lastTime = lastModifiedTime;
        if (lastTime != null) {
            return lastTime;
        }
        String lastTimeStr = stringRedisTemplate.opsForValue().get(KEY_LAST_TIME);
        if (StringUtils.isNotEmpty(lastTimeStr)) {
            lastTime = LocalDateTime.parse(lastTimeStr);
        } else {
            LocalDateTime time = LocalDateTime.now().minusDays(155L);
            lastTime = StringUtils.isNotEmpty(startModifiedTime) ? LocalDateTime.parse(startModifiedTime)
                : time.withSecond(0).withNano(0).minusMinutes(1);
        }
        if (lastModifiedTime == null) {
            lastModifiedTime = lastTime;
        }
        return lastTime;
    }

    /**
     * 周期性打印日志
     */
    @Scheduled(cron = "${order.renew.mongoIndex.auto-stop.cron: 0 * * * * ?}")
    public void checkJob() {
        if (!started || stopped || !enableAutoStop) {
            return;
        }
        stopped = !checkRunTime();
    }

    /**
     * 检查运行时间
     *
     * @return
     */
    private boolean checkRunTime() {
        int hour = LocalTime.now().getHour();
        if (hour < beginEnableTime || hour >= endEnableTime) {
            LOGGER.logInfo(NAME + "错误的任务时间, 当前时间: " + LocalDateTime.now() + " beginTime:" + beginEnableTime
                + " endTime:" + endEnableTime);
            return false;
        }
        return true;
    }

    @Scheduled(fixedDelay = 10000)
    public void cronLog() {
        if (!started || stopped) {
            return;
        }
        LOGGER.logInfo(NAME + "当前同步时间=" + lastModifiedTime + ", step=" + stepSize + ", speed=" + (speed / 10)
            + "/s timer=" + DoubleFormat.decimal(timer.takeSnapshot().mean(TimeUnit.SECONDS)));
        speed = 0;
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (!started) {
            return;
        }
        LOGGER.logInfo(NAME + "应用退出");
        stopped = true;
        try {
            Thread.sleep(5 * 1000);
        } catch (InterruptedException ignored) {
        }
        LOGGER.logInfo(NAME + "应用退出完毕");
        started = false;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
