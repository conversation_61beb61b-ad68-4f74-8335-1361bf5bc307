package cn.loveapp.orders.monitor.dto.grafana;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-23 10:39
 * @Description: grafana请求体
 */
@Data
public class GrafanaRequest {

	/**
	 * 告警消息内容
	 */
	private String message;
	/**
	 * 告警标题
	 */
	private String title;
	/**
	 * 告警返回链接网址
	 */
	private String ruleUrl;
	/**
	 * 匹配标签体
	 */
	private List<Tag> evalMatches;

	@Data
	public static class Tag {
		/**
		 * 标签名称
		 */
		private String metric;

		/**
		 * 标签内容
		 */
		private String value;

	}
}
