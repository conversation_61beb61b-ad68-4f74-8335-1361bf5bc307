package cn.loveapp.orders.monitor.dao.dream;

import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据落库状态记录表(AyOrderPullRecord)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-01-09
 */
public interface UserProductinfoTradeExtDao {

	/**
	 * 分页查询所有用户
	 *
	 * @param lastId
	 * @param limit
	 * @return
	 */
	List<UserProductionInfoExt> queryAllByLimit(@Param("lastId") Integer lastId, @Param("limit") int limit);

}
