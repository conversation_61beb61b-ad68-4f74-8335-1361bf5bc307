package cn.loveapp.orders.monitor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 17:00
 * @Description: Grafana警告消息转发配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "orders.monitor.grafana.alarm")
public class GrafanaAlarmForWardProperties {

	/**
	 * grafana的请求地址
	 */
	private String grafanaUrl;
	/**
	 * kubernetes的钉钉机器人地址
	 */
	private String kubernetesDingDingWebhook;
	/**
	 * 监控报警的钉钉机器人地址
	 */
	private String monitoringDingDingWebhook;
	/**
	 * 日志报警的钉钉机器人地址
	 */
	private String logAlarmDingDingWebhook;
}
