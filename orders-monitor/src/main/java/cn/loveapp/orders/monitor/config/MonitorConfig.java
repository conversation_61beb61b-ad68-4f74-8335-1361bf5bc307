package cn.loveapp.orders.monitor.config;

import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.convert.DurationUnit;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MonitorCofig
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Getter
@Setter
@Configuration
@EnableScheduling
public class MonitorConfig {
	@Value("${orders.monitor.db.size:13}")
	private int dbSize;

	@Value("${orders.monitor.service.url}")
	private String serviceUrl;

	@Value("${orders.monitor.service.host:trade.aiyongbao.com}")
	private String serviceHost;

	@Value("${orders.monitor.service.cookie:}")
	private String serviceCookie;

	@Value("${orders.monitor.service.name}")
	private String serviceName;

	@Value("${orders.monitor.scheduling.enable: true}")
	private boolean enableScheduling;

	@Value("${orders.monitor.alarm.enable: true}")
	private boolean enableAlarm;

	@Value("${orders.monitor.thread-pool.size: 20}")
	private int threadPoolSize;

	@Value("${orders.monitor.query.limit: 500}")
	private int queryLimit;

	@Value("${orders.monitor.api.check.limit:50}")
	private int apiCheckLimit;

//	@Value("${orders.monitor.checkMiss.enable: false}")
//	private boolean enableCheckMiss;

	/**
	 * 是否启用自动关闭订单对外服务的功能
	 */
	@Value("${orders.monitor.stopOrder.enable:false}")
	private boolean enableAutoStopOrder;

	@Value("${orders.mail.from:}")
	private String mailFrom;

	@Value("${orders.mail.to:}")
	private List<String> mailTo = Lists.newArrayList();

	//fullinfo

	@Value("${orders.monitor.fullinfo.enable:true}")
	private boolean fullinfoEnable;

	@Value("${orders.monitor.fullinfo.fields}")
	private String fullinfoFields;

	@Value("${orders.monitor.fullinfo.ignores}")
	private Set<String> fullinfoIgnores = Sets.newHashSet();

	@DurationUnit(ChronoUnit.HOURS)
	@Value("${orders.monitor.fullinfo.timeRange: 24h}")
	private Duration fullinfoTimeRange;

	@Value("${orders.monitor.fullinfo.status: WAIT_BUYER_CONFIRM_GOODS}")
	private String fullinfoStatus;

	@Value("${orders.monitor.fullinfo.number: 10000}")
	private int fullinfoNumber;

//	@Value("${orders.monitor.fullinfo.threshold: 1.0}")
//	private double fullinfoThreshold;

	//soldGet

	@Value("${orders.monitor.soldGet.enable:true}")
	private boolean soldGetEnable;

	@DurationUnit(ChronoUnit.HOURS)
	@Value("${orders.monitor.soldGet.timeRange:24h}")
	private Duration soldGetTimeRange;

	@Value("${orders.monitor.soldGet.status: WAIT_BUYER_CONFIRM_GOODS}")
	private String soldGetStatus;

	/**
	 * 是否开启多平台fullInfo校验
	 */
	@Value("${orders.monitor.multi.fullInfo.enable:true}")
	private boolean multiFullInfoEnable;

	/**
	 * 多平台fullInfo支持的平台
	 */
	@Value("${orders.monitor.multi.fullInfo.platforms:1688,PDD}")
	private List<String> multiFullInfoPlatforms = Lists.newArrayList();

	/**
	 * 多平台fullInfo时间范围
	 */
	@DurationUnit(ChronoUnit.HOURS)
	@Value("${orders.monitor.multi.fullInfo.timeRange:2H}")
	private Duration multiFullInfoTimeRange;

	/**
	 * 多平台fullInfo订单状态
	 */
	@Value("${orders.monitor.multi.fullInfo.status:" + TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS + "}")
	private String multiFullInfoStatus;

	/**
	 * 多平台fullInfo订单状态
	 */
	@Value("${orders.monitor.multi.fullInfo.fields:tid}")
	private String multiFullInfoFields;


	/**
	 * 多平台fullInfo各平台验证的样本数
	 */
	@Value("#{${orders.monitor.multi.fullInfo.numbers:{1688:'2',PDD:'2'}}}")
	private Map<String, String> multiFullInfoNumbers;

	/**
	 * 多平台fullInfo各平台要请求的字段
	 */
	@Value("#{${orders.monitor.multi.fullInfo.platformFields:{1688:'CanSendCheck,NativeLogistics'}}}")
	private Map<String, String> multiFullInfoPlatformFields;

	/**
	 * 多平台fullInfo各平台要忽略的字段
	 */
	private Map<String, Set<String>> multiFullInfoIgnores;

	@Value("#{${orders.monitor.multi.fullinfo.ignores:{PDD:'card_info_list,service_orders,receiver_mobile,receiver_city,receiver_state,receiver_name,receiver_town,buyer_nick,receiver_address,receiver_phone'}}}")
	private void setMultiFullInfoIgnores(Map<String, String> multiFullInfoIgnores){
		this.multiFullInfoIgnores = multiFullInfoIgnores.entrySet().stream().collect(Collectors.toMap(
			Map.Entry::getKey, e->Sets.newHashSet(e.getValue().split(","))));
	}

	/**
	 * 是否开启多平台soldGet校验
	 */
	@Value("${orders.monitor.multi.soldGet.enable:true}")
	private boolean multiSoldGetEnable;

	/**
	 * 多平台soldGet支持的平台
	 */
	@Value("${orders.monitor.multi.soldGet.platforms:1688,PDD}")
	private List<String> multiSoldGetPlatforms = Lists.newArrayList();

	/**
	 * 多平台soldGet时间范围
	 */
	@DurationUnit(ChronoUnit.HOURS)
	@Value("${orders.monitor.multi.soldGet.timeRange:24H}")
	private Duration multiSoldGetTimeRange;

	/**
	 * 多平台soldGet订单状态
	 */
	@Value("${orders.monitor.multi.soldGet.status:" + TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS + "}")
	private String multiSoldGetStatus;

	/**
	 * 各平台soldGet指定的订单状态
	 *
	 * soldGet status默认使用{@link MonitorConfig#multiSoldGetStatus}, 这里用来指定特殊平台的soldGet status
	 */
	@Value("#{${orders.monitor.multi.specify.soldGet.status:{KWAISHOP:'2'}}}")
	private Map<String, String> multiSpecifySoldGetStatus;

	/**
	 * 多平台soldGet各平台pageSize
	 */
	@Value("#{${orders.monitor.multi.soldGet.pageSizes:{1688:'20',PDD:'50'}}}")
	private Map<String, String> multiSoldGetPageSizes;


	/**
	 * 多平台soldGet各平台验证的样本数
	 */
	@Value("#{${orders.monitor.multi.soldGet.numbers:{1688:'50',PDD:'10000'}}}")
	private Map<String, String> multiSoldGetNumbers;

	/**
	 * 多平台soldGet各平台查询存单接口的排序
	 */
	@Value("#{${orders.monitor.multi.soldGet.sort:{1688:'desc',PDD:'asc'}}}")
	private Map<String, String> multiSoldGetSorts;

	@Value("${orders.monitor.soldGet.number: 1000}")
	private int soldGetNumber;

	@Value("${orders.monitor.soldGet.fields: tid}")
	private String soldGetFields;

	@Value("${orders.monitor.soldGet.pageSize: 50}")
	private long soldGetPageSize;

	@Value("${orders.monitor.soldGet.threshold: 1.0}")
	private double soldGetThreshold;

	//rds推送

	@Value("${orders.monitor.rds.enable:true}")
	private boolean rdsDelayEnable;

	@DurationUnit(ChronoUnit.MINUTES)
	@Value("${orders.monitor.rds.timeRange:10m}")
	private Duration rdsTimeRange;

	@DurationUnit(ChronoUnit.SECONDS)
	@Value("${orders.monitor.rds.delayTime:30S}")
	private Duration rdsDelayTime;

	@Value("${orders.monitor.rds.delayNumber: 100000}")
	private int rdsDelayNumber;

	@Value("${orders.monitor.rds.delayThreshold: 0.999}")
	private double rdsDelayThreshold;

	//ons消费

	@Value("${orders.monitor.ons.enable:true}")
	private boolean onsEnable;


	@Value("${orders.taobao.onsOrder.namesrvAddr:}")
	private String onsOrderNamesrvAddr;

	@Value("${orders.monitor.onsOrder.enable:false}")
	private boolean onsOrderEnable;

	@Value("${orders.monitor.aliyun-rocketmq.enable:true}")
	private boolean aliyunRocketMQEnable;

	@Value("${orders.monitor.es-shared.enable:true}")
	private boolean esSharedEnable;

	//统计自动关闭的用户

	@Value("${orders.monitor.compare_autoclose.enable:true}")
	private boolean compareAutoCloseEnable;


	//更新指定状态订单的数据

	@Value("${orders.monitor.fixTaoStatus.producer-pool.size:1}")
	private int orderFixTaoStatusProducerPoolSize;

	@Value("${orders.monitor.fixTaoStatus.consumer-pool.size:1}")
	private int orderFixTaoStatusConsumerPoolSize;

	@Value("${orders.monitor.fixTaoStatus.dbIds:}")
	private List<Integer> orderFixTaoStatusDbIds = Lists.newArrayList();

	private LocalDateTime orderFixTaoStatusStartTime;

	@Value("${orders.monitor.fixTaoStatus.start-time:0001-01-01 00:00:00}")
	private void setOrderFixTaoStatusStartTime(String startTime) {

		if (startTime != null && !startTime.isEmpty()) {
			orderFixTaoStatusStartTime = LocalDateTime.parse(startTime, DateUtil.FORMATTER_DATETIME);
		}
	}

	@Value("${orders.monitor.fixTaoStatus.status:WAIT_SELLER_SEND_GOODS,PAID_FORBID_CONSIGN}")
	private List<String> orderFixTaoStatusStatus = Lists.newArrayList();

	@Value("${orders.monitor.fixTaoStatus.limit:200}")
	private long orderFixTaoStatusLimit;

	//更新指定状态订单的数据

	@Value("${orders.monitor.fixMergeOrder.topic:iymergeorder}")
	private String orderFixMergeOrderTopic;

	@Value("${orders.monitor.fixMergeOrder.consumerid:CID_iymergeorder}")
	private String orderFixMergeOrderConsumerid;

	@Value("${orders.monitor.fixMergeOrder.producer-pool.size:50}")
	private int orderFixMergeOrderProducerPoolSize;

	@Value("${orders.monitor.fixMergeOrder.pageSize:100}")
	private int orderFixMergeOrderPageSize;

	@Value("${orders.monitor.fixMergeOrder.limit:10000}")
	private long orderFixMergeOrderLimit;

	//修补丢失MQ的功能

	@Deprecated
	@Value("${orders.monitor.fixMissMq.enable:true}")
	private boolean fixMissMQEnable;

	@Value("${orders.monitor.fixMissMq.limit:5000}")
	private int fixMissMQLimit;

	@Deprecated
	@Value("${orders.monitor.fixMissMq.producer-pool.size:1}")
	private int fixMissMQProducerPoolSize;

	@Deprecated
	@Value("${orders.monitor.fixMissMq.consumer-pool.size:1}")
	private int fixMissMQConsumerPoolSize;

	@Value("${orders.monitor.fixPrint.nick:}")
	private String orderFixPrintNick;

	@Value("${orders.monitor.fixPrint.limit:1}")
	private int orderFixPrintLimit;

	@Value("${orders.monitor.fixPrint.pool.size:1}")
	private int orderFixPrintPoolSize;

	@Value("${orders.monitor.fixPrint.start-time:2019-11-11 00:00:00}")
	private String orderFixPrintStartTime;

	@Value("${orders.monitor.fixPrint.end-time:2019-11-11 18:00:00}")
	private String orderFixPrintEndTime;
}
