package cn.loveapp.orders.monitor.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.common.api.request.AyRefundGetRequest;
import cn.loveapp.orders.common.api.request.FullInfoRequest;
import cn.loveapp.orders.common.api.response.AyRefundGetResponse;
import cn.loveapp.orders.common.api.response.FullinfoResponse;
import cn.loveapp.orders.common.config.taobao.TaobaoFullInfoAppConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoRefundGetConfig;
import cn.loveapp.orders.common.dao.mongo.OrderRefundRepository;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderRefund;
import cn.loveapp.orders.common.exception.UnNeedRetryException;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.monitor.dto.PlatformOrderInfoRequest;

/**
 * <AUTHOR>
 * @date 2023-05-20 14:12
 * @Description: 监控服务控制器
 */
@RestController
@RequestMapping("monitor/operations")
public class OperationController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OperationController.class);

    @Autowired
    private TradeApiPlatformHandleService tradeApiPlatformHandleService;

    @Autowired
    private UserService userService;

    @Autowired
    private TaobaoFullInfoAppConfig taobaoFullInfoAppConfig;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderRefundRepository orderRefundRepository;

    @Autowired
    private TaobaoRefundGetConfig taobaoRefundGetConfig;

    @RequestMapping("/platform.order.get")
    public CommonApiResponse<List<FullinfoResponse>> getPlatformOrderInfo(@RequestBody PlatformOrderInfoRequest request) {
        paramHandle(request);

        String storeId = request.getPlatformId();
        String appName = request.getAppName();
        String tid = request.getTid();
        String sellerNick = request.getSellerNick();
        String sellerId = null;

        if (StringUtils.isEmpty(sellerNick)) {
            TcOrder ayTradeMain = orderRepository.queryByTid(tid, storeId, appName);
            if (ayTradeMain == null) {
                return CommonApiResponse.of(1001, "获取用户信息失败，mongo主单无订单信息，请输入sellerNick通过查询用户表获取用户信息");
            }
            sellerNick = ayTradeMain.getSellerNick();
            sellerId = ayTradeMain.getSellerId();
        }

        String topSession = userService.getAuthorization(sellerNick, sellerId, storeId, appName);

        if (StringUtils.isEmpty(topSession)) {
            return CommonApiResponse.of(1001, "topSession为空");
        }

        FullInfoRequest fullInfoRequest = new FullInfoRequest();
        fullInfoRequest.setTid(tid);
        fullInfoRequest.setSellerNick(sellerNick);
        fullInfoRequest.setTopSession(topSession);
        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            fullInfoRequest.setApiFields(taobaoFullInfoAppConfig.getFileds());
        }

        FullinfoResponse fullInfoResponse = tradeApiPlatformHandleService.fullInfo(fullInfoRequest, storeId, appName);

        return CommonApiResponse.success(Lists.newArrayList(fullInfoResponse));
    }

    @RequestMapping("/platform.refund.get")
    public CommonApiResponse<List<AyRefundGetResponse>> getPlatformOrderRefundInfo(@RequestBody PlatformOrderInfoRequest request)
        throws UnNeedRetryException, UserNeedAuthException {
        paramHandle(request);

        String storeId = request.getPlatformId();
        String appName = request.getAppName();
        String tid = request.getTid();
        String sellerNick = request.getSellerNick();
        String sellerId = null;

        List<TcOrderRefund> tcOrderRefunds = orderRefundRepository.queryByTid(tid, sellerId, storeId, appName);
        if (CollectionUtils.isEmpty(tcOrderRefunds)) {
            return CommonApiResponse.of(1001, "获取用户信息失败，库中无订单退款信息，请输入sellerNick查询用户表信息，请输入sellerNick通过查询用户表获取用户信息");
        }

        sellerNick = tcOrderRefunds.get(0).getSellerNick();
        sellerId = tcOrderRefunds.get(0).getSellerId();

        String topSession = userService.getAuthorization(sellerNick, sellerId, storeId, appName);

        if (StringUtils.isEmpty(topSession)) {
            return CommonApiResponse.of(1001, "topSession为空");
        }

        List<AyRefundGetResponse> refundOrderInfos = new ArrayList<>();
        for (TcOrderRefund tcOrderRefund : tcOrderRefunds) {
            AyRefundGetRequest refundGetRequest = new AyRefundGetRequest();
            refundGetRequest.setRefundId(tcOrderRefund.getRefundId());
            refundGetRequest.setFields(taobaoRefundGetConfig.getFields());
            refundGetRequest.setSellerId(tcOrderRefund.getSellerId());
            AyRefundGetResponse ayRefundGetResponse =
                tradeApiPlatformHandleService.refundGet(refundGetRequest, sellerNick, storeId, appName);
            refundOrderInfos.add(ayRefundGetResponse);
        }

        return CommonApiResponse.success(refundOrderInfos);
    }

    private void paramHandle(PlatformOrderInfoRequest request) {
        request.setTid(request.getTid().trim());
        request.setSellerNick(request.getSellerNick().trim());
        request.setPlatformId(request.getPlatformId().trim());

        if (!StringUtils.isEmpty(request.getPlatformId()) && CommonPlatformConstants.PLATFORM_TAO.equals(request.getPlatformId())) {
            request.setAppName(null);
        } else {
            request.setAppName(request.getAppName().trim());
        }

    }

}
