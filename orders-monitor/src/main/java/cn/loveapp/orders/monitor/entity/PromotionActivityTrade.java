package cn.loveapp.orders.monitor.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交易-营销活动赠送主表(PromotionActivityTrade)实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-22 16:38
 */
@Data
public class PromotionActivityTrade implements Serializable {
	private static final long serialVersionUID = -86198880763433634L;
	private Integer id;

	/**
	 * 卖家昵称
	 */
	private String sellernick;

	/**
	 * 赠送的时长，单位天
	 */
	private Integer actCycle;

	/**
	 * 何时给用户赠送运营时长的时间
	 */
	private LocalDateTime optime;

	/**
	 * 活动类型
	 */
	private String actflag;

	/**
	 * 赠送的客服名字
	 */
	private String sender;

	/**
	 * 活动Code
	 */
	private String promotionCode;

	/**
	 * 是否已经使用
	 */
	private Integer isused;


}
