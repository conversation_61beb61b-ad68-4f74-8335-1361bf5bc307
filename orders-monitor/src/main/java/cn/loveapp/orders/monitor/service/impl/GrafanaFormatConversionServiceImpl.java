package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.config.GrafanaAlarmForWardProperties;
import cn.loveapp.orders.monitor.dto.grafana.GrafanaRequest;
import cn.loveapp.orders.monitor.service.GrafanaFormatConversionService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-13 11:46
 * @Description: Grafana告警格式转钉钉机器人消息格式服务接口实现类
 */
@Service
public class GrafanaFormatConversionServiceImpl implements GrafanaFormatConversionService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(GrafanaFormatConversionServiceImpl.class);
	private static final String MARKDOWN_LINK_TITLE = "[查看详情]";
	private static final String MSG_TYPE = "actionCard";
	private static final String WARNING_EMOJ = "[Alerting]";
	private static final String WARNING = "警告: ";
	private static final String RECOVER_EMOJ = "[OK]";
	private static final String RECOVER = "恢复: ";
	private static final String MARKDOWN_TITLE_FORMAT = "### ";

    private static final String FEISHU_MESSAGE_TEMPLATE = "{\n" +
        "    \"msg_type\": \"interactive\",\n" +
        "    \"card\": {\n" +
        "        \"elements\": [\n" +
        "            {\n" +
        "                \"tag\": \"div\",\n" +
        "                \"text\": {\n" +
        "                    \"content\": \"$CONTENT$\",\n" +
        "                    \"tag\": \"lark_md\"\n" +
        "                }\n" +
        "            },\n" +
        "           {\n" +
        "                \"actions\": [],\n" +
        "                \"tag\": \"action\"\n" +
        "            }" +
        "        ],\n" +
        "        \"header\": {\n" +
        "            \"title\": {\n" +
        "                \"content\": \"$TITLE$\",\n" +
        "                \"tag\": \"plain_text\"\n" +
        "            },\n" +
        "            \"template\": \"$COLOR$\"\n" +
        "        }\n" +
        "    }\n" +
        "}";

	@Autowired
	GrafanaAlarmForWardProperties grafanaAlarmForWardProperties;

	@Override
	public void convertAndSendByKubernetesAlarm(GrafanaRequest grafanaRequest) {
		NotificationFormatConversionRequest conversionRequest = getMarkdownRequest(grafanaRequest);
		String dingDingUrl = grafanaAlarmForWardProperties.getKubernetesDingDingWebhook();
		urlHttp(conversionRequest, dingDingUrl);
	}

	@Override
	public void convertAndSendByMonitoringAlarm(GrafanaRequest grafanaRequest) {
		NotificationFormatConversionRequest conversionRequest = getMarkdownRequest(grafanaRequest);
		String dingDingUrl = grafanaAlarmForWardProperties.getMonitoringDingDingWebhook();
		urlHttp(conversionRequest, dingDingUrl);
	}

	@Override
	public void convertAndSendByLogAlarm(GrafanaRequest grafanaRequest) {
		NotificationFormatConversionRequest conversionRequest = getMarkdownRequest(grafanaRequest);
		String dingDingUrl = grafanaAlarmForWardProperties.getLogAlarmDingDingWebhook();
		urlHttp(conversionRequest, dingDingUrl);
	}


    @Override
    public void convertAndSendByAlarm(String hookId, GrafanaRequest grafanaRequest) {
        NotificationFormatConversionRequest conversionRequest = getMarkdownRequest(grafanaRequest);
        String url = "https://open.feishu.cn/open-apis/bot/v2/hook/" + hookId;
        urlHttp(conversionRequest, url);
    }

    private void urlHttp(NotificationFormatConversionRequest param, String url) {
        if(StringUtils.containsIgnoreCase(url, "feishu")){
            feishuUrlHttp(param, url);
        }else {
            dingdingUrlHttp(param, url);
        }
    }

	private void dingdingUrlHttp(NotificationFormatConversionRequest param, String url) {
		HashMap<String, String> map = new HashMap<>(2);
		map.put("msgtype", MSG_TYPE);
		map.put(MSG_TYPE, JSON.toJSONString(param));
		String body = JSONObject.toJSONString(map);

		LOGGER.logInfo(param.getTitle(), null, "钉钉请求参数为: " + body);

		HttpHeaders headers = new HttpHeaders();
		headers.add("Content-Type", "application/json");
		HttpEntity<String> formEntity = new HttpEntity<>(body, headers);
		RestTemplate restTemplate = new RestTemplate();
		restTemplate.postForEntity(url, formEntity, String.class);
	}

    private void feishuUrlHttp(NotificationFormatConversionRequest param, String url) {
        String color = param.getTitle().contains("恢复") ? "green" : "red";
        String body = FEISHU_MESSAGE_TEMPLATE.replace("$TITLE$", param.getTitle())
            .replace("$CONTENT$", param.getText().replace("\n", "\\n"))
            .replace("$COLOR$", color);

        LOGGER.logInfo(param.getTitle(), null, "飞书请求参数为: " + body);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> formEntity = new HttpEntity<>(body, headers);
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.postForEntity(url, formEntity, String.class);
    }

	private NotificationFormatConversionRequest getMarkdownRequest(GrafanaRequest grafanaRequest) {
		// 将表情包提示替换为对应文字提示
		String markdownTitle;
		if (grafanaRequest.getTitle().contains(WARNING_EMOJ)) {
			markdownTitle = grafanaRequest.getTitle().replace(WARNING_EMOJ, WARNING);
		} else {
			markdownTitle = grafanaRequest.getTitle().replace(RECOVER_EMOJ, RECOVER);
		}

		List<GrafanaRequest.Tag> evalMatches = grafanaRequest.getEvalMatches();
		StrBuilder evalMatchesBuilder = new StrBuilder();
		DecimalFormat decimalFormat = new DecimalFormat("0.00");
		for (int i = 0; i < evalMatches.size(); i++) {
			GrafanaRequest.Tag tagRequest = evalMatches.get(i);
			try {
				evalMatchesBuilder.append((i + 1) + "." + tagRequest.getMetric() + " : " + decimalFormat.format(new BigDecimal(tagRequest.getValue())) + "  \n");
			} catch (Exception e) {
				//避免tagRequest.getValue()出现数据转换异常
				evalMatchesBuilder.append((i + 1) + "." + tagRequest.getMetric() + " : " + tagRequest.getValue() + "  \n");
			}
		}

		// 钉钉接受markdown文本格式时，换行符前需要两个空格
		String markdownMessage = grafanaRequest.getMessage().replace("\n", "  \n");
		// 组装跳转链接
		String singleUrl = "(" + grafanaRequest.getRuleUrl().replace("localhost", grafanaAlarmForWardProperties.getGrafanaUrl()) + ")";
		String markdownText = MARKDOWN_TITLE_FORMAT + markdownTitle + "  \n" + markdownMessage + "  \n" + evalMatchesBuilder + MARKDOWN_LINK_TITLE + singleUrl;

		NotificationFormatConversionRequest dingDingFormatConversionRequest = new NotificationFormatConversionRequest();
		dingDingFormatConversionRequest.setTitle(markdownTitle);
		dingDingFormatConversionRequest.setText(markdownText);
		return dingDingFormatConversionRequest;
	}

	@Data
	private static class NotificationFormatConversionRequest {

		/**
		 * 机器人消息标题
		 */
		private String title;
		/**
		 * 机器人消息内容
		 */
		private String text;

	}
}
