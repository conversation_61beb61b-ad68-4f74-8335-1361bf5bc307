package cn.loveapp.orders.monitor.checker.impl;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Sets;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Data;

/**
 * OrderOnsCheckServiceImpl
 *
 * <AUTHOR>
 * @date 2018-12-18
 */
@Service
public class ElasticsearchMonitorChecker extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchMonitorChecker.class);
	@Autowired
	private RestHighLevelClient client;

	@Autowired
	private MeterRegistry registry;

	@Autowired
	public MonitorConfig monitorConfig;

	private Map<String, ShardStatus> shardStatuses;

	private Set<Meter> shardMeters = Sets.newHashSet();

	@Override
	public CheckerType type() {
		return CheckerType.ES_SHARED;
	}

	@Override
	public String name() {
		return "ES Shared Monitor";
	}

	@Override
	protected boolean enableDebug() {
		return true;
	}

	@Override
	protected synchronized String innerCheck() {
		try {
			Request request = new Request(HttpGet.METHOD_NAME, "/_cat/shards/ay_*");
			request.addParameter("h", "s,n,i,p,iito,idto,sqto,gto");
			request.addParameter("format", "JSON");
			Response response = client.getLowLevelClient().performRequest(request);
			if(response.getStatusLine().getStatusCode() == HttpStatus.OK.value()){
				String text = EntityUtils.toString(response.getEntity());
				shardStatuses = JSON.parseArray(text, ShardStatus.class).stream()
					.collect(Collectors.toMap(a->a.getShared()+a.getType()+a.getIndexName(), Function.identity()));

				// 清理历史指标, 防止回调获取到错误数据
				shardMeters.forEach(e->registry.remove(e));
				shardMeters.clear();

				for(String key : shardStatuses.keySet()){
					ShardStatus shardStatus = shardStatuses.get(key);
					Meter meter = Gauge.builder("es_shared_index", MonitoringSummaryData.getInstance(), x -> Long.parseLong(shardStatuses.get(key).getIndexTotal()))
						.tag("op", "index")
						.tag("node", shardStatus.getNode())
						.tag("type", shardStatus.getType())
						.tag("index", shardStatus.getIndexName())
						.tag("shared", shardStatus.getShared())
						.register(registry);
					shardMeters.add(meter);


					meter = Gauge.builder("es_shared_index", MonitoringSummaryData.getInstance(), x -> Long.parseLong(shardStatuses.get(key).getDeleteTotal()))
						.tag("op", "delete")
						.tag("node", shardStatus.getNode())
						.tag("type", shardStatus.getType())
						.tag("index", shardStatus.getIndexName())
						.tag("shared", shardStatus.getShared())
						.register(registry);
					shardMeters.add(meter);

					meter = Gauge.builder("es_shared_search", MonitoringSummaryData.getInstance(), x -> Long.parseLong(shardStatuses.get(key).getGetTotal()))
						.tag("op", "get")
						.tag("node", shardStatus.getNode())
						.tag("type", shardStatus.getType())
						.tag("index", shardStatus.getIndexName())
						.tag("shared", shardStatus.getShared())
						.register(registry);
					shardMeters.add(meter);

					meter = Gauge.builder("es_shared_search", MonitoringSummaryData.getInstance(), x -> Long.parseLong(shardStatuses.get(key).getQueryTotal()))
						.tag("op", "query")
						.tag("node", shardStatus.getNode())
						.tag("type", shardStatus.getType())
						.tag("index", shardStatus.getIndexName())
						.tag("shared", shardStatus.getShared())
						.register(registry);
					shardMeters.add(meter);
				}

			}else{
				LOGGER.logError(name() + " 获取shared状态失败: " + response.getStatusLine().toString());
			}
		} catch (Exception e) {
			LOGGER.logError(name() + " 获取shared状态失败: " + e.getMessage(), e);
		}
		return null;
	}


	@Data
	protected static class ShardStatus{
		@JSONField(name = "s")
		private String shared;

		@JSONField(name = "n")
		private String node;

		@JSONField(name = "i")
		private String indexName;

		@JSONField(name = "p")
		private String type;

		@JSONField(name = "iito")
		private String indexTotal;

		@JSONField(name = "idto")
		private String deleteTotal;

		@JSONField(name = "sqto")
		private String queryTotal;

		@JSONField(name = "gto")
		private String getTotal;
	}
}
