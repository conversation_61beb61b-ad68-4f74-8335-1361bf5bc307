package cn.loveapp.orders.monitor.job;

import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.checker.MonitorCheckerFactory;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 正确率校验任务
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Component
public class MonitorCheckTask {

	@Autowired
	MonitorConfig monitorConfig;

	@Autowired
	private MonitorCheckerFactory factory;

	@Scheduled(cron = "${orders.monitor.fullinfo.cron: 0 20 * * * ?}")
	public void runFullinfoMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isFullinfoEnable()){
			factory.getChecker(CheckerType.TAO_BAO_FULLINFO).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.multi.fullinfo.cron: 0 0 7,13,19 * * ?}")
	public void runPddFullinfoMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isMultiFullInfoEnable()){
			factory.getChecker(CheckerType.MULTI_FULLINFO).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.soldGet.cron: 0 50 * * * ?}")
	public void soldGetNumberMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isSoldGetEnable()){
			factory.getChecker(CheckerType.TAO_BAO_SOLD_GET).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.multi.soldGet.cron: 0 0 8,14,20 * * ?}")
	public void pddSoldGetNumberMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isMultiSoldGetEnable()){
			factory.getChecker(CheckerType.MULTI_SOLD_GET).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.rds.cron: 0 0/10 * * * ?}")
	public void runRdsDelayMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isRdsDelayEnable()){
			factory.getChecker(CheckerType.RDS_PUSH).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.ons.cron: 3/10 * * * * ?}")
	public void runOnsMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isOnsEnable()){
			factory.getChecker(CheckerType.ONS).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.ons.cron: 3/10 * * * * ?}")
	public void runOns2Monitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isOnsOrderEnable()){
			factory.getChecker(CheckerType.ONS_ORDER).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.aliyun-rocketmq.cron: 6/10 * * * * ?}")
	public void runAliyunRocketMQMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isAliyunRocketMQEnable()){
			factory.getChecker(CheckerType.ALIYUN_ROCKETMQ).check();
		}
	}

	@Scheduled(cron = "${orders.monitor.es-shared.cron: 1/10 * * * * ?}")
	public void runEsSharedMonitor() {
		if(monitorConfig.isEnableScheduling() && monitorConfig.isEsSharedEnable()){
			factory.getChecker(CheckerType.ES_SHARED).check();
		}
	}

}
