package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.checker.MonitorCheckerInterface;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.service.MonitorService;
import cn.loveapp.orders.monitor.service.MonitorUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;

import java.util.concurrent.atomic.AtomicLong;

/**
 * AbstractMonitorChecker
 *
 * <AUTHOR>
 * @date 2018-12-22
 */
public abstract class AbstractMonitorChecker implements MonitorCheckerInterface, ApplicationListener<ContextClosedEvent> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractMonitorChecker.class);
	private volatile boolean isChecking = false;

	@Autowired
	protected MonitorConfig monitorConfig;

	@Autowired
	protected MonitorService monitorService;

	@Autowired
	protected MonitorUserService monitorUserService;

	protected volatile boolean isStoped;

	protected AtomicLong userNeedAuthNumber = new AtomicLong();
	protected AtomicLong validNumber = new AtomicLong();

	public void setValidNumber(long validNumber) {
		this.validNumber.set(validNumber);
	}

	protected void decrementValidNumber() {
		this.validNumber.decrementAndGet();
	}

	@Override
	public void check() {
		if (isChecking) {
			LOGGER.logWarn(name() + " 校验正在执行, 请稍后重试");
			return;
		}
		isChecking = true;
		setValidNumber(0);
		userNeedAuthNumber.set(0);

		log(name() + " 校验开始...");
		long start = System.currentTimeMillis();
		try {
			String result = innerCheck();
			if (StringUtils.isNotEmpty(result)) {
				log(name() + " 校验结果 " + result +
					( validNumber.longValue() > 0 ? " 有效样本数量: " + validNumber.longValue() : "" ) +
					( userNeedAuthNumber.intValue() > 0 ? (" 授权失效数量: " + userNeedAuthNumber.longValue()) : ""));
			}
		} finally {
			log(name() + " 校验结束, 耗时: " + (System.currentTimeMillis() - start));
			isChecking = false;
		}
	}

	private void log(String log) {
		if (enableDebug()) {
			LOGGER.logDebug(log);
		} else {
			LOGGER.logInfo(log);
		}
	}

	protected boolean enableDebug() {
		return false;
	}

	/**
	 * 校验名称
	 *
	 * @return
	 */
	protected abstract String name();

	/**
	 * 开始监控校验
	 *
	 * @return
	 */
	protected abstract String innerCheck();

	@Override
	public void onApplicationEvent(ContextClosedEvent event){
		if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
			return;
		}
		isStoped = true;
	}
}
