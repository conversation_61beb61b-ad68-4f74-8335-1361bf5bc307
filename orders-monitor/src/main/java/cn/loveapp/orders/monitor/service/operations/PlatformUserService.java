package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoRequest;
import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022-08-02 14:20
 * @Description: 运维平台用户服务接口
 */
public interface PlatformUserService {

    /**
     * 获取用户信息
     *
     * @param userInfoRequest
     * @param request
     * @param response
     * @return
     */
    PlatformPowerUserInfoResponse getPlatformUserInfo(PlatformPowerUserInfoRequest userInfoRequest,
                                                      HttpServletRequest request, HttpServletResponse response);
}
