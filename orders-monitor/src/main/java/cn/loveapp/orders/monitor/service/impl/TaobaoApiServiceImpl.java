package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.taobao.TaobaoSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.service.TaobaoApiService;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.request.TradeFullinfoGetRequest;
import com.taobao.api.request.TradesSoldGetRequest;
import com.taobao.api.response.TradeFullinfoGetResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 淘宝api访问service
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Service
public class TaobaoApiServiceImpl implements TaobaoApiService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaobaoApiServiceImpl.class);
	@Autowired
	private UserService userService;

	@Autowired
	private TaobaoSDKService taobaoService;

	@Autowired
	private MonitorConfig monitorConfig;

	@Override
	public String fullinfo(String nick, long tid) throws UserNeedAuthException, ApiException {
		LOGGER.logDebug("请求淘宝接口: fullinfo");
		String topSession = userService.getAuthorization(nick, null, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE);

		TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
		req.setFields(monitorConfig.getFullinfoFields());
		req.setTid(tid);

		String reponse =  execute(req, topSession).getBody();
		if(LOGGER.isDebugEnabled()){
			LOGGER.logDebug("淘宝接口fullinfo返回: " + reponse);
		}

		return reponse;
	}

	@Override
	public TradeFullinfoGetResponse fullinfoForObject(String topSession, long tid, String... fields) throws ApiException {
		TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
		req.setFields(String.join(",", fields));
		req.setTid(tid);
		return execute(req, topSession);
	}

	@Override
	public String soldGet(String nick, LocalDateTime startTime, LocalDateTime endTime) throws UserNeedAuthException, ApiException{
		return this.soldGet(nick, startTime, endTime, -1);
	}

	@Override
	public String soldGet(String nick, LocalDateTime startTime, LocalDateTime endTime, long pageNo) throws UserNeedAuthException, ApiException {
		LOGGER.logDebug("请求淘宝接口: soldGet");
		String topSession = userService.getAuthorization(nick, null, CommonPlatformConstants.PLATFORM_TAO, CommonAppConstants.APP_TRADE);

		TradesSoldGetRequest req = new TradesSoldGetRequest();
		req.setFields(monitorConfig.getSoldGetFields());
		req.setPageNo(pageNo <= 0 ? 1L : pageNo);
		req.setStatus(monitorConfig.getSoldGetStatus());
		req.setPageSize(monitorConfig.getSoldGetPageSize());
		req.setStartCreated(DateUtil.convertLocalDateTimetoDate(startTime));
		req.setEndCreated(DateUtil.convertLocalDateTimetoDate(endTime));
		req.setUseHasNext(pageNo >= 1);

		String response = execute(req, topSession).getBody();
		if(LOGGER.isDebugEnabled()){
			LOGGER.logDebug("淘宝接口soldGet返回: " + response);
		}
		return response;
	}

	private <T extends TaobaoResponse> T execute(TaobaoRequest<T> request, String accessToken) {
		T result = taobaoService.execute(request, accessToken, CommonAppConstants.APP_TRADE);
		return result;
	}
}
