package cn.loveapp.orders.monitor.controller.operations;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.service.UserCenterService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.monitor.constant.operations.ResponseConstant;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;
import cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog;
import cn.loveapp.orders.monitor.entity.OrderSearchTrade;
import cn.loveapp.orders.monitor.service.AyTradeOpenUserLogService;
import cn.loveapp.orders.monitor.service.OrderSearchTradeService;
import cn.loveapp.orders.monitor.utils.MonitorPlatformRequestParamProcessing;
import cn.loveapp.uac.response.UserFullInfoResponse;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-03 20:33
 * @Description: 用户信息控制器
 */
@RestController
@RequestMapping(value = "monitor/operations/userinfo")
public class UserInfoController {
    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserInfoController.class);

    @Autowired
    private UserProductionInfoExtService userProductionInfoExtService;

    @Autowired
    private OrderSearchTradeService orderSearchTradeService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private AyTradeOpenUserLogService ayTradeOpenUserLogService;

    @Autowired
    private MonitorPlatformRequestParamProcessing monitorPlatformRequestParamProcessing;

    @RequestMapping(value = "/openuserinfolog.list.get")
    public CommonApiResponse<List<AyTradeOpenUserLog>>
        getUserOpenUserInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getSellerNick())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        String sellerNick = request.getSellerNick();
        List<AyTradeOpenUserLog> allOpenUserLogBySellerNick =
            ayTradeOpenUserLogService.getAllOpenUserLogBySellerNick(sellerNick);
        return CommonApiResponse.success(allOpenUserLogBySellerNick);
    }

    @RequestMapping(value = "/ordersearchinfo.list.get")
    public CommonApiResponse<List<OrderSearchTrade>>
        getUserOrderSearch(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getSellerNick())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        String sellerId = request.getSellerId();
        List<OrderSearchTrade> orderSearchTrades =
            orderSearchTradeService.getAllOrderSearchBySellerNick(sellerNick, sellerId, storeId, appName);
        return CommonApiResponse.success(orderSearchTrades);
    }

    @RequestMapping(value = "/productinfo.list.get")
    public CommonApiResponse<List<UserFullInfoResponse>>
        getUserProductInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getSellerNick())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        UserFullInfoResponse userFullInfo = userCenterService.getUserFullInfo(sellerNick, null, appName, storeId);

        String accessToken = userFullInfo.getAccessToken();
        if (StringUtils.isNotEmpty(accessToken)) {
            userFullInfo.setAccessToken(monitorPlatformRequestParamProcessing.getEncryptedStr(accessToken));
        }

        String refreshToken = userFullInfo.getRefreshToken();
        if (StringUtils.isNotEmpty(refreshToken)) {
            userFullInfo.setRefreshToken(monitorPlatformRequestParamProcessing.getEncryptedStr(refreshToken));
        }

        List<UserFullInfoResponse> params = new ArrayList<>();
        params.add(userFullInfo);

        return CommonApiResponse.success(params);
    }

    @RequestMapping(value = "/extinfo.list.get")
    public CommonApiResponse<List<UserProductionInfoExt>>
        getExtUserInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getSellerNick())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        List<UserProductionInfoExt> userProductionInfoExts =
            userProductionInfoExtService.queryListBySellerNick(sellerNick, storeId, appName);
        return CommonApiResponse.success(userProductionInfoExts);
    }
}
