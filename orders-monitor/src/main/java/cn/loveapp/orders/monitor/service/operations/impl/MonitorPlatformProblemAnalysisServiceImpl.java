package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;
import cn.loveapp.orders.monitor.service.operations.MonitorPlatformProblemAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-08 14:28
 * @Description: 运维可视化问题分析服务接口实现类
 */
@Service
public class MonitorPlatformProblemAnalysisServiceImpl implements MonitorPlatformProblemAnalysisService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorPlatformProblemAnalysisServiceImpl.class);

    @Autowired
    private AyUserQueryServiceImpl ayUserQueryService;

    @Autowired
    private AyNewUserServiceLogQueryServiceImpl ayNewUserServiceLogQueryService;

    @Autowired
    private AySoldGetLogQueryServiceImpl aySoldGetLogQueryService;

    @Autowired
    private AyUserProblemQueryChainServiceImpl ayUserProblemQueryChainService;

    @Autowired
    private AyOrderQueryServiceImpl ayOrderQueryService;

    @Autowired
    private PlatformFullInfoApiQueryServiceImpl platformFullInfoApiQueryService;

    @Autowired
    private AyTmcSubLogQueryServiceImpl ayTmcSubLogQueryService;

    @Autowired
    private AyRdsLogQueryServiceImpl rdsLogQueryService;

    @Autowired
    private AyMcRouterLogQueryServiceImpl mcRouterLogQueryService;

    @Autowired
    private AyMcFullInfoLogQueryServiceImpl fullInfoLogQueryService;

    @Autowired
    private UserService userService;

    @Override
    public void getUserProblemAnalysisResult(UserProblemAnalysisRequest request,
        ProblemAnalysisResponse problemAnalysisResponse) {

        AyUserProblemQueryChainRequest ayUserProblemQueryChainRequest = new AyUserProblemQueryChainRequest();
        ayUserProblemQueryChainRequest.setSellerNick(request.getSellerNick());
        ayUserProblemQueryChainRequest.setAppName(request.getAppName());
        ayUserProblemQueryChainRequest.setStoreId(request.getStoreId());
        ayUserProblemQueryChainRequest.setProblemAnalysisResponse(problemAnalysisResponse);

        ayUserProblemQueryChainService.addProblemQueryChain(ayUserQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(ayNewUserServiceLogQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(aySoldGetLogQueryService);
        ayUserProblemQueryChainService.execute(ayUserProblemQueryChainRequest);

        // 排序，将异常的参数放在集合前面
        paramsSortByParamTag(problemAnalysisResponse);

    }

    @Override
    public void getOrderProblemAnalysisResult(UserProblemAnalysisRequest request,
        ProblemAnalysisResponse problemAnalysisResponse) {
        String topSession =
            userService.getAuthorization(request.getSellerNick(), request.getSellerId(), request.getStoreId(), request.getAppName());

        AyUserProblemQueryChainRequest ayUserProblemQueryChainRequest = new AyUserProblemQueryChainRequest();
        ayUserProblemQueryChainRequest.setSellerNick(request.getSellerNick());
        ayUserProblemQueryChainRequest.setTid(request.getTid());
        ayUserProblemQueryChainRequest.setSellerId(request.getSellerId());
        ayUserProblemQueryChainRequest.setAppName(request.getAppName());
        ayUserProblemQueryChainRequest.setStoreId(request.getStoreId());
        ayUserProblemQueryChainRequest.setProblemAnalysisResponse(problemAnalysisResponse);
        ayUserProblemQueryChainRequest.setTopSession(topSession);

        ayUserProblemQueryChainService.addProblemQueryChain(ayOrderQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(platformFullInfoApiQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(ayTmcSubLogQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(rdsLogQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(mcRouterLogQueryService);
        ayUserProblemQueryChainService.addProblemQueryChain(fullInfoLogQueryService);
        ayUserProblemQueryChainService.execute(ayUserProblemQueryChainRequest);

        // 排序
        paramsSortByParamTag(problemAnalysisResponse);
    }

    /**
     * 对问题结果进行排序优先显示错误结果)
     *
     * @param problemAnalysisResponse
     */
    private void paramsSortByParamTag(ProblemAnalysisResponse problemAnalysisResponse) {
        List<ProblemAnalysisResponse.Param> params = problemAnalysisResponse.getParams();
        ArrayList<ProblemAnalysisResponse.Param> collect =
            params.stream().sorted(Comparator.comparing(ProblemAnalysisResponse.Param::getParamStatusTab))
                .collect(Collectors.toCollection(ArrayList::new));
        problemAnalysisResponse.setParams(collect);
    }
}
