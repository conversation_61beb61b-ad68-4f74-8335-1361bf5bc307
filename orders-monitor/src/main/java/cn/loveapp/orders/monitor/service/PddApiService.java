package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.common.exception.UserNeedAuthException;
import com.taobao.api.ApiException;

import java.time.LocalDateTime;


/**
 * 拼多多api service
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
public interface PddApiService {

	/**
	 * 访问拼多多fullinfo接口, 返回原始数据
	 *
	 * @param nick
	 * @param tid
	 * @param appName
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	String fullinfo(String nick, String tid, String appName) throws UserNeedAuthException, ApiException;

	/**
	 * 访问拼多多soldGet(pdd.order.list.get)接口
	 * @param sellerNick
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	String soldGet(String sellerNick, String appName, LocalDateTime startTime, LocalDateTime endTime);
}
