package cn.loveapp.orders.monitor.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.bo.UserInfoBo;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-26 16:05
 * @Description: 监控平台参数检查工具类
 */
@Component
public class MonitorPlatformRequestParamProcessing {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorPlatformRequestParamProcessing.class);

    @Autowired
    private UserService userService;

    @Autowired
    private MonitorAyTradeSearchESDao monitorAyTradeSearchESDao;

    /**
     * 入参处理
     *
     * @param request
     */
    public void examineParams(UserProblemAnalysisRequest request) {
        String sellerNick = null;
        if (request.getTid() != null) {
            String tid = request.getTid().trim();
            request.setTid(tid);
        }
        // 如果名字是空的则通过订单查出用户名
        if (StringUtils.isEmpty(request.getSellerNick())) {
            try {
                sellerNick = monitorAyTradeSearchESDao.getSellerNameByTid(request.getTid(), request.getStoreId(),
                    request.getAppName());

            } catch (IOException e) {
                LOGGER.logError("通过Tid查询用户名异常", e);
            }
        } else {
            sellerNick = request.getSellerNick().trim();
        }
        request.setSellerNick(sellerNick);

        UserInfoBo userInfoBo =
            userService.getSellerInfoBySellerNick(request.getSellerNick(), request.getStoreId(), request.getAppName());
        if (userInfoBo != null) {
            request.setSellerId(userInfoBo.getSellerId());
            request.setStoreId(userInfoBo.getStoreId());
            if (CommonPlatformConstants.PLATFORM_TAO.equals(request.getStoreId())
                && CommonAppConstants.APP_TRADE.equals(request.getAppName())) {
                request.setAppName(null);
            }
        }
    }

    /**
     * 对私密字符进行加密
     *
     * @param accessToken
     * @return
     */
    public String getEncryptedStr(String accessToken) {
        String prefixStr = accessToken.substring(0, 4);
        String suffixStr = accessToken.substring(accessToken.length() - 6);
        String substring = accessToken.substring(4, accessToken.length() - 6);
        String encryptedStr = substring.replaceAll(substring, "**********");
        return prefixStr + encryptedStr + suffixStr;
    }
}
