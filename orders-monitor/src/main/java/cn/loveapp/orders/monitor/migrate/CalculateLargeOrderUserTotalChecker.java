package cn.loveapp.orders.monitor.migrate;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.UserOrderMigrateProgress;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.monitor.dao.dream.UserOrderMigrateProgressDao;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.service.MonitorUserService;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @program: 计算用户订单数的代码
 * @description:
 * @author: Jason
 * @create: 2019-10-12 10:50
 **/
@Component
public class CalculateLargeOrderUserTotalChecker implements ApplicationRunner {

	private LoggerHelper LOGGER = LoggerHelper.getLogger(CalculateLargeOrderUserTotalChecker.class);

	@Autowired
	private MonitorUserService monitorUserService;

	@Autowired
	private MonitorAyTradeSearchESDao ayTradeSearchDao;

	@Autowired
	private UserOrderMigrateProgressDao userOrderMigrateProgressDao;

	@Value("${calculate.large.order.user.total.pool.size:1}")
	private int poolSize;

	private LocalDateTime startCreatedTime;

	private LocalDateTime endCreatedTime;

	@Value("${calculate.large.order.user.total.created.start:}")
	private void setStartCreatedTime(String startCreate) {
		if (StringUtils.isNotEmpty(startCreate)) {
			try {
				startCreatedTime = LocalDateTime.parse(startCreate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			} catch (Exception e) {
				LOGGER.logError(e.getMessage(), e);
			}
		}
	}

	@Value("${calculate.large.order.user.total.created.end:}")
	private void setEndCreatedTime(String endCreated) {
		if (StringUtils.isNotEmpty(endCreated)) {
			try {
				endCreatedTime = LocalDateTime.parse(endCreated, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			} catch (Exception e) {
				LOGGER.logError(e.getMessage(), e);
			}
		}
	}

	ThreadPoolExecutor executor = null;

	/**
	 * Callback used to run the bean.
	 *
	 * @param args incoming application arguments
	 * @throws Exception on error
	 */
	@Override
	public void run(ApplicationArguments args) throws Exception {
		if (args.containsOption("orders.calculate.switch")) {
			LOGGER.logInfo("beginning...");
			startCalculateUserSpace();
			LOGGER.logInfo("ending....");
		}
	}

	private void startCalculateUserSpace() {
		try {
			LOGGER.logInfo("startTime = " + startCreatedTime);
			LOGGER.logInfo("endTime = " + endCreatedTime);
			executor = new ThreadPoolExecutor(poolSize, poolSize, 5L,
				TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
				new ThreadFactoryBuilder().setNameFormat("calculate-pool-%d").build());
			//1 先查出来梦想库ext表里的所有用户信息,开始一个一个用户计算订单总合
			startGetOrderUserFull();

			executor.shutdown();
			executor.awaitTermination(1, TimeUnit.DAYS);

			LOGGER.logInfo("全部执行完毕");
		} catch (Exception e) {
			LOGGER.logError(" 计算用户空间, 执行异常: " + e.getMessage(), e);
		}
	}

	private void startGetOrderUserFull() {
		LOGGER.logInfo("开始查询ext表中所有用户信息");
		List<UserProductionInfoExt> userProductionInfoExtList = monitorUserService.findAllOrderUser();
		LOGGER.logInfo("用户信息总数为:" + userProductionInfoExtList.size());
		LOGGER.logInfo("开始处理用户迁移数据实体");

		for (UserProductionInfoExt userProductionInfoExt : userProductionInfoExtList) {
			executor.execute(()->{
				String nick = userProductionInfoExt.getSellerNick();
				try {
					LOGGER.logInfo(nick, "", "开始计算迁移数量, dbId - " + userProductionInfoExt.getDbId());
					UserOrderMigrateProgress userOrderMigrateProgress = new UserOrderMigrateProgress();
					userOrderMigrateProgress.setFromDbId(userProductionInfoExt.getDbId());
					userOrderMigrateProgress.setFromSearchDbId(userProductionInfoExt.getSearchdbId());
					userOrderMigrateProgress.setSellerId(userProductionInfoExt.getSellerId());
					userOrderMigrateProgress.setSellerNick(nick);
//					long sellerTotal = ayTradeMainDao.countBySellerIdAndCreated(userProductionInfoExt.getSellerId()
//							, startCreatedTime, endCreatedTime);
//					long subOrerTotal = ayTradeSubOrderDao.countBySellerIdAndCreated(userProductionInfoExt.getSellerId()
//							, startCreatedTime, endCreatedTime);
					long dbTotal = ayTradeSearchDao.countBySellerIdAndCreated(userProductionInfoExt.getSellerId()
						, startCreatedTime, endCreatedTime);
					long subOrerTotal = dbTotal;
					long sellerTotal = dbTotal;

					userOrderMigrateProgress.setOrderTotal(sellerTotal);
					userOrderMigrateProgress.setSubOrderTotal(subOrerTotal);
					userOrderMigrateProgress.setDbOrderTotal(dbTotal);
					batchInsert(userProductionInfoExt.getDbId(), userOrderMigrateProgress);
				} catch (Exception e) {
					LOGGER.logError(nick, "", "用户异常出错啦", e);
				}
			});

		}
	}

	private void batchInsert(Integer dbId, UserOrderMigrateProgress userOrderMigrate) {
		try {
			UserOrderMigrateProgress old = userOrderMigrateProgressDao.queryBySellerId(userOrderMigrate.getSellerId());
			LocalDateTime now = LocalDateTime.now();
			userOrderMigrate.setGmtModified(now);
			if (null == old) {
				userOrderMigrate.setGmtCreate(now);
				userOrderMigrateProgressDao.migrateOrderInsert(userOrderMigrate);
			} else {
				userOrderMigrateProgressDao.migrateOrderUpdate(userOrderMigrate);
			}
		} catch (Exception e) {
			LOGGER.logError(userOrderMigrate.getSellerNick(), userOrderMigrate.getSellerId(),
				"写入数据库异常>dbId为:" + dbId + ", 写入数据为:" + JSON.toJSONString(userOrderMigrate), e);
		}
		LOGGER.logInfo(userOrderMigrate.getSellerNick(), "", "用户迁移数据统计完毕 " + JSON.toJSONString(userOrderMigrate));
	}

}
