package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.orders.monitor.dto.operations.degradation.DegradationLogListResponse;
import cn.loveapp.orders.monitor.entity.operations.DegradationTaskLog;
import cn.loveapp.orders.monitor.dao.dream.DegradationTaskLogDao;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 降级任务日志表(DegradationTaskLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-01 17:25:42
 */
@Service
public class DegradationTaskLogServiceImpl implements DegradationTaskLogService {
	@Autowired
	private DegradationTaskLogDao degradationTaskLogDao;

	/**
	 * 通过taskName数据
	 *
	 * @param taskName 任务名
	 * @return 实例对象
	 */
	@Override
	public DegradationLogListResponse pageDegradationTaskLog(String taskName, Pageable pageable) {
		DegradationLogListResponse degradationLogListResponse = new DegradationLogListResponse();
		degradationLogListResponse.setContent(degradationTaskLogDao.queryByTaskName(taskName, pageable));
		degradationLogListResponse.setTotal(degradationTaskLogDao.queryTotal(taskName));
		return degradationLogListResponse;
	}

	/**
	 * 新增数据
	 *
	 * @param degradationTaskLogDao 实例对象
	 */
	@Override
	public void addDegradationTaskLog(DegradationTaskLog degradationTaskLogDao) {
		this.degradationTaskLogDao.insert(degradationTaskLogDao);
	}
}
