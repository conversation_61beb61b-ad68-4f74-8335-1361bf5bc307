package cn.loveapp.orders.monitor.migrate;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.UserOrderMigrateProgress;
import cn.loveapp.orders.monitor.dao.dream.UserOrderMigrateProgressDao;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: orders-services-group
 * @description: CalculatgeMigrateUserToDbChecker
 * @author: Jason
 * @create: 2019-10-15 16:26
 **/
@Component
public class CalculateMigrateUserToDbChecker implements ApplicationRunner {

	private LoggerHelper LOGGER = LoggerHelper.getLogger(CalculateMigrateUserToDbChecker.class);

	@Autowired
	private UserOrderMigrateProgressDao userOrderMigrateProgressDao;

	/**
	 * Callback used to run the bean.
	 *
	 * @param args incoming application arguments
	 * @throws Exception on error
	 */
	@Override
	public void run(ApplicationArguments args) throws Exception {
		if (args.containsOption("orders.calculate.migreate.switch")) {
			LOGGER.logInfo("beginning...");
			start();
			LOGGER.logInfo("ending");
		}
	}

	private void start() {
		int toDbNum = 14;
		List<UserOrderMigrateProgress> allUser = querAllUsers();
		List<UserOrderMigrateProgress> fromUsers = Lists.newArrayList();
		Map<Integer, Long> eachOrderCountMap = Maps.newTreeMap();

		Map<Integer, Long> toDbCountMap = Maps.newHashMap();
		Map<Integer, Long> fromDbCountMap = Maps.newHashMap();
		for (UserOrderMigrateProgress user : allUser) {
			Integer dbId = user.getFromDbId();
			user.setToDbId(dbId);
			Long orderCount = user.getOrderTotal();
			long sum = 0;
			if (eachOrderCountMap.containsKey(dbId)) {
				sum = eachOrderCountMap.get(dbId);
			}
			eachOrderCountMap.put(dbId, sum + orderCount);

			if(dbId <= 14){
				toDbCountMap.put(dbId, sum + orderCount);
			}else{
				fromDbCountMap.put(dbId, sum + orderCount);
				fromUsers.add(user);
			}
		}

		LOGGER.logInfo("各库订单数: " + eachOrderCountMap);

		LOGGER.logInfo("手工调整目标库订单数");
		for(Map.Entry<Integer, Long> entry: toDbCountMap.entrySet()){
			int distDbId = entry.getKey();
			if(distDbId == 1 || distDbId == 14){
				toDbCountMap.put(distDbId, entry.getValue() + 10000000);
			}else if(distDbId == 10 || distDbId == 11 || distDbId == 12 ){
				toDbCountMap.put(distDbId, entry.getValue() - 10000000);
			}
		}

		long avgOrderCount = (eachOrderCountMap.values().stream().reduce(Long::sum).get()) / toDbNum;

		long range = 500000;
		LOGGER.logInfo("目标平均订单数: " + avgOrderCount + "+"+ range + "=" + (avgOrderCount + range));

		avgOrderCount += range;

//		Map<Integer, List<UserOrderMigrateProgress>> userGroup =
//			allUser.stream().collect(Collectors.groupingBy(UserOrderMigrateProgress::getFromDbId));

		LOGGER.logInfo("对每个库的用户按各自订单数排序");
		fromUsers.sort(Comparator.comparingLong(UserOrderMigrateProgress::getOrderTotal).reversed());

		LOGGER.logInfo("对每个库按总订单数排序");

		List<Map.Entry<Integer, Long>> fromOrderCounts = fromDbCountMap.entrySet().stream()
			.sorted(Comparator.comparing(Map.Entry::getValue)).collect(Collectors.toList());
		LOGGER.logInfo("来源各DB的orderCount: " + fromOrderCounts);

		List<Map.Entry<Integer, Long>> toOrderCounts = toDbCountMap.entrySet().stream()
			.sorted(Comparator.comparing(Map.Entry::getValue)).collect(Collectors.toList());
		LOGGER.logInfo("目标各DB的orderCount: " + toOrderCounts);

		int i=0;
		for(UserOrderMigrateProgress user : fromUsers){
			int fromDbId = user.getFromDbId();
			long userOrderCount = user.getOrderTotal();
			if("end".equals(user.getProgress())){
				continue;
			}

			int count=0;
			while(count <= toOrderCounts.size()){
				count++;
				if(i == toOrderCounts.size()){
					i = 0;
				}
				Map.Entry<Integer, Long> distOrderDb = toOrderCounts.get(i);
				i++;
				int distDbId = distOrderDb.getKey();
				if(distDbId == fromDbId){
					continue;
				}
				long fromOrderCount = fromDbCountMap.get(fromDbId);
				long distOrderCount = distOrderDb.getValue();

				if(distOrderCount >= avgOrderCount){
					continue;
				}
				if(userOrderCount + distOrderCount > avgOrderCount){
					continue;
				}
				user.setToDbId(distDbId);

				fromDbCountMap.put(fromDbId, fromOrderCount - userOrderCount);
				toDbCountMap.put(distDbId, distOrderCount + userOrderCount);

				break;
			}
		}

		LOGGER.logInfo("目标各库订单数: " + toDbCountMap);
		LOGGER.logInfo("来源各库订单数: " + fromDbCountMap);

		Map<Integer, Long> needMigrateOrders = Maps.newTreeMap();
		Map<Integer, Long> needMigrateUsers = Maps.newTreeMap();

		for (UserOrderMigrateProgress user : allUser) {
			Integer dbId = user.getToDbId();
			if(dbId!=null && !user.getFromDbId().equals(dbId)){
				long moveCount = 0;
				if(needMigrateOrders.containsKey(dbId)){
					moveCount = needMigrateOrders.get(dbId);
				}
				needMigrateOrders.put(dbId, moveCount + user.getOrderTotal());

				moveCount = 0;
				if(needMigrateUsers.containsKey(dbId)){
					moveCount = needMigrateUsers.get(dbId);
				}
				needMigrateUsers.put(dbId, moveCount + 1);
			}
			user.setGmtModified(LocalDateTime.now());
		}
		LOGGER.logInfo("结果: 对每个库按总订单数从小到大排序");
		fromOrderCounts = fromDbCountMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getValue)).collect(
			Collectors.toList());
		LOGGER.logInfo("结果: 来源各DB的orderCount: " + fromOrderCounts);

		toOrderCounts = toDbCountMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getValue)).collect(
			Collectors.toList());
		LOGGER.logInfo("结果: 目标各DB的orderCount: " + toOrderCounts);

		LOGGER.logInfo("结果: 各DB需要移入的订单数: " + needMigrateOrders);
		LOGGER.logInfo("结果: 各DB需要移入的用户数: " + needMigrateUsers);

		ThreadPoolExecutor executor = new ThreadPoolExecutor(100, 100, 5L,
			TimeUnit.SECONDS, new SynchronousQueue<>(),
			new ThreadFactoryBuilder().setNameFormat("pool-%d").build(),
			(Runnable r, ThreadPoolExecutor exe) -> {
				if (!exe.isShutdown()) {
					try {
						exe.getQueue().put(r);
					} catch (InterruptedException e) {
						LOGGER.logError("pgExecutor", "-", e.toString(), e);
						Thread.currentThread().interrupt();
					}
				}
			});

		LOGGER.logInfo("开始更新UserOrderMigrateProgress: " + allUser.size());

		for (UserOrderMigrateProgress user : allUser) {
			executor.execute(()->{
				userOrderMigrateProgressDao.migrateOrderUpdate(user);
			});
		}
		try {
			executor.shutdown();
			executor.awaitTermination(1, TimeUnit.DAYS);
		} catch (InterruptedException e) {
		}
		System.exit(0);
	}

	private List<UserOrderMigrateProgress> querAllUsers() {
		long limit = 5000;
		long offet = 0;
		List<UserOrderMigrateProgress> allUser = Lists.newArrayList();
		while (true) {
			List<UserOrderMigrateProgress> result = userOrderMigrateProgressDao.queryAll(limit, offet);
			allUser.addAll(result);
			offet += limit;
			if (result.isEmpty() || result.size() < limit) {
				break;
			}
		}
		return allUser;
	}
}
