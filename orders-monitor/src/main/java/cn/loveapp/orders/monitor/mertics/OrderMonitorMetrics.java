package cn.loveapp.orders.monitor.mertics;

import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.service.MonitorService;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * OrderMonitorMetrics
 *
 * <AUTHOR>
 * @date 2019-04-17
 */
@Component
public class OrderMonitorMetrics implements MeterBinder {
	@Autowired
	private MonitorService monitorService;

	@Autowired
	private MonitorConfig monitorConfig;

	@Override
	public void bindTo(MeterRegistry registry) {
		MonitoringSummaryData data = MonitoringSummaryData.getInstance();
		Gauge.builder("orders_monitor_orderDelay", data, MonitoringSummaryData::getOrderDelayAccuracyRate)
			.register(registry);
		Gauge.builder("orders_monitor_rdsDelay", data, MonitoringSummaryData::getRdsDelayAccuracyRate)
			.register(registry);
		Gauge.builder("orders_monitor_rdsDelay_time", data, MonitoringSummaryData::getRdsDelayMedianTime)
			.register(registry);
		Gauge.builder("orders_monitor_fullinfo", data, MonitoringSummaryData::getFullInfoAccuracyRate)
			.register(registry);
		Gauge.builder("orders_monitor_soldget", data, MonitoringSummaryData::getSoldGetAccuracyRate).register(registry);
		for (int i = 1; i <= monitorConfig.getDbSize(); i++) {
			int dbId = i;
			Gauge.builder("orders_service_status",
				() -> monitorService.isOrderServiceEnabled(dbId, false) ? 1 : 0)
				.tag("dbId", String.valueOf(i)).register(registry);
			Gauge.builder("orders_service_soldget_status",
				() -> monitorService.isOrderServiceEnabled(dbId, true) ? 1 : 0)
				.tag("dbId", String.valueOf(i)).register(registry);
		}

	}
}
