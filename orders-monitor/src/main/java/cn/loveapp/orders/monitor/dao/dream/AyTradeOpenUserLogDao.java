package cn.loveapp.orders.monitor.dao.dream;

import cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 15:37
 * @Description: (ay_trade_open_user_log)开户记录日志表数据访问层
 */
public interface AyTradeOpenUserLogDao {

	/**
	 * 通过用户名获取订购记录
	 * @param sellerNick
	 * @return
	 */
    List<AyTradeOpenUserLog> getAllBySellerNick(@Param("sellerNick") String sellerNick);

}
