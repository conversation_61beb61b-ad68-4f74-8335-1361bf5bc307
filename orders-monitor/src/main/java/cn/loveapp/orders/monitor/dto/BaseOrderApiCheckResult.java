package cn.loveapp.orders.monitor.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * AbstractOrderApiCheckResult
 *
 * <AUTHOR>
 * @date 2019-03-19
 */
@Data
public abstract class BaseOrderApiCheckResult<T, R extends OrderApiCheckFailed> {

	/**
	 * 成功率
	 */
	protected double accuracyRate;

	/**
	 * 订单状态
	 */
	private String orderStatus;

	/**
	 * 丢单情况描述
	 */
	protected OrderMissCheckResult orderMissCheckResult = new OrderMissCheckResult();

	/**
	 * 校验失败的订单
	 */
	protected List<R> failedOrders = Lists.newCopyOnWriteArrayList();

	/**
	 * 添加错误说明
	 *
	 * @param failed
	 */
	public void addFailed(R failed){
		failedOrders.add(failed);
	}

	/**
	 * 获取所有的OrderApiCheckFailed
	 *
	 * @return
	 */
	public List<R> getFaileds(){
		return failedOrders;
	}

	public void sortByDbId(){
		if(!failedOrders.isEmpty()){
			Collections.sort(failedOrders, Comparator.comparingInt(OrderApiCheckFailed::getDbId));
		}
	}

	/**
	 * 创建错误说明对象
	 *
	 * @param sample
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public abstract R createOrderApiCheckFailed(T sample, LocalDateTime startTime, LocalDateTime endTime);

}
