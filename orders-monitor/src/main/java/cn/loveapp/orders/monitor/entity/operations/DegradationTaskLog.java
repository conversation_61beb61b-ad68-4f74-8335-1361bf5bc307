package cn.loveapp.orders.monitor.entity.operations;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 降级任务日志表(Degradation_task_log)实体类
 *
 * <AUTHOR>
 * @since 2022-04-01 17:25:39
 */
@Data
public class DegradationTaskLog implements Serializable {
	private static final long serialVersionUID = 272350528473115380L;

	private long id;
	/**
	 * 任务名
	 */
	private String taskName;
	/**
	 * 任务日志
	 */
	private String content;
	/**
	 * 执行任务时间
	 */
	private LocalDateTime gmtCreate;

}

