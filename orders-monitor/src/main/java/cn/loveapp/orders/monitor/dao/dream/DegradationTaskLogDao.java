package cn.loveapp.orders.monitor.dao.dream;

import cn.loveapp.orders.monitor.entity.operations.DegradationTaskLog;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 降级任务日志表(DegradationTaskLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-01 17:25:38
 */
public interface DegradationTaskLogDao {

	/**
	 * 通过任务名查询数据
	 *
	 * @param taskName
	 * @param pageable
	 * @return
	 */
	List<DegradationTaskLog> queryByTaskName(String taskName, Pageable pageable);

	/**
	 * 新增数据
	 *
	 * @param degradationTaskLog 实例对象
	 * @return 影响行数
	 */
	int insert(DegradationTaskLog degradationTaskLog);

	/**
	 * 获取总条数
	 *
	 * @param taskName
	 * @return
	 */
	int queryTotal(String taskName);

}

