package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.rocketmq.RocketMQDefaultProducerConfig;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.service.MonitorService;
import com.google.common.collect.HashBasedTable;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.impl.factory.MQClientInstance;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.admin.ConsumeStats;
import org.apache.rocketmq.common.protocol.ResponseCode;
import org.apache.rocketmq.common.protocol.body.GroupList;
import org.apache.rocketmq.common.protocol.body.TopicList;
import org.apache.rocketmq.common.protocol.route.BrokerData;
import org.apache.rocketmq.common.protocol.route.TopicRouteData;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.apache.rocketmq.tools.admin.DefaultMQAdminExt;
import org.apache.rocketmq.tools.admin.DefaultMQAdminExtImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * OrderOnsCheckServiceImpl
 *
 * <AUTHOR>
 * @date 2018-12-18
 */
@Service
public class OnsOrderMonitorChecker extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsOrderMonitorChecker.class);
	public static final String ONS_IYSAVER = "iysaverdsorders";

	@Autowired
	private MonitorService monitorService;

	@Autowired
	private MeterRegistry registry;

	@Autowired
	public MonitorConfig monitorConfig;

	@Autowired
	public RocketMQDefaultProducerConfig rocketMQDefaultProducerConfig;

	private DefaultMQAdminExt defaultMQAdminExt;

	private MQClientInstance mqClientInstance;

	protected ThreadPoolExecutor threadPool = null;

	protected HashBasedTable<String, String, Double> diffs = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> delays = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> tpses = HashBasedTable.create();

	@Override
	public CheckerType type() {
		return CheckerType.ONS_ORDER;
	}

	@Override
	public String name() {
		return "ons堆积";
	}

	@Override
	protected boolean enableDebug() {
		return true;
	}

	@PostConstruct
	public void init() {
		try {
			threadPool = new ThreadPoolExecutor(20, 20, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
				new ThreadFactoryBuilder().setNameFormat(name() + "-pool-%d").build(),
				(Runnable r, ThreadPoolExecutor executor) -> {
					if (!executor.isShutdown()) {
						try {
							executor.getQueue().put(r);
						} catch (InterruptedException e) {
							LOGGER.logError(e.toString(), e);
							Thread.currentThread().interrupt();
						}
					}
				});
			threadPool.allowCoreThreadTimeOut(true);
		} catch (Exception e) {
			LOGGER.logError(e.getMessage(), e);
		}
	}

	@Override
	protected synchronized String innerCheck() {
		if(defaultMQAdminExt == null){
			createDefaultMQAdminExt();
		}
		try {
			TopicList topicList = defaultMQAdminExt.fetchAllTopicList();
			for (String topic : topicList.getTopicList()) {
				try {
					GroupList groupList = defaultMQAdminExt.queryTopicConsumeByWho(topic);
					for (String group : groupList.getGroupList()) {
						if (isStoped) {
							return null;
						}
						threadPool.execute(() -> {
							try {
								ConsumeStats consumeStats = examineConsumeStats(group, topic);
								double diffTotal = consumeStats.computeTotalDiff();
								double tps = consumeStats.getConsumeTps();
								double delay = tps == 0 ? 0 : diffTotal / tps;
								if (diffTotal > 0 && tps == 0) {
									delay = -1;
								}
								diffs.put(topic, group, diffTotal);
								tpses.put(topic, group, tps);
								delays.put(topic, group, delay);

								Gauge.builder("monitor_ons2_diff", MonitoringSummaryData.getInstance(), x -> diffs.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_ons2_tps", MonitoringSummaryData.getInstance(), x -> tpses.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_ons2_delay", MonitoringSummaryData.getInstance(), x -> delays.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

							} catch (MQClientException e) {
								if(e.getResponseCode() == ResponseCode.CONSUMER_NOT_ONLINE) {
//									LOGGER.logError("topic=" + topic + " group=" + group + ", consumer不在线");
								}else if(e.getResponseCode() == ResponseCode.TOPIC_NOT_EXIST){
//									LOGGER.logError("topic=" + topic + " group=" + group + ", topic不存在");
								}else{
									LOGGER.logError(e.getMessage(), e);
								}
							} catch (Exception e) {
								LOGGER.logError(e.getMessage(), e);
							}
						});
					}
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
			}
		} catch (Exception e) {
			LOGGER.logError("监控ONS2堆积信息失败", e);
		}
		return null;
	}

	private synchronized void createDefaultMQAdminExt() {
		if(defaultMQAdminExt == null){
			try {
				defaultMQAdminExt = new DefaultMQAdminExt();
				defaultMQAdminExt.setInstanceName("admin_ext_group_order");
				defaultMQAdminExt.setNamesrvAddr(monitorConfig.getOnsOrderNamesrvAddr());
				defaultMQAdminExt.start();


				Field field = ReflectionUtils.findField(DefaultMQAdminExt.class, "defaultMQAdminExtImpl");
				field.setAccessible(true);
				DefaultMQAdminExtImpl
					defaultMQAdminExtImpl = (DefaultMQAdminExtImpl)ReflectionUtils.getField(field, defaultMQAdminExt);

				field = ReflectionUtils.findField(DefaultMQAdminExtImpl.class, "mqClientInstance");
				field.setAccessible(true);
				mqClientInstance = (MQClientInstance)ReflectionUtils.getField(field, defaultMQAdminExtImpl);
			} catch (Exception e) {
				e.printStackTrace();
				LOGGER.logError("监控ONS2堆积信息失败", e);
			}
		}
	}

	private ConsumeStats examineConsumeStats(String consumerGroup,
		String topic) throws RemotingException, MQClientException,
		InterruptedException, MQBrokerException {
		String retryTopic = MixAll.getRetryTopic(consumerGroup);
		TopicRouteData topicRouteData = defaultMQAdminExt.examineTopicRouteInfo(retryTopic);
		ConsumeStats result = new ConsumeStats();

		for (BrokerData bd : topicRouteData.getBrokerDatas()) {
			HashMap<Long, String> brokerAddrs = bd.getBrokerAddrs();
			if(brokerAddrs == null || brokerAddrs.isEmpty()){
				continue;
			}
			for(String addr : brokerAddrs.values()){
				if (addr != null) {
					ConsumeStats consumeStats =
						mqClientInstance.getMQClientAPIImpl().getConsumeStats(addr, consumerGroup, topic, 5000 * 3);
					result.getOffsetTable().putAll(consumeStats.getOffsetTable());
					double value = result.getConsumeTps() + consumeStats.getConsumeTps();
					result.setConsumeTps(value);
				}
			}
		}

		if (result.getOffsetTable().isEmpty()) {
			throw new MQClientException(ResponseCode.CONSUMER_NOT_ONLINE,
				"Not found the consumer group consume stats, because return offset table is empty, maybe the consumer not consume any message");
		}

		return result;
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		super.onApplicationEvent(event);
		if (!isStoped) {
			return;
		}
		if (defaultMQAdminExt != null) {
			try {
				defaultMQAdminExt.shutdown();
			} catch (Exception e) {
			}
		}
	}

}
