package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 14:29
 * @Description: 用户问题查询链实现类
 */
@Service
public class AyUserProblemQueryChainServiceImpl {
    private ArrayList<AyUserProblemQueryService> problemQueryChainList;

    public AyUserProblemQueryChainServiceImpl() {
        problemQueryChainList = new ArrayList<AyUserProblemQueryService>();
    }

    /**
     * 添加任务查询链
     *
     * @param ayUserProblemQueryService
     */
    public void addProblemQueryChain(AyUserProblemQueryService ayUserProblemQueryService) {
        problemQueryChainList.add(ayUserProblemQueryService);
    }

    /**
     * 任务查询链入口
     *
     * @param request
     * @return
     */
    public AyUserProblemQueryChainRequest execute(AyUserProblemQueryChainRequest request) {
        List<AyUserProblemQueryService> ayUserProblemQueryServices = new ArrayList<>();
        ayUserProblemQueryServices.addAll(problemQueryChainList);
        problemQueryChainList.clear();
        AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO =
            new AyUserProblemQueryChainDTO(request, ayUserProblemQueryServices, 0);
        return ayUserProblemQueryChainDTO.proceed(request);
    }
}
