package cn.loveapp.orders.monitor.dto.operations;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-08-02 14:26
 * @Description: 管理用户登录响应体
 */
@Data
public class PlatformPowerUserInfoResponse {
    /**
     * 是否登录成功
     */
    private Boolean isLoginSuccess;

    /**
     * 用户级别
     */
    private Integer userLevel;

    /**
     * 用户账号是否启用
     */
    private Boolean enable;

    public static PlatformPowerUserInfoResponse of(Boolean isLoginSuccess, Integer userLevel, Boolean enable) {
        PlatformPowerUserInfoResponse response = new PlatformPowerUserInfoResponse();
        response.setIsLoginSuccess(isLoginSuccess);
        response.setUserLevel(userLevel);
        response.setEnable(enable);
        return response;
    }
}
