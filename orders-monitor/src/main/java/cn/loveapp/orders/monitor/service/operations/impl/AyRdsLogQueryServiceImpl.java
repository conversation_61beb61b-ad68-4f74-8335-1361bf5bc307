package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.config.operations.MonitorPlatformBaseConfig;
import cn.loveapp.orders.monitor.constant.operations.LogEsIndexConstant;
import cn.loveapp.orders.monitor.constant.operations.ParamStatusTagConstant;
import cn.loveapp.orders.monitor.dao.es.MonitorAyLogSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 14:16
 * @Description: 爱用RDS日志查询服务接口
 */
@Service
public class AyRdsLogQueryServiceImpl implements AyUserProblemQueryService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyRdsLogQueryServiceImpl.class);

    @Autowired
    private MonitorAyLogSearchESDao monitorAyLogSearchESDao;

    @Autowired
    private MonitorPlatformBaseConfig monitorPlatformBaseConfig;

    @Override
    public AyUserProblemQueryChainRequest logQueryDispose(AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO) {
        AyUserProblemQueryChainRequest request = ayUserProblemQueryChainDTO.ayUserProblemQueryChainRequest();

        String sellerNick = request.getSellerNick();
        String tid = request.getTid();
        String storeId = request.getStoreId();
        Date searchLogStartTime = request.getSearchLogStartTime();
        String platformStatus = request.getPlatformStatus();
        ProblemAnalysisResponse problemAnalysisResponse = request.getProblemAnalysisResponse();
        ProblemAnalysisResponse.Param param;
        // 开始查询rds平台的日志
        LOGGER.logInfo(sellerNick, tid, "开始查询Rds-Consumer日志");
        if (monitorPlatformBaseConfig.getOpenRdsPlatforms().contains(storeId)) {

            List<String> rdsConsumerMessages = monitorAyLogSearchESDao.getLogMessageList(sellerNick,
                Lists.newArrayList(sellerNick, tid, platformStatus), searchLogStartTime,
                LogEsIndexConstant.getRdsConsumerIndexPrefix(storeId));
            LOGGER.logInfo(sellerNick, tid, "Rds-Consumer中日志查询结果为: " + JSON.toJSONString(rdsConsumerMessages));
            if (rdsConsumerMessages.size() > 0) {
                param = new ProblemAnalysisResponse.Param("Rds-Consumer", "收到状态为: " + platformStatus + "的消息推送",
                    ParamStatusTagConstant.CORRECTTAG);
                problemAnalysisResponse.getParams().add(param);
            } else {
                param = new ProblemAnalysisResponse.Param("Rds-Consumer日志", "没有收到状态为: " + platformStatus + "的消息推送",
                    ParamStatusTagConstant.ERRORTAG);
                problemAnalysisResponse.getParams().add(param);
            }

        }
        return ayUserProblemQueryChainDTO.proceed(request);
    }
}
