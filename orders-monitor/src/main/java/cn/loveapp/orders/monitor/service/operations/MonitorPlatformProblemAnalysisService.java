package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-08 14:27
 * @Description: 运维可视化问题分析服务接口
 */
public interface MonitorPlatformProblemAnalysisService {

	/**
	 * 获取用户问题分析结果
	 * @param request
	 * @param problemAnalysisResponse
	 */
	void getUserProblemAnalysisResult(UserProblemAnalysisRequest request,
        ProblemAnalysisResponse problemAnalysisResponse);

	/**
	 * 获取订单问题分析结果
	 * @param request
	 * @param problemAnalysisResponse
	 */
	void getOrderProblemAnalysisResult(UserProblemAnalysisRequest request,
        ProblemAnalysisResponse problemAnalysisResponse);
}
