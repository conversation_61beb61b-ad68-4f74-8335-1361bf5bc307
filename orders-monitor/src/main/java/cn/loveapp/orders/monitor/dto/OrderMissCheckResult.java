package cn.loveapp.orders.monitor.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 丢单校验结果描述
 *
 * <AUTHOR>
 * @date 2019-01-05
 */
@Data
public class OrderMissCheckResult {
	private AtomicLong totalCount = new AtomicLong();

	private List<String> reasons = Lists.newArrayList();

	public void increment(long add){
		totalCount.getAndAdd(add);
	}
}
