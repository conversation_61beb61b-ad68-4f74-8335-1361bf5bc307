package cn.loveapp.orders.monitor.config.operations;

import cn.loveapp.common.utils.LoggerHelper;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-10 20:41
 * @Description: 日志ES存储库配置类
 */
@Configuration
public class AyLogESConfig {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyLogESConfig.class);

    @Autowired
    private MonitorPlatformBaseConfig monitorPlatformBaseConfig;

    public RestHighLevelClient elasticsearchClient() {
        String username = monitorPlatformBaseConfig.getUsername();
        String password = monitorPlatformBaseConfig.getPassword();
        String[] urls = monitorPlatformBaseConfig.getUrls().stream().toArray(String[]::new);
        final ClientConfiguration clientConfiguration =
            ClientConfiguration.builder().connectedTo(urls).withBasicAuth(username, password).build();
        return RestClients.create(clientConfiguration).rest();
    }
}
