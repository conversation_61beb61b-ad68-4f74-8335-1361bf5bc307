package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.dto.purchase.request.SearchPurchaseRequest;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * 采购单处理service
 *
 * @program: orders-services-group
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/4/26 15:14
 **/
public interface PurchaseService {

    /**
     * 采购单导出excel
     * 
     * @param request
     */
    void exportToExcel(SearchPurchaseRequest request, HttpServletResponse response)
        throws ApiServerErrorException, IOException;

}
