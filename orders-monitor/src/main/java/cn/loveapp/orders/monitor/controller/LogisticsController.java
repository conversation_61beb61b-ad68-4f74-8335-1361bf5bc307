package cn.loveapp.orders.monitor.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.monitor.dto.purchase.request.LogisticsSendRequest;
import com.alibaba.fastjson.JSON;
import com.kdniao.api.domain.LogisticsData;
import com.kdniao.api.domain.LogisticsRequestData;
import com.kdniao.api.internal.util.KdniaoUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.util.Map;

/**
 * 物流相关工具
 *
 * <AUTHOR>
 * @Date 2023/6/30 12:14
 */
@RestController
@RequestMapping("monitor/logistics")
public class LogisticsController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsController.class);

    @Autowired
    private DefaultMQProducer producer;


    @Value("${monitor.logistics.kdniao.appId:xxx}")
    private String appId;

    @Value("${monitor.logistics.kdniao.appKey:xxx}")
    private String appKey;

    @Value("${monitor.logistics.kdniao.sendUrl:https://mcaliyun.aiyongtech.com/mcsub/receiverPush/kdniao/logistics}")
    private String sendUrl;

    /**
     * 手动发送物流轨迹(快递鸟)
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "sendKdniaoLogistics", method = RequestMethod.POST)
    public CommonApiResponse<Object> sendKdniaoLogistics(@RequestBody LogisticsSendRequest request,
                                                                HttpServletResponse response) {
        try {
            if (CollectionUtils.isEmpty(request.getData())) {
                return CommonApiResponse.failed(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message());
            }
            LOGGER.logInfo("开始发送轨迹" + JSON.toJSONString(request));
            return CommonApiResponse.success(sendLogisticsKdniao(request.getData()));
        } catch (Exception e) {
            LOGGER.logError("发送失败:" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiResponse.serverError().getCode(), e.getMessage(), e);
        }
    }



    private String sendLogisticsKdniao(List<LogisticsData> data) {
        LogisticsRequestData logisticsRequestData = new LogisticsRequestData();
        logisticsRequestData.setPushTime(LocalDateTime.now());
        logisticsRequestData.setEBusinessId(appId);
        logisticsRequestData.setCount(String.valueOf(data.size()));
        logisticsRequestData.setData(data);

        try {
            String requestData = JSON.toJSONString(logisticsRequestData);
            String dataSign = KdniaoUtils.encrypt(requestData, appKey);

            HashMap<String, String> postFields = new HashMap<>();
            postFields.put("RequestData", requestData);
            postFields.put("DataSign", dataSign);
            postFields.put("RequestType", "102");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            return NetworkUtil.http(sendUrl, postFields, true, null, null, false, false, null, headers);
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return null;
    }

}
