package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * OrderService
 *
 * <AUTHOR>
 * @date 2019-01-12
 */
public interface MonitorUserService {

	/**
	 * 获取所有开通订单服务的用户
	 *
	 * @return
	 */
	List<UserProductionInfoExt> findAllOrderUser();
}
