package cn.loveapp.orders.monitor.repairer;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.constant.OrderPullRdsTypeConstant;
import cn.loveapp.orders.common.proto.PullRdsOrdersRefundRequestProto;
import cn.loveapp.orders.common.proto.PullRdsOrdersRequestProto;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import com.alibaba.druid.pool.DruidDataSource;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * soldget修补淘宝待发货订单不匹配的用户订单
 *
 * <AUTHOR>
 * @date 2021/3/10
 */
@Service
public class TaoRdsOrderRepairer implements ApplicationRunner, ApplicationListener<ContextClosedEvent>, Ordered {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoRdsOrderRepairer.class);

	private final static String NAME = "淘宝RDS订单修复，";
	/**
	 * 是否已停止
	 */
	private volatile boolean stopped;

	/**
	 * 是否已开始
	 */
	private volatile boolean started;

	@Autowired(required = false)
	private DefaultMQProducer ordersPullSoldGetONSProducer;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	/**
	 * rds推送库扫描起始时间
	 */
	@Value("${orders.repairer.tao-rds-order.pull-start-time:2021-03-09 00:00:00}")
	private String pullStartTimeStr;

	/**
	 * rds推送库扫描结束时间
	 */
	@Value("${orders.repairer.tao-rds-order.pull-end-time:2021-03-09 00:01:00}")
	private String pullEndTimeStr;

	/**
	 * rds推送库数量
	 */
	@Value("${orders.repairer.tao-rds-order.rds-count:1}")
	private int rdsCount;

	/**
	 * rds推送库扫描pageSize
	 */
	@Value("${orders.repairer.tao-rds-order.page-size:1}")
	private int pageSize;

	/**
	 * 是否是扫描退款信息
	 */
	@Value("${orders.repairer.tao-rds-order.refund:false}")
	private boolean pullRefund;

	private volatile double rdsLimitValue = 1.0;
	private List<RateLimiter> rateLimiters = new CopyOnWriteArrayList<>();

	@Value("${orders.repairer.tao-rds-order.limit:1}")
	private void listenLimit(double limit) {
		rdsLimitValue = limit;
		for (RateLimiter rateLimiter : rateLimiters) {
			rateLimiter.setRate(limit);
		}
	}

	private ThreadPoolExecutor executor;

	@Override
	public void run(ApplicationArguments args) throws Exception {
		if (!args.containsOption("orders.repairer.tao-rds-order.switch")) {
			return;
		}
		CompletableFuture.runAsync(this::start);
	}

	private void start() {
		started = true;

		executor = new ThreadPoolExecutor(rdsCount, rdsCount, 5L,
			TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
			new ThreadFactoryBuilder().setNameFormat("fixRdsOrder-pool-%d").build());

		boolean isRefund = pullRefund;
		String tableName = isRefund ? "jdp_tb_refund" : "jdp_tb_trade";
		String fields = isRefund ? "seller_nick,tid,oid,modified" : "seller_nick,tid,modified";

		for(int i=1; i<=rdsCount; i++){
			RateLimiter rateLimiter = RateLimiter.create(rdsLimitValue);
			rateLimiters.add(rateLimiter);

			Config config = ConfigService.getConfig("orders-push-" + i);

			String topic = config.getProperty("orders.taobao.ons.vip.rds.topic", "");
			String tag = config.getProperty("orders.taobao.ons.vip.rds.tag", "");

			DruidDataSource dataSource = new DruidDataSource();
			dataSource.setUrl(config.getProperty("loveapp.datasources.rdsDataSource.url", ""));
			dataSource.setDriverClassName("com.mysql.jdbc.Driver");
			dataSource.setUsername(config.getProperty("loveapp.datasources.rdsDataSource.username", ""));
			dataSource.setPassword(config.getProperty("loveapp.datasources.rdsDataSource.password", ""));
			dataSource.setMinIdle(0);
			dataSource.setInitialSize(0);
			dataSource.setMaxActive(1);

			JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

			String fullName = "rds" + i + "." + NAME;

			executor.execute(()->{

				int allOrdersCount = jdbcTemplate.queryForObject("SELECT count(*) from " + tableName + " where jdp_modified >= ? and jdp_modified <= ?", Integer.class, pullStartTimeStr, pullEndTimeStr);
				int progress = 0;

				LOGGER.logInfo(fullName + "总订单数: " + allOrdersCount);
				if(allOrdersCount == 0){
					return;
				}
				LocalDateTime pullStartTime = DateUtil.parseString(pullStartTimeStr);
				LocalDateTime pullEndTime = DateUtil.parseString(pullEndTimeStr);


				LocalDateTime startTime = pullStartTime;

				Pageable pageable = PageRequest.of(0, pageSize);
				while (startTime.isBefore(pullEndTime) && !stopped){
					List<Map<String, Object>> orders = null;
					LocalDateTime endTime = startTime.plusSeconds(4);
					try {
						orders = jdbcTemplate.queryForList("SELECT " + fields
							+ " from " + tableName + " where jdp_modified >= ? and jdp_modified < ? order by jdp_modified limit ? OFFSET ?",
							startTime, endTime, pageable.getPageSize(), pageable.getOffset());

						if(CollectionUtils.isEmpty(orders)){
							continue;
						}
						for (Map<String, Object> order : orders) {
							rateLimiter.acquire();
							sendRdsOnsMsg(order, topic, tag, isRefund);
							progress++;
							if(progress % 100 == 0){
								LOGGER.logInfo(fullName + "当前进度 " + progress + "/" + allOrdersCount + ", start=" + startTime + " end=" + endTime);
							}
						}
					} catch (Exception e){
						LOGGER.logError(fullName + ", 异常: " + e.getMessage(), e);
					} finally {
						if(orders != null && orders.size() < pageable.getPageSize()){
							startTime = startTime.plusSeconds(4);
							pageable = pageable.first();
							LOGGER.logInfo(fullName + "下一段: " + "start=" + startTime + " end=" + endTime + ", offset=" + pageable.getOffset());
						}else{
							pageable = pageable.next();
							LOGGER.logInfo(fullName + "下一页: " + "start=" + startTime + " end=" + endTime + ", offset=" + pageable.getOffset());
						}
					}
				}
				LOGGER.logInfo(fullName + "修补结束");
			});
		}



	}

	protected void sendRdsOnsMsg(Map<String, Object> order, String topic, String tag, boolean isRefund) {
		try {
			String sellerNick = order.get("seller_nick").toString();
			String tid = order.get("tid").toString();
			LocalDateTime modified = ((Timestamp)order.get("modified")).toLocalDateTime();
            Map<String, String> properties = new HashMap<>();
			if(isRefund){
				String oid = order.get("oid").toString();
				PullRdsOrdersRefundRequestProto pullRdsOrdersRequestProto = new PullRdsOrdersRefundRequestProto();
				pullRdsOrdersRequestProto.setTid(tid);
				pullRdsOrdersRequestProto.setOid(oid);
				pullRdsOrdersRequestProto.setSellerNick(sellerNick);
				pullRdsOrdersRequestProto.setModified(modified);
                properties.put(OrderPullRdsTypeConstant.RDS_TYPE, pullRdsOrdersRequestProto.getRdsType());
				rocketMqQueueHelper.push(topic, tag, pullRdsOrdersRequestProto, ordersPullSoldGetONSProducer, 0, properties);
			}else{
				PullRdsOrdersRequestProto pullRdsOrdersRequestProto = new PullRdsOrdersRequestProto();
				pullRdsOrdersRequestProto.setTid(tid);
				pullRdsOrdersRequestProto.setSellerNick(sellerNick);
				pullRdsOrdersRequestProto.setModified(modified);
                properties.put(OrderPullRdsTypeConstant.RDS_TYPE, pullRdsOrdersRequestProto.getRdsType());
				rocketMqQueueHelper.push(topic, tag, pullRdsOrdersRequestProto, ordersPullSoldGetONSProducer, 0, properties);
			}
		} catch (Exception e) {
			LOGGER.logError(topic, "", NAME + "发送消息失败: " + e.getMessage(), e);
		}
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		if (!started) {
			return;
		}
		LOGGER.logInfo(NAME + "应用退出");
		stopped = true;
		try {
			if(executor != null){
				executor.shutdown();
				executor.awaitTermination(1, TimeUnit.MINUTES);
			}
		} catch (Exception ignored) {
		}
		LOGGER.logInfo(NAME + "应用退出完毕");
		started = false;
	}

	@Override
	public int getOrder() {
		return Ordered.LOWEST_PRECEDENCE;
	}
}
