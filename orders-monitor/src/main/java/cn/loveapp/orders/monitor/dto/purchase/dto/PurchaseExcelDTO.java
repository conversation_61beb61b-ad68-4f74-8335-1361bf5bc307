package cn.loveapp.orders.monitor.dto.purchase.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 采购单列表Excel转换DTO
 *
 * @program: orders-services-group
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/4/26 15:24
 **/
@Data
public class PurchaseExcelDTO {

    @ExcelProperty(value = "采购单Id", index = 0)
    private String purchaseId;

    @ExcelProperty(value = "三方单号", index = 1)
    private String cpTid;

    @ExcelProperty(value = "三方来源分销商平台", index = 2)
    private String sourceStoreId;

    @ExcelProperty(value = "爱用采购单状态Code", index = 3)
    private String purchaseAyStatusCode;

    @ExcelProperty(value = "爱用采购单状态", index = 4)
    private String purchaseAyStatusName;

    @ExcelProperty(value = "鸿源采购单状态Code", index = 5)
    private Integer purchaseHyStatusCode;

    @ExcelProperty(value = "鸿源采购单状态", index = 6)
    private String purchaseHyStatusName;

    @ExcelProperty(value = "三方平台订单状态Code", index = 7)
    private String cpStatusCode;

    @ExcelProperty(value = "三方平台订单状态", index = 8)
    private String cpStatusName;

    @ExcelProperty(value = "订单创建时间", index = 9)
    private String createTime;

    /**
     * 是否异常订单
     */
    @ExcelIgnore
    private boolean isError = false;

}
