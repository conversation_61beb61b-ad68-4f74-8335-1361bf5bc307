package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.api.ApiException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 订单正确率校验
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Service
public abstract class AbstractOrderApiMonitorChecker<T> extends AbstractConcurrentCheckService<T> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractOrderApiMonitorChecker.class);
	public static final String TRADE = "trade";
	public static final String TITLE = "title";
	public static final String BODY = "body";

	protected ThreadPoolExecutor apiThreadPool = null;
	protected RateLimiter rateLimiter = null;

	@PostConstruct
	private void init() {
		rateLimiter = RateLimiter.create(monitorConfig.getApiCheckLimit());
		apiThreadPool =
			new ThreadPoolExecutor(getPoolSize(), getPoolSize(), 2L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000),
				new ThreadFactoryBuilder().setNameFormat(name() + "-api-pool-%d").build());
		apiThreadPool.allowCoreThreadTimeOut(true);

	}

	/**
	 * api 返回json的根节点名称
	 *
	 * @return
	 */
	protected abstract String getResultJsonRoot();


	/**
	 * 返回 校验的订单所属平台
	 * @return
	 */
	protected abstract String getStoreId();

	/**
	 * api校验结果对象
	 *
	 * @return
	 */
	protected abstract BaseOrderApiCheckResult getOrderApiCheckResult();

	/**
	 * 需要按照json校验的string类型节点
	 *
	 * @return
	 */
	protected abstract Set<String> checkAsJsonStringKeys();

	/**
	 * 请求订单service api
	 *
	 * @param sample
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws ApiServerErrorException
	 * @throws IOException
	 */
	protected abstract String requestOrderServiceApi(T sample, LocalDateTime startTime, LocalDateTime endTime)
		throws ApiServerErrorException, IOException;

	/**
	 * 请求订单淘宝api
	 *
	 * @param sample
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	protected abstract String requestPlatformApi(T sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException;

	/**
	 * 是否需要继续比较json结构
	 *
	 * @param sample
	 * @param failedOrder
	 * @param taobaoApiTime
	 * @param apiJson
	 * @param taobaoJson
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	protected abstract boolean needCompareJson(T sample, OrderApiCheckFailed failedOrder, LocalDateTime taobaoApiTime,
		JSONObject apiJson, JSONObject taobaoJson, LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 获取要忽略的key
	 *
	 * @return
	 */
	protected Set<String> getNeedIgnoreKeys(){
		return SetUtils.EMPTY_SET;
	}

	@Override
	public boolean checkSample(T sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException {
		if(isStoped){
			return true;
		}
		rateLimiter.acquire();
		final boolean[] result = new boolean[] {true};
		final String[] apiResponse = new String[] {""};
		OrderApiCheckFailed failedOrder =
			getOrderApiCheckResult().createOrderApiCheckFailed(sample, startTime, endTime);
		try {
			JSONObject apiJson;
			Future<JSONObject> apiJsonFuture = apiThreadPool
				.submit(() -> getOrderApiJsonObject(sample, startTime, endTime, result, apiResponse, failedOrder));

			LocalDateTime taoApiTimeMinute = LocalDateTime.now().withSecond(0).withNano(0);
			String taobaoResponse;
			try {
				taobaoResponse = requestPlatformApi(sample, startTime, endTime);
			} catch (UserNeedAuthException | ApiException e) {
				try {
					apiJsonFuture.get();
				} catch (Exception ex) {
				}
				throw e;
			}
			try {
				apiJson = apiJsonFuture.get();
			} catch (Exception e) {
				error(sample, e.getMessage(), e);
				decrementValidNumber();
				return result[0] = true;
			}
			if (apiJson == null) {
				return result[0];
			}
			JSONObject taobaoJson = JSON.parseObject(taobaoResponse);

			if (!taobaoJson.containsKey(getResultJsonRoot())) {
				error(sample,
					"查询" + getStoreId() +"Api失败, startTime " + startTime + " endTime " + endTime + " 请求失败: " + taobaoResponse);
				throw new UserNeedAuthException(500, "查询TaoApi失败, 请求失败: " + taobaoResponse);
			}
			if (apiJson.containsKey(getResultJsonRoot())) {
//				String requestId = apiJson.getJSONObject(getResultJsonRoot()).getString("request_id");
//				if (StringUtils.isNotEmpty(requestId)) {
//					//是service调用淘宝api返回的响应
//					getOrderApiCheckResult().getTaoApiRequestCount().increment();
//				}
				if (!needCompareJson(sample, failedOrder, taoApiTimeMinute, apiJson, taobaoJson, startTime, endTime)) {
					return result[0] = true;
				}
			}
			//比较json
			if (!compareJson(StringUtils.EMPTY, taobaoJson, apiJson, sample, failedOrder)) {
				// 重试一次, 减少错误
				OrderApiCheckFailed tmpFailedOrder =
					getOrderApiCheckResult().createOrderApiCheckFailed(sample, startTime, endTime);
				apiJson = getOrderApiJsonObject(sample, startTime, endTime, result, apiResponse, tmpFailedOrder);
				if (!needCompareJson(sample, tmpFailedOrder, taoApiTimeMinute, apiJson, taobaoJson, startTime,
					endTime)) {
					return result[0] = true;
				}
				if (!compareJson(StringUtils.EMPTY, taobaoJson, apiJson, sample, tmpFailedOrder)) {
					error(sample,
						"验证失败 startTime " + startTime + " endTime " + endTime + "\r\n " + getStoreId() + "Api返回: \r\n" + taobaoResponse
							+ " \r\n OrderApi返回: \r\n" + apiResponse[0]);
					result[0] = ignoreOnlyMOdifiedError(failedOrder);
					return result[0];
				}
			}
		} finally {
			if (!result[0]) {
				getOrderApiCheckResult().addFailed(failedOrder);
			}
		}
		return true;
	}

	private boolean ignoreOnlyMOdifiedError(OrderApiCheckFailed failedOrder){
		Map<String, List<String>> diff = failedOrder.getDiff();
		String modified = "modified";
		boolean onlyModified = diff != null && diff.size() == 1 && diff.containsKey(modified);
		//只有modified不同时不算失败
		if(diff == null || onlyModified){
			return true;
		}else{
			return false;
		}
	}

	protected JSONObject getOrderApiJsonObject(T sample, LocalDateTime startTime, LocalDateTime endTime, boolean[] result,
		String[] apiResponse, OrderApiCheckFailed failedOrder) {
		try {
			apiResponse[0] = requestOrderServiceApi(sample, startTime, endTime);
			JSONObject jsonObject = JSON.parseObject(apiResponse[0]);
			Integer code = jsonObject.getInteger("code");
			if (code != null && CommonApiStatus.Success.code() != code) {
				// 业务错误, 跳过校验
				warn(sample, "code不为200, api返回了失败结果, 跳过校验: " + apiResponse[0]);
				decrementValidNumber();
				result[0] = true;
				return null;
			}
			String subCode = jsonObject.getString("sub_code");
			if (StringUtils.isNotBlank(subCode)) {
				// 用户topsession失效等错误, 跳过校验
				warn(sample, "sub_code不为空, api返回了失败结果, 跳过校验: " + apiResponse[0]);
				decrementValidNumber();
				result[0] = true;
				return null;
			}
			if (!jsonObject.containsKey(getResultJsonRoot()) && jsonObject.containsKey(BODY)) {
				return jsonObject.getJSONObject(BODY);
			} else {
				return jsonObject;
			}
		} catch (JSONException e) {
			error(sample,
				"验证失败, startTime " + startTime + " endTime " + endTime + " OrderApi响应非json数据: " + apiResponse[0], e);
			failedOrder.addDiffProperty("api响应", "响应非json数据");
			result[0] = false;
		} catch (ApiServerErrorException e) {
			error(sample,
				"验证失败, startTime " + startTime + " endTime " + endTime + " OrderApi服务器响应异常: " + e.getMessage(), e);
			failedOrder.addDiffProperty("api响应", "响应异常: " + e.getMessage());
			result[0] = false;
		} catch (Exception e) {
			error(sample, "查询OrderApi失败 startTime " + startTime + " endTime " + endTime, e);
			decrementValidNumber();
			result[0] = true;
		}
		return null;
	}

	/**
	 * 比较json中两个值是否相等
	 *
	 * @param key
	 * @param platformValue
	 * @param nowValue
	 * @param sample
	 * @param failedOrder
	 * @return
	 */
	private boolean compareJson(String key, Object platformValue, Object nowValue, T sample,
		OrderApiCheckFailed failedOrder) {
		boolean result = true;
		boolean needRecord = true;
		try {
			Set<String> ignoreKeys = getNeedIgnoreKeys();
			if(ignoreKeys.contains(key)){
				//忽略字段不校验
				return true;
			}

			if (platformValue != nowValue) {
				if (platformValue == null) {
					if(nowValue instanceof String) {
						result = StringUtils.isEmpty((String)nowValue);
					}
					//属性不同
					result = false;
				} else if (nowValue == null) {
					if (platformValue instanceof String) {
						result = StringUtils.isEmpty((String)platformValue);
					}
					//属性不同
					result = false;
				} else if (platformValue instanceof Map && nowValue instanceof Map) {
					//map对比
					needRecord = false;
					Map<String, Object> src = (Map<String, Object>)platformValue;
					Map<String, Object> dist = (Map<String, Object>)nowValue;
					for (Map.Entry<String, Object> entry : src.entrySet()) {
						if (ignoreKeys.contains(entry.getKey())){
							continue;
						}
						if (TRADE.equalsIgnoreCase(key) && TITLE.equalsIgnoreCase(entry.getKey())) {
							//根节点的title不校验
							continue;
						}
						Object srcEntryValue = entry.getValue();
						Object distEntryValue = dist.get(entry.getKey());

						if (!dist.containsKey(entry.getKey())) {
							if(srcEntryValue instanceof String && StringUtils.isEmpty(srcEntryValue.toString())){
								// 空字符串不算缺失
								continue;
							}else if(srcEntryValue instanceof Boolean && !((Boolean)srcEntryValue)){
								// false不算缺失
								continue;
							}else if(srcEntryValue instanceof Collection && CollectionUtils.isEmpty((Collection)srcEntryValue)){
								continue;
							}else if(srcEntryValue instanceof Map && MapUtils.isEmpty((Map)srcEntryValue)){
								continue;
							}
							error(sample, key + "缺少节点 " + entry.getKey());
							failedOrder.addDiffProperty(key, "缺少节点 " + entry.getKey());
							//缺少属性
							result = false;
						} else {
							//比较子属性
							result = compareJson(entry.getKey(), srcEntryValue, distEntryValue, sample, failedOrder) && result;
						}
					}
				} else if (platformValue instanceof List && nowValue instanceof List) {
					//List对比
					needRecord = false;
					List<Object> src = (List<Object>)platformValue;
					List<Object> dist = (List<Object>)nowValue;
					if (src.size() != dist.size()) {
						result = false;
						error(sample, "节点 " + key + " 的长度不相同, platform: " + ((List)platformValue).size() + "  now: " + ((List<Object>)nowValue).size());
						failedOrder.addDiffProperty(key,
							"长度不相同, platform: " + ((List)platformValue).size() + "  now: " + ((List<Object>)nowValue).size());
						if(src.size() > 0 && src.get(0) instanceof String || dist.size() > 0 && dist.get(0) instanceof String){
							for (int i = 0; i < Math.max(src.size(), dist.size()); i++) {
								compareJson(key + "[" + i + "]",
									src.size() > i ? src.get(i) : null,
									dist.size() > i ? dist.get(i) : null,
									sample,
									failedOrder);
							}
						}
					} else {
						for (int i = 0; i < src.size(); i++) {
							//比较子属性
							result = compareJson(key + "[" + i + "]", src.get(i), dist.get(i), sample, failedOrder);
						}
					}
				} else if (checkAsJsonStringKeys().contains(key) && platformValue instanceof String && nowValue instanceof String) {
					//按照json来校验string节点
					result = compareJson(key, JSON.parseObject((String)platformValue), JSON.parseObject((String)nowValue), sample, failedOrder);
				} else if (platformValue instanceof String && nowValue instanceof String) {
					// 忽略特殊字符的差异
					String srcStr = (String)platformValue;
					String distStr = (String)nowValue;
					srcStr = srcStr.replace("＜", "<").replace("＞", ">");
					distStr = distStr.replace("＜", "<").replace("＞", ">");
					result = StringUtils.trim(srcStr).equals(StringUtils.trim(distStr));
					if (!result){
						try {
							Double distStrDouble = Double.parseDouble(distStr);
							Double srcStrDouble = Double.parseDouble(srcStr);
							result = distStrDouble.equals(srcStrDouble);
						} catch (NumberFormatException e) {
						}
					}
				} else if (platformValue instanceof String && platformValue.equals(Objects.toString(nowValue))
					|| nowValue instanceof String && nowValue.equals(Objects.toString(platformValue)) ) {
					result = true;
				} else if (!platformValue.equals(nowValue)) {
					result = false;
				}
			}
		} finally {
			if (!result && needRecord) {
				String srcType = platformValue == null ? "null" : platformValue.getClass().getSimpleName();
				String distType = nowValue == null ? "null" : nowValue.getClass().getSimpleName();

				String diff =
					"platform(" + srcType + "): \r\n" + StringUtils.substring(Objects.toString(platformValue, "null"), 0, 200) + "  \r\nnow("
						+ distType + "): \r\n" + StringUtils.substring(Objects.toString(nowValue, "null"), 0, 200);
				error(sample, "节点 " + key + " 不相同, " + diff);
				failedOrder.addDiffProperty(key, diff);
			}
		}
		return result;
	}

	/**
	 * error日志
	 *
	 * @param sample
	 * @param message
	 * @param e
	 */
	protected abstract void error(T sample, String message, Throwable e);

	/**
	 * error日志
	 *
	 * @param sample
	 * @param message
	 */
	protected abstract void error(T sample, String message);

	/**
	 * info日志
	 *
	 * @param sample
	 * @param message
	 */
	protected abstract void info(T sample, String message);

	/**
	 * warn日志
	 *
	 * @param sample
	 * @param message
	 */
	protected abstract void warn(T sample, String message);

}
