package cn.loveapp.orders.monitor.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.monitor.dto.grafana.GrafanaRequest;
import cn.loveapp.orders.monitor.service.GrafanaFormatConversionService;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-13 11:36
 * @Description: Grafana监控告警控制接口
 */
@Validated
@RestController
@RequestMapping("monitor/grafana")
public class GrafanaNotificationController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(GrafanaNotificationController.class);

	@Autowired
	GrafanaFormatConversionService grafanaFormatConversionService;

	@RequestMapping(value = "kubernetesAlarm", method = RequestMethod.POST)
	public CommonApiResponse<String> kubernetesAlarm(@RequestBody GrafanaRequest request) {
		LOGGER.logInfo("k8s告警请求体: " + JSON.toJSONString(request));
		grafanaFormatConversionService.convertAndSendByKubernetesAlarm(request);
		return CommonApiResponse.success();
	}

	@RequestMapping(value = "monitoringAlarm", method = RequestMethod.POST)
	public CommonApiResponse<String> monitoringAlarm(@RequestBody GrafanaRequest request) {
		LOGGER.logInfo("monitoring告警请求体: " + JSON.toJSONString(request));
		grafanaFormatConversionService.convertAndSendByMonitoringAlarm(request);
		return CommonApiResponse.success();
	}

	@RequestMapping(value = "logAlarm", method = RequestMethod.POST)
	public CommonApiResponse<String> logAlarm(@RequestBody GrafanaRequest request) {
		LOGGER.logInfo("log告警请求体: " + JSON.toJSONString(request));
		grafanaFormatConversionService.convertAndSendByLogAlarm(request);
		return CommonApiResponse.success();
	}

    @RequestMapping(value = "alarm/{hookId}", method = RequestMethod.POST)
    public CommonApiResponse<String> alarm(@PathVariable String hookId, @RequestBody GrafanaRequest request) {
        LOGGER.logInfo("通用告警请求体: hookId=" + hookId + " request=" + JSON.toJSONString(request));
        grafanaFormatConversionService.convertAndSendByAlarm(hookId, request);
        return CommonApiResponse.success();
    }
}
