package cn.loveapp.orders.monitor.dao.es;

import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.validation.constraints.NotEmpty;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.document.DocumentField;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.elasticsearch.ElasticsearchConfiguration;
import cn.loveapp.orders.common.constant.EsFields;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.dao.es.CommonAyTradeSearchESDao;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import io.micrometer.core.instrument.util.DoubleFormat;
/**
 * MonitorAyTradeSearchESDao
 *
 * <AUTHOR>
 * @date 2020/6/22
 */
@Primary
@Repository
public class MonitorAyTradeSearchESDao extends CommonAyTradeSearchESDao {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorAyTradeSearchESDao.class);

	private static final String INDEX_PRE = "ay_trade_search_index-";

	public MonitorAyTradeSearchESDao(ElasticsearchOperations operations, RestHighLevelClient client, ResultsMapper mapper,
		ElasticsearchConfiguration elasticsearchConfiguration) {
		super(operations, client, mapper, elasticsearchConfiguration);
	}

	public List<SearchHit> queryByGmtModified(String indexName, LocalDateTime start, LocalDateTime end, int page, int pageSize) throws IOException {
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		sourceBuilder.query(QueryBuilders.rangeQuery(EsFields.gmtModified).gte(start.format(formatter)).lt(end.format(formatter)));
		sourceBuilder.sort(new FieldSortBuilder(EsFields.gmtModified).order(SortOrder.ASC));
		sourceBuilder.from(page * pageSize);
		sourceBuilder.size(pageSize);
		sourceBuilder.timeout(new TimeValue(5, TimeUnit.SECONDS));
		sourceBuilder.version(true);

		SearchRequest searchRequest = new SearchRequest();
		searchRequest.indices(indexName);
		searchRequest.source(sourceBuilder);
		SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
//		LOGGER.logInfo(sourceBuilder.toString());
		return Lists.newArrayList(response.getHits().getHits());
	}

    public int checkVersion(String indexName, List<SearchHit> searchHits, RestHighLevelClient targetClient) throws IOException {
        if(CollectionUtils.isEmpty(searchHits)){
            return 0;
        }

        Map<String, Long> check = new HashMap<>();

        for(SearchHit searchHit : searchHits){
            check.put(searchHit.getId(), searchHit.getVersion());
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(termsQuery("id", check.keySet())).size(1000).version(true);
        SearchRequest request = new SearchRequest(indexName);
        request.source(searchSourceBuilder);

        SearchResponse response = targetClient.search(request, RequestOptions.DEFAULT);

        int conflictCount = 0;
        for (SearchHit itemResponse : response.getHits().getHits()) {
            if(!check.get(itemResponse.getId()).equals(itemResponse.getVersion())){
                conflictCount++;
                LOGGER.logInfo("conflict id=" + itemResponse.getId()+ " sourceVersion=" + check.get(itemResponse.getId()) + ", version=" + itemResponse.getVersion());
            }
        }
        if(searchHits.size() > conflictCount){
            LOGGER.logInfo("bulk length=" + searchHits.size() + ",  版本更新个数: " + (searchHits.size() - conflictCount));
        }
        return searchHits.size();
    }

	public int bulkIndex(String indexName, List<SearchHit> searchHits, RestHighLevelClient targetClient, String logPrefix) throws IOException {
		if(CollectionUtils.isEmpty(searchHits)){
			return 0;
		}
		BulkRequest request = new BulkRequest();
		request.timeout(TimeValue.timeValueMinutes(1));
		for(SearchHit searchHit : searchHits){
			DocumentField routing = searchHit.getFields().get("_routing");
			IndexRequest indexRequest = new IndexRequest(indexName)
				.id(searchHit.getId())
				.routing(routing.getValue())
				// 设置版本, 老数据覆盖
				.version(searchHit.getVersion())
				.versionType(VersionType.EXTERNAL)
				.source(searchHit.getSourceRef(), XContentType.JSON);
			request.add(indexRequest);
		}
		BulkResponse response = targetClient.bulk(request, RequestOptions.DEFAULT);

		int conflictCount = 0;

        String lastId = null;
		for (BulkItemResponse itemResponse : response.getItems()) {
			if (!itemResponse.isFailed()) {
                lastId = itemResponse.getId() + " " + itemResponse.getVersion();
				continue;
			}
			if(itemResponse.getFailure().getStatus() == RestStatus.CONFLICT){
				conflictCount++;
			}else{
				throw new IOException(logPrefix + "批量插入失败: " + response.buildFailureMessage());
			}
		}
		if(searchHits.size() > conflictCount){
			LOGGER.logInfo(logPrefix + "bulk length=" + searchHits.size() + " size=" + DoubleFormat.decimal(request.estimatedSizeInBytes()/1024.0/1024.0) + "MB,  版本更新个数: " + (searchHits.size() - conflictCount) + ", lastId=" + lastId);
		}
		return searchHits.size();
	}

	/**
	 * 根据sellerId和created查询订单数
	 *
	 * @param sellerId
	 * @param startCreated
	 * @param endCreated
	 * @return
	 */
	public long countBySellerIdAndCreated(String sellerId, LocalDateTime startCreated, LocalDateTime endCreated){
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
			.mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true))
			.must(QueryBuilders.termsQuery(EsFields.sellerId, sellerId))
			.must(QueryBuilders.rangeQuery(EsFields.created)
				.gte(minuteSecondFormatter.format(startCreated)).lt(minuteSecondFormatter.format(endCreated)));
		builder.withSearchType(SearchType.QUERY_THEN_FETCH).withQuery(boolQueryBuilder);
		AyTradeSearchES es = new AyTradeSearchES();
		es.setSellerId(sellerId);
		return count(es, builder.build());
	}

	/**
	 * 查询指定时间内的订单数据(seller_nick, seller_id, corp_id, tid, list_id)
	 *
	 * @param startTime 查询起始时间
	 * @param endTime   查询结束时间
	 * @param status    订单状态
	 * @param limit
	 * @return 对象列表
	 */
	public List<AyTradeSearchES> queryOrdersByTime(LocalDateTime startTime, LocalDateTime endTime,String status, String storeId, int page, int limit){
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
			.mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true))
			.must(QueryBuilders.termsQuery(EsFields.storeId, storeId))
			.must(QueryBuilders.rangeQuery(EsFields.created)
				.gte(minuteSecondFormatter.format(startTime)).lt(minuteSecondFormatter.format(endTime)))
			.mustNot(QueryBuilders.existsQuery(EsFields.mergeTradeStatus));

		if(StringUtils.isNotEmpty(status) && !status.equals("ALL")){
			boolQueryBuilder.must(QueryBuilders.termsQuery(EsFields.taoStatus, status));
		}
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
		builder.withSearchType(SearchType.QUERY_THEN_FETCH);
		builder.withIndices(AyTradeSearchES.INDEX_NAME_PREFIX + "*");
		builder.withPageable(PageRequest.of(page, limit));
		builder.withFields(EsFields.sellerNick, EsFields.sellerId, EsFields.corpId, EsFields.tid, EsFields.storeId, EsFields.appName, EsFields.created);
		builder.withQuery(boolQueryBuilder);
		builder.withSort(SortBuilders.fieldSort(EsFields.created).order(SortOrder.DESC));

		return operations.queryForPage(builder.build(), AyTradeSearchES.class).toList();
	}

	/**
	 * 查询卖家指定时间内的订单数量(最多一千)
	 *
	 * @param startTime  查询起始时间
	 * @param endTime    查询结束时间
	 * @param status     订单状态
	 * @param sellerId	 卖家id
	 * @return 订单数量
	 */
	public long countOrdersBySellerId(LocalDateTime startTime, LocalDateTime endTime, String status, String sellerId,
		String storeId, String appName){
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
			.mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true))
			.must(QueryBuilders.termsQuery(EsFields.sellerId, sellerId))
			.must(QueryBuilders.termsQuery(EsFields.storeId, storeId))
			.must(QueryBuilders.rangeQuery(EsFields.created)
				.gte(minuteSecondFormatter.format(startTime)).lt(minuteSecondFormatter.format(endTime)))
			.mustNot(QueryBuilders.existsQuery(EsFields.mergeTradeStatus));

		if(StringUtils.isNotEmpty(status) && !status.equals("ALL")){
			boolQueryBuilder.must(QueryBuilders.termsQuery(EsFields.taoStatus, status));
		}
		appendAppNameQuery(boolQueryBuilder, appName);
		NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
            .withSearchType(SearchType.QUERY_THEN_FETCH)
            .withQuery(boolQueryBuilder);

		return count(AyTradeSearchES.of(sellerId, sellerId, storeId, appName), builder.build());
	}

	/**
	 * 查询待发货订单
	 * @param start
	 * @param end
	 * @param page
	 * @param pageSize
	 * @return
	 * @throws IOException
	 */
	public List<SearchHit> queryWaitSellerSendGoodsByGmtModified(LocalDateTime start, LocalDateTime end, int page, int pageSize) throws IOException {
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
		boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.taoStatus, TaobaoStatusConstant.WAIT_SELLER_SEND_GOODS));
		boolQueryBuilder.must(QueryBuilders.rangeQuery(EsFields.gmtModified).gte(start.format(formatter)).lt(end.format(formatter)));
		sourceBuilder.query(boolQueryBuilder);
		sourceBuilder.sort(new FieldSortBuilder(EsFields.gmtModified).order(SortOrder.ASC));
		sourceBuilder.from(page * pageSize);
		sourceBuilder.size(pageSize);
		sourceBuilder.timeout(new TimeValue(5, TimeUnit.SECONDS));
		sourceBuilder.version(true);

		SearchRequest searchRequest = new SearchRequest();
		searchRequest.indices(AyTradeSearchES.INDEX_NAME_PREFIX + "*");
		searchRequest.source(sourceBuilder);
		SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
		return Lists.newArrayList(response.getHits().getHits());
	}

    /**
     * 根据tid查出卖家昵称
     *
     * @param tid
     * @param storeId
     * @param appName
     * @return
     */
    public String getSellerNameByTid(@NotEmpty String tid, String storeId, String appName) throws IOException {
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.tid, tid))
            .must(QueryBuilders.termQuery(EsFields.storeId, storeId));
        NativeSearchQueryBuilder queryBuilder = nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
        queryBuilder.withIndices(AyTradeSearchES.INDEX_NAME_PREFIX + "*");
        Page<AyTradeSearchES> ayTradeSearchES = operations.queryForPage(queryBuilder.build(), AyTradeSearchES.class);
        if (ayTradeSearchES != null && ayTradeSearchES.getContent().size() > 0) {
            String sellerNick = ayTradeSearchES.getContent().get(0).getSellerNick();
            return sellerNick;
        }
        return null;
    }

    /**
     * 返回查询数量
     *
     * @param queryBuilder
     * @return
     */
    public long countByBuilder(BoolQueryBuilder queryBuilder) {
        // 总订单数
        NativeSearchQueryBuilder builder =
            new NativeSearchQueryBuilder().withSearchType(SearchType.QUERY_THEN_FETCH).withQuery(queryBuilder);
        return count(builder.build());
    }

    /**
     * 根据条件滚动查询
     *
     * @param boolQueryBuilder
     * @param scrollId
     * @param batchSize
     * @param fields
     * @return
     */
    public ScrolledPage<AyTradeSearchES> scrollQueryByBuilder(BoolQueryBuilder boolQueryBuilder, String scrollId,
        int batchSize, String[] fields) {

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withSearchType(SearchType.QUERY_THEN_FETCH).withIndices(getIndexName(null, null))
            .withFields(fields).withPageable(PageRequest.of(0, batchSize)).withQuery(boolQueryBuilder);
        ScrolledPage<AyTradeSearchES> result = null;
        if (StringUtils.isBlank(scrollId)) {
            result = startScroll(queryBuilder.build(), AyTradeSearchES.class);
        } else {
            // 持续 scroll 直到查询出所有的数据
            result = continueScroll(scrollId, AyTradeSearchES.class);
        }
        return result;
    }

    /**
     * 查询出sellerId 和 sellerNick不匹配的订单
     * @param sellerId
     * @param sellerNick
     * @param storeId
     * @return
     */
    public List<AyTradeSearchES> querySellerIdNotMatchSellerNickTradeList(String sellerId, String sellerNick, String storeId) {

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.storeId, storeId))
            .mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true));

        boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.sellerId, sellerId))
            .mustNot(QueryBuilders.termQuery(EsFields.sellerNick, sellerNick));

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQueryBuilder queryBuilder = nativeSearchQueryBuilder.withQuery(boolQueryBuilder);

        queryBuilder.withPageable(PageRequest.of(0, 200));
        queryBuilder.withSearchType(SearchType.QUERY_THEN_FETCH);
        queryBuilder.withIndices(AyTradeSearchES.INDEX_NAME_PREFIX + "*");
        queryBuilder.withFields(EsFields.tid, EsFields.storeId, EsFields.appName, EsFields.sellerId, EsFields.sellerNick, EsFields.mergeTid);

        return operations.queryForList(queryBuilder.build(), AyTradeSearchES.class);
    }


    public long countByMergeTid(String mergeTid, String storeId) {

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.storeId, storeId))
            .mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true));
        boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.mergeTid, mergeTid));

        return countByBuilder(boolQueryBuilder);
    }
}
