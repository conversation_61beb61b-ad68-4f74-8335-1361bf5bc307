package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.dto.operations.degradation.DegradationLogListResponse;
import cn.loveapp.orders.monitor.entity.operations.DegradationTaskLog;
import org.springframework.data.domain.Pageable;

/**
 * 降级任务日志表(DegradationTaskLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-01 17:25:41
 */
public interface DegradationTaskLogService {

	/**
	 * 通过name进行分页查询
	 *
	 * @param taskName
	 * @param pageable
	 * @return
	 */
	DegradationLogListResponse pageDegradationTaskLog(String taskName, Pageable pageable);

	/**
	 * 新增日志
	 *
	 * @param degradationTaskLog 实例对象
	 */
	void addDegradationTaskLog(DegradationTaskLog degradationTaskLog);
}
