package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.monitor.entity.OrderSearchTrade;
import cn.loveapp.orders.monitor.dao.dream.OrderSearchTradeDao;
import cn.loveapp.orders.monitor.service.OrderSearchTradeService;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单记录基本信息(TaobaoOrderSearchTrade)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-23 16:26:10
 */
@Service
public class OrderSearchTradeServiceImpl implements OrderSearchTradeService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderSearchTradeServiceImpl.class);

	private static final String TABLE_NAME = "_order_search_";

	@Resource
	private OrderSearchTradeDao OrderSearchTradeDao;

	/**
	 * 获取订单信息
	 *
	 * @param nick
	 * @param sellerId
	 * @param storeId
	 * @param appName
	 * @return
	 */
    @Override
    public OrderSearchTrade getOrderSearchInfo(String nick, String sellerId, String storeId, String appName) {
        try {
            if (CommonPlatformConstants.PLATFORM_DOUDIAN.equals(storeId)) {
                return OrderSearchTradeDao.queryBySellerId(getStoreTableName(storeId, appName), sellerId);
            }
            return OrderSearchTradeDao.queryByNick(getStoreTableName(storeId, appName), nick);
        } catch (Exception e) {
            LOGGER.logInfo(nick, appName, "订单记录表不存在, " + e.getMessage());
        }

        return null;
    }

    @Override
    public List<OrderSearchTrade> getAllOrderSearchBySellerNick(String sellerNick, String sellerId, String storeId,
        String appName) {
        if (CommonPlatformConstants.PLATFORM_DOUDIAN.equals(storeId)) {
            return OrderSearchTradeDao.queryListBySellerId(sellerId, getStoreTableName(storeId, appName));
        }
        return OrderSearchTradeDao.queryListBySellerNick(sellerNick, getStoreTableName(storeId, appName));
    }

	/**
	 * 判断数据来源storeId
	 *
	 * @param storeId
	 * @return
	 */
    public String getStoreTableName(String storeId, String appName) {
        appName = OrderUtil.defaultAppName(storeId, appName, CommonAppConstants.APP_TRADE);
        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            return "taobao" + TABLE_NAME + appName.toLowerCase();
        } else {
            return storeId.toLowerCase() + TABLE_NAME + appName.toLowerCase();
        }
    }
}
