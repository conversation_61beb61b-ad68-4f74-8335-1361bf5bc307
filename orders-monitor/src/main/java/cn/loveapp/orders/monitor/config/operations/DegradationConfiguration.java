package cn.loveapp.orders.monitor.config.operations;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;

import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;

import cn.loveapp.common.autoconfigure.db.CommonDataSourcesProperties;
import lombok.Data;

/**
 * 降级接口连接配置类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-07 12:13
 */
@Data
@Configuration
@EnableConfigurationProperties({DegradationProperties.class, CommonDataSourcesProperties.class})
public class DegradationConfiguration {
	@Autowired
	DegradationProperties degradationProperties;

	@Autowired
	@Qualifier("userInfoStringRedisTemplate")
	private StringRedisTemplate userRedisTemplate;

	@Autowired
	@Qualifier("stringRedisTemplate")
	private StringRedisTemplate orderRedisTemplate;

	@Autowired
	private CommonDataSourcesProperties commonDataSourcesProperties;

	@Autowired
	@Qualifier("dreamDataSource")
	private DataSource druidDataSource;


	@Bean
	public ApolloOpenApiClient apolloOpenApiClient() {
		String portalUrl = degradationProperties.getApollo().getPortalUrl();
		String token = degradationProperties.getApollo().getToken();
		return ApolloOpenApiClient.newBuilder()
			.withPortalUrl(portalUrl)
			.withToken(token)
			.build();
	}

	@Bean
	public JdbcTemplate getDataSource() {
		return new JdbcTemplate(druidDataSource);
	}

	public StringRedisTemplate getUserRedisTemplate() {
		return userRedisTemplate;
	}

	public StringRedisTemplate getOrderRedisTemplate() {
		return orderRedisTemplate;
	}
}
