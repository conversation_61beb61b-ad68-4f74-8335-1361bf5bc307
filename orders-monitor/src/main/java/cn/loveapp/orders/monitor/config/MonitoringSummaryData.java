package cn.loveapp.orders.monitor.config;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import lombok.Getter;
import lombok.Setter;

import java.io.IOException;
import java.lang.reflect.Type;
import java.text.DecimalFormat;

/**
 * 监控汇总数据
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
@Getter
@Setter
public class MonitoringSummaryData {
	private static MonitoringSummaryData instance = new MonitoringSummaryData();

	@JSONField(name = "fullinfo合格率", serializeUsing = Double2StringSerizlizer.class)
	private volatile double fullInfoAccuracyRate = -0.1;

	@JSONField(name = "soldGet合格率", serializeUsing = Double2StringSerizlizer.class)
	private volatile double soldGetAccuracyRate = -0.1;

	@JSONField(name = "order延时合格率", serializeUsing = Double2StringSerizlizer.class)
	private volatile double orderDelayAccuracyRate = -0.1;

	@JSONField(name = "rds推送延时合格率", serializeUsing = Double2StringSerizlizer.class)
	private volatile double rdsDelayAccuracyRate = -0.1;

	@JSONField(name = "rds推送延时中位时间")
	private volatile double rdsDelayMedianTime = -0.1;

	private MonitoringSummaryData(){}

	public static MonitoringSummaryData getInstance() {
		return instance;
	}

	public static class Double2StringSerizlizer implements ObjectSerializer {
		@Override
		public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features)
			throws IOException {
			if (object == null) {
				serializer.writeNull();
				return;
			}
			serializer.write(new DecimalFormat("#.####%").format(object));
		}
	}
}
