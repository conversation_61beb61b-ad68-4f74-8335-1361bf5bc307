package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 15:59
 * @Description: (ay_trade_open_user_log)开户记录日志表数据访问接口
 */
public interface AyTradeOpenUserLogService {

    /**
     * 获取用户开户日志记录
     *
     * @param sellerNick
     * @return
     */
    List<AyTradeOpenUserLog> getAllOpenUserLogBySellerNick(String sellerNick);
}
