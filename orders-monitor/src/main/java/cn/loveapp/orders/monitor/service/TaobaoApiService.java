package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.common.exception.UserNeedAuthException;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.response.TradeFullinfoGetResponse;

import java.time.LocalDateTime;

/**
 * 淘宝api service
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
public interface TaobaoApiService {
	/**
	 * 访问淘宝fullinfo接口, 返回原始数据
	 *
	 * @param nick
	 * @param tid
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	String fullinfo(String nick, long tid) throws UserNeedAuthException, ApiException;

	/**
	 * 访问淘宝fullinfo接口, 返回序列化对象
	 *
	 * @param topSession
	 * @param tid
	 * @param fields
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	TradeFullinfoGetResponse fullinfoForObject(String topSession, long tid, String ...fields) throws ApiException;

	/**
	 * 访问淘宝soldGet接口
	 *
	 * @param nick
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	String soldGet(String nick, LocalDateTime startTime, LocalDateTime endTime) throws UserNeedAuthException, ApiException;

	/**
	 * 分页访问淘宝soldGet接口
	 *
	 * @param nick
	 * @param startTime
	 * @param endTime
	 * @param pageNo 页数
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	String soldGet(String nick, LocalDateTime startTime, LocalDateTime endTime, long pageNo) throws UserNeedAuthException, ApiException;

}
