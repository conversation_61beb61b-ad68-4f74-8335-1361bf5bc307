package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.api.request.SoldGetRequest;
import cn.loveapp.orders.common.api.response.SoldGetResponse;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.dto.SoldGetCheckResult;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.OrdersApiService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Sets;
import com.taobao.api.ApiException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 多平台soldGet正确率校验
 *
 * <AUTHOR>
 * @date 2021/4/16
 */
@Service
public class MultiSoldGetMonitorChecker extends AbstractOrderApiMonitorChecker<UserProductionInfoExt> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MultiSoldGetMonitorChecker.class);
	public static final String JSON_ROOT = "body";

	@Autowired
	private TradeApiPlatformHandleService tradeApiPlatformService;

	@Autowired
	@Qualifier("ordersApiServiceImpl")
	private OrdersApiService ordersApiService;

	@Autowired
	private UserService userService;

	protected SoldGetCheckResult soldGetCheckResult;

	protected volatile String currentStoreId;

	@Override
	public CheckerType type() {
		return CheckerType.MULTI_SOLD_GET;
	}

	@Override
	public String name() {
		return "multi soldGet";
	}

	@Override
	protected int getSampleTableCount() {
		return 1;
	}

	@Override
	protected String getResultJsonRoot() {
		return JSON_ROOT;
	}

	protected String getCurrentStoreId() {
		return currentStoreId;
	}

	protected void setCurrentStoreId(String storeId) {
		this.currentStoreId = storeId;
	}

	@Override
	protected BaseOrderApiCheckResult getOrderApiCheckResult() {
		return soldGetCheckResult;
	}

	@Override
	protected Set<String> checkAsJsonStringKeys(){
		return Sets.newHashSet();
	}

	@Override
	public void check() {
		for(String platformId : monitorConfig.getMultiSoldGetPlatforms()){
			setCurrentStoreId(platformId);
			super.check();
		}
	}

	@Override
	protected synchronized String innerCheck() {
		double accuracyRate;
		soldGetCheckResult = new SoldGetCheckResult();
		soldGetCheckResult.setOrderStatus(monitorConfig.getMultiSoldGetStatus());

		accuracyRate = checkSampleInTimeRange(monitorConfig.getMultiSoldGetTimeRange(), null,
			monitorConfig.getSoldGetNumber());

		MonitoringSummaryData.getInstance().setSoldGetAccuracyRate(accuracyRate);

		if (!isStoped) {
			postCheck();
		}

		return String.valueOf(accuracyRate);
	}

	@Override
	protected String requestOrderServiceApi(UserProductionInfoExt sample, LocalDateTime startTime,
		LocalDateTime endTime) throws ApiServerErrorException, IOException {
		String platformId = sample.getStoreId();
        long pageSize = NumberUtils.toLong(monitorConfig.getMultiSoldGetPageSizes().get(platformId));
		String sort = monitorConfig.getMultiSoldGetSorts().get(platformId);
		String appName = OrderUtil.defaultAppName(platformId, sample.getAppName());
		String response = ordersApiService
			.searchList(sample.getSellerId(), sample.getSellerNick(), "tid", soldGetCheckResult.getOrderStatus(),
				startTime, endTime, 1, pageSize, sort, platformId, appName);

		JSONObject responseJson = JSON.parseObject(response);
		if(String.valueOf(CommonApiStatus.ServerError.code()).equals(responseJson.getString("code"))){
			return response;
		}
		JSONObject tradeListResponse = (JSONObject)JSONPath.eval(responseJson, "$.body.tradeListResponse");
		if(tradeListResponse == null){
			throw new ApiServerErrorException("访问订单服务失败: " + response);
		}
		long totalResults = tradeListResponse.getLongValue("total_results");
		JSONObject root = new JSONObject();
		JSONObject body = new JSONObject();
		body.put("totalResults", totalResults);
		if(totalResults > 0){
			JSONArray trades = new JSONArray();
			for (Object ob : tradeListResponse.getJSONObject("trades").getJSONArray("trade")) {
				trades.add(((JSONObject)ob).get("tid"));
			}
			trades.sort(Comparator.comparing(Object::toString));
			body.put("trades", trades);
		}
		root.put("body", body);
		return root.toJSONString();
	}

	@Override
	protected String requestPlatformApi(UserProductionInfoExt sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException {
		String platformId = getCurrentStoreId();
		String appName = OrderUtil.defaultAppName(platformId, sample.getAppName());
		String sellerNick = sample.getSellerNick();
		String topSession = userService.getAuthorization(sellerNick, sample.getSellerId(), platformId, appName);
		if(StringUtils.isEmpty(topSession)){
			throw new UserNeedAuthException(500, "获取topSession失败");
		}
		long pageSize = NumberUtils.toLong(monitorConfig.getMultiSoldGetPageSizes().get(platformId));
		String status = TaobaoStatusConstant.transformToPlatformOrderStatus(platformId, soldGetCheckResult.getOrderStatus());
		String specifySoldGetStatus = monitorConfig.getMultiSpecifySoldGetStatus().get(platformId);
		if (specifySoldGetStatus != null) {
			// 覆盖默认的status配置
			status = specifySoldGetStatus;
		}

		SoldGetRequest request = new SoldGetRequest();
		request.setPageNo(1L);
		request.setPageSize(pageSize);
		request.setStartCreated(startTime);
		request.setEndCreated(endTime);
		request.setOrderStatus(status);
		request.setRefundStatus(TaobaoStatusConstant.getPlatformOrderListRefundStatusAll(platformId));
		request.setApiFileds("tid");
		request.setUseHasNext(false);
		request.setTopSession(topSession);

		SoldGetResponse response = tradeApiPlatformService.soldGet(request, sellerNick, platformId, appName);
		if(!response.isSuccess()){
			throw new ApiException(response.getErrorCode(), response.getMsg(), response.getSubCode(), response.getSubMsg());
		}
		JSONObject root = new JSONObject();
		JSONObject body = new JSONObject();
		Long totalResults = null;
		if (response.getTotalResults().isPresent()) {
			totalResults = response.getTotalResults().get();
		}
		body.put("totalResults", totalResults);
		if(totalResults != null && totalResults > 0){
			JSONArray trades = new JSONArray();
			for (AyTrade ayTrade : response.getTrades()) {
				trades.add(ayTrade.getTid() == null ? ayTrade.getTidStr() : ayTrade.getTid().toString());
			}
			trades.sort(Comparator.comparing(Object::toString));
			body.put("trades", trades);
		}
		root.put("body", body);
		return root.toJSONString();
	}

	@Override
	protected boolean needCompareJson(UserProductionInfoExt sample, OrderApiCheckFailed failedOrder,
		LocalDateTime taobaoApiTime, JSONObject apiJson, JSONObject taobaoJson, LocalDateTime startTime,
		LocalDateTime endTime) {
		return true;
	}

	protected void postCheck() {
		soldGetCheckResult.setAccuracyRate(MonitoringSummaryData.getInstance().getSoldGetAccuracyRate());
		soldGetCheckResult.getOrderMissCheckResult().increment(validNumber.longValue());

		//设置dbId
		monitorService.setDbId(soldGetCheckResult);

		String titleSuffix;
		if(soldGetCheckResult.getOrderMissCheckResult().getTotalCount().longValue() <= 0){
			titleSuffix = "缺少样本";
		}else{
			titleSuffix = new DecimalFormat("#.####%").format(soldGetCheckResult.getAccuracyRate());
		}
		//发送校验结果通知
		monitorService.sendCheckResultNotify(getCurrentStoreId() + "_SOLD_GET", soldGetCheckResult,
			titleSuffix, "sold_get");
	}


	/**
	 * 查询指定时间内的不重复的订单卖家nick(seller_nick, seller_id)
	 *
	 * @param startTime 查询起始时间
	 * @param endTime   查询结束时间
	 * @param status    无用
	 * @param listId
	 * @return
	 */
	@Override
	public List<UserProductionInfoExt> querySamples(LocalDateTime startTime, LocalDateTime endTime, String status,
		long listId) {
		List<UserProductionInfoExt> all = monitorUserService.findAllOrderUser();
		all = all.stream().filter(user-> getCurrentStoreId().equals(user.getStoreId())).collect(Collectors.toList());

		Collections.shuffle(all);
		int size = Integer.parseInt(monitorConfig.getMultiSoldGetNumbers().get(getCurrentStoreId()));
		size = Math.min(all.size(), size);
		List<UserProductionInfoExt> result = all.subList(0, size);
		LOGGER.logInfo(name() + " 样本数:" + size);
		return result;
	}

	@Override
	protected String getStoreId() {
		return currentStoreId;
	}

	@Override
	protected void error(UserProductionInfoExt sample, String message, Throwable e) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message, e);
	}

	@Override
	protected void error(UserProductionInfoExt sample, String message) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void info(UserProductionInfoExt sample, String message) {
		LOGGER.logInfo(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void warn(UserProductionInfoExt sample, String message) {
		LOGGER.logWarn(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}
}
