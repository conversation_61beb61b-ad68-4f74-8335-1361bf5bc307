package cn.loveapp.orders.monitor.controller.operations;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.monitor.constant.operations.ResponseConstant;
import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoRequest;
import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoResponse;
import cn.loveapp.orders.monitor.service.operations.PlatformUserService;

/**
 * <AUTHOR>
 * @date 2022-08-02 14:13
 * @Description: 运维平台登录控制器
 */
@RestController
@RequestMapping(value = "monitor/operations")
public class PlatformLoginController {
    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(PlatformLoginController.class);

    @Autowired
    private PlatformUserService platformUserService;

    @RequestMapping(value = "/login")
    public CommonApiResponse<PlatformPowerUserInfoResponse> userLogin(HttpServletRequest request,
        HttpServletResponse response, @RequestBody PlatformPowerUserInfoRequest userInfoRequest) {
        PlatformPowerUserInfoResponse platFormPowerUserInfoResponse =
            platformUserService.getPlatformUserInfo(userInfoRequest, request, response);
        if (platFormPowerUserInfoResponse == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }
        if (!platFormPowerUserInfoResponse.getEnable()) {
            return CommonApiResponse.failed(ResponseConstant.USER_EXPIRE.getCode(),
                ResponseConstant.USER_EXPIRE.getMessage());
        }
        return CommonApiResponse.success(platFormPowerUserInfoResponse);
    }
}
