package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.dao.dream.MonitorPlatformAdminDao;
import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoRequest;
import cn.loveapp.orders.monitor.dto.operations.PlatformPowerUserInfoResponse;
import cn.loveapp.orders.monitor.entity.operations.MonitorPlatformAdmin;
import cn.loveapp.orders.monitor.service.operations.PlatformUserService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022-08-02 14:23
 * @Description: 运维平台用户服务接口实现类
 */
@Service
public class PlatformUserServiceImpl implements PlatformUserService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(PlatformUserServiceImpl.class);

    private static final String MONITOR_PLATFORM_PREFIX = "monitor:platform:user:";

    @Autowired
    private MonitorPlatformAdminDao monitorPlatformAdminDao;

    @Autowired
    @Qualifier("userInfoStringRedisTemplate")
    private StringRedisTemplate template;

    @Override
    public PlatformPowerUserInfoResponse getPlatformUserInfo(PlatformPowerUserInfoRequest userInfoRequest,
        HttpServletRequest request, HttpServletResponse response) {
        String token = request.getHeader("token");
        String username = userInfoRequest.getUsername();
        String password = userInfoRequest.getPassword();

        MonitorPlatformAdmin monitorPlatformAdmin = monitorPlatformAdminDao.queryByUsername(username);

        if (monitorPlatformAdmin == null) {
            return null;
        }

        if (!monitorPlatformAdmin.isEnable()) {
            return PlatformPowerUserInfoResponse.of(false, null, monitorPlatformAdmin.isEnable());
        }

        if (token != null) {
            String userToken = template.opsForValue().get(token);
            if (userToken != null) {
                return PlatformPowerUserInfoResponse.of(true, null, monitorPlatformAdmin.isEnable());
            }
        }

        if (monitorPlatformAdmin.getPassword().equals(DigestUtils.md5Hex(password))) {

            String tokenKey = UUID.randomUUID().toString();
            String s = template.opsForValue().get(tokenKey);
            if (s == null) {
                String tokenValue = MONITOR_PLATFORM_PREFIX + userInfoRequest.getUsername();
                template.opsForValue().set(tokenKey, tokenValue);
            }
            response.setHeader("token", tokenKey);
            return PlatformPowerUserInfoResponse.of(true, null, monitorPlatformAdmin.isEnable());
        }
        return null;
    }
}
