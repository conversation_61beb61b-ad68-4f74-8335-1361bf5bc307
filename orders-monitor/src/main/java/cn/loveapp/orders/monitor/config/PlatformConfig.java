package cn.loveapp.orders.monitor.config;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.listen.PlatformStatusListen;
import cn.loveapp.orders.common.service.AyStatusCodeConfigService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @program: orders-services-group
 * @description: PlatformConfig
 * @author: Jason
 * @create: 2018-12-24 15:26
 **/
@Configuration
@EnableScheduling
public class PlatformConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PlatformConfig.class);

	@Bean
	public PlatformStatusListen platformStatusListen(AyStatusCodeConfigService statusCodeConfigService) {
		PlatformStatusListen platformStatusListen = new PlatformStatusListen(statusCodeConfigService);
		LOGGER.logInfo("初始化状态码...");
		try {
			platformStatusListen.syncStatusCodeConfig();
		} catch (Exception e) {
			LOGGER.logError("初始化状态码失败, 自动关闭服务: " + e.getMessage(), e);
			System.exit(0);
		}
		LOGGER.logInfo("初始化状态码结束");
		return platformStatusListen;
	}

}
