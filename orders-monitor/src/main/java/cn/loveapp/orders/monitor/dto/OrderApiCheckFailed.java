package cn.loveapp.orders.monitor.dto;

import java.util.List;
import java.util.Map;

/**
 * OrderApiCheckFailed
 *
 * <AUTHOR>
 * @date 2019-03-19
 */
public interface OrderApiCheckFailed {

	/**
	 * 添加差异属性说明
	 *
	 * @param property
	 * @param desc
	 */
	void addDiffProperty(String property, String desc);

	/**
	 * 设置dbId
	 *
	 * @param dbId
	 */
	void setDbId(int dbId);

	/**
	 * 获取dbId
	 * @return
	 */
	int getDbId();

	/**
	 * 获取sellerNick
	 *
	 * @return
	 */
	String getSellerNick();

	/**
	 * 获取storeId
	 *
	 * @return
	 */
	String getStoreId();

	/**
	 * 获取appName
	 *
	 * @return
	 */
	String getAppName();

	/**
	 * 获取所有差异
	 *
	 * @return
	 */
	Map<String, List<String>> getDiff();
}
