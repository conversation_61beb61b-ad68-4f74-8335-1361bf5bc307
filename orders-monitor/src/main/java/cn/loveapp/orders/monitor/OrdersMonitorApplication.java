package cn.loveapp.orders.monitor;

import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 订单告警服务
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@EnableScheduling
@EnableCaching
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, RedisRepositoriesAutoConfiguration.class,
	DataSourceAutoConfiguration.class, MybatisAutoConfiguration.class, RedisReactiveAutoConfiguration.class,
	DataSourceHealthContributorAutoConfiguration.class}, scanBasePackages = {"cn.loveapp.orders.monitor",
	"cn.loveapp.orders.common"})
public class OrdersMonitorApplication implements CommandLineRunner {
	public static void main(String[] args) {
		new SpringApplication(OrdersMonitorApplication.class).run(args);
	}

	@Override
	public void run(String... args) throws Exception {

	}
}
