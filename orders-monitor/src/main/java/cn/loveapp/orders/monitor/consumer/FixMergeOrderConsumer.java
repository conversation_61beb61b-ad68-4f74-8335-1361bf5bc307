//package cn.loveapp.orders.monitor.consumer;
//
//import cn.loveapp.common.utils.LoggerHelper;
//import cn.loveapp.orders.common.bo.UserDbId;
//import cn.loveapp.orders.common.consumer.BaseDbIdOnsConsumer;
//import cn.loveapp.orders.common.dao.order.CommonAyTradeMainDao;
//import cn.loveapp.orders.common.dao.redis.OrderSaveLockRedisDao;
//import cn.loveapp.orders.common.service.TradeMergeOrderService;
//import cn.loveapp.orders.common.service.UserProductionInfoExtService;
//import cn.loveapp.orders.monitor.config.MonitorConfig;
//import com.github.rholder.retry.Retryer;
//import com.github.rholder.retry.RetryerBuilder;
//import com.github.rholder.retry.StopStrategies;
//import com.google.common.util.concurrent.RateLimiter;
//import io.micrometer.core.instrument.MeterRegistry;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.env.Environment;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * FixMergeOrderConsumer
// *
// * <AUTHOR>
// * @date 2019-08-27
// */
//@Component
//public class FixMergeOrderConsumer extends BaseDbIdOnsConsumer {
//	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(FixMergeOrderConsumer.class);
//
//	@Autowired
//	private UserProductionInfoExtService userProductionInfoExtService;
//
////	@Autowired
////	private AyTradeSearchDao ayTradeSearchDao;
////
////	@Autowired
////	private CommonAyTradeSearchDao commonAyTradeSearchDao;
//
//	@Autowired
//	private CommonAyTradeMainDao ayTradeMainDao;
//
//	@Autowired
//	private OrderSaveLockRedisDao tradeHandleBatchOrderRedisDao;
//
//	@Autowired
//	private TradeMergeOrderService tradeMergeOrderService;
//
//	private MonitorConfig monitorConfig;
//
//	private RateLimiter limiter;
//
//	public FixMergeOrderConsumer(MeterRegistry registry, Environment environment, MonitorConfig monitorConfig) {
//		super(registry, environment, "FixMergeOrder.QPS");
//		this.monitorConfig = monitorConfig;
//		limiter = RateLimiter.create(monitorConfig.getOrderFixMergeOrderLimit());
//
//	}
//
//	@Override
//	protected String getRateLimitKey() {
//		return "orders.ons.ratelimit.fix.merge.order";
//	}
//
//	@Override
//	protected void execute(String content) throws Exception {
//		String sellerNick = content;
//		if(StringUtils.isEmpty(sellerNick)){
//			return;
//		}
//		UserDbId userDbId = userProductionInfoExtService.getDbIdBySellerNick(sellerNick);
//		if(userDbId == null){
//			LOGGER.logError(sellerNick, "", "未开户用户, 无法获取用户dbId");
//			return;
//		}
//		rateLimiterAcquire();
//		int pageSize = monitorConfig.getOrderFixMergeOrderPageSize();
//		String sellerId = userDbId.getSellerId();
//
//		Retryer<List<AyTradeSearch>> retryer = RetryerBuilder.<List<AyTradeSearch>>newBuilder()
//			.retryIfExceptionOfType(Exception.class)
//			.withStopStrategy(StopStrategies.stopAfterAttempt(3))
//			.build();
//
//		// 更新合单信息
//		updateMergeInfo(sellerNick, pageSize, sellerId, retryer);
//
//		// 尝试对可合单但未合单的订单合单
//		mergeOrder(sellerNick, pageSize, sellerId, retryer);
//
//		LOGGER.logInfo(sellerNick, "", "修补合单结束");
//
//	}
//
//	private void updateMergeInfo(String sellerNick, int pageSize, String sellerId, Retryer<List<AyTradeSearch>> retryer) {
////		Long id = Long.MAX_VALUE;
////		while(true){
////			Long fId = id;
////			List<AyTradeSearch> result;
////			try {
////				result = retryer.call(()->ayTradeSearchDao.queryAllNeedFixMerge(sellerId, fId, pageSize));
////			} catch (Exception e) {
////				LOGGER.logError(sellerNick, "", "查询可修复合单失败: " + e.getMessage(), e);
////				break;
////			}
////			LOGGER.logInfo(sellerNick, "", "查询可修复合单信息, size=" + (result == null ? 0 : result.size())
////				+ " lastId=" + id + " pageSize=" + pageSize);
////			if(result.isEmpty()){
////				break;
////			}
////			int size = result.size();
////			id = result.get(size - 1).getId();
////
////			for(AyTradeSearch ayTradeSearch : result){
////				limiter.acquire();
////				try {
////					String mergeLock = tradeHandleBatchOrderRedisDao.lockMergeOrder(sellerNick,
////						ayTradeSearch.getBuyerNick(), ayTradeSearch.getMergeTid(), ayTradeSearch.getMergeMd5());
////					try {
////						List<cn.loveapp.orders.common.entity.AyTradeSearch> list = commonAyTradeSearchDao.queryAllMergeByMergeTid(ayTradeSearch);
////						List<String> tids = list.stream().filter(x->BooleanUtils.isTrue(x.getIsCombine())).map(cn.loveapp.orders.common.entity.AyTradeSearch::getTid)
////							.collect(Collectors.toList());
////						AyTradeMain ayTradeMain = new AyTradeMain();
////						ayTradeMain.setSellerId(ayTradeSearch.getSellerId());
////						ayTradeMain.setStoreId(ayTradeSearch.getStoreId());
////						ayTradeMain.setMergeTid(ayTradeSearch.getMergeTid());
////						ayTradeMain.setMergeAyTid(ayTradeSearch.getMergeAyTid());
////						ayTradeMain.setIsManual(ayTradeSearch.getIsManual());
////						ayTradeMain.setIsCombine(true);
////						ayTradeMain.setMergeTradeStatus(null);
////						ayTradeMainDao.updateSubMergeInfo(ayTradeMain, tids,false);
////						LOGGER.logInfo(sellerNick, "", "修复主表合单信息, size=" + tids);
////					} finally {
////						tradeHandleBatchOrderRedisDao.unLockMergeOrder(sellerNick, ayTradeSearch.getBuyerNick(),
////							ayTradeSearch.getMergeTid(), ayTradeSearch.getMergeMd5(), mergeLock);
////					}
////					tradeMergeOrderService.directlyUpdateMerge(sellerId, ayTradeSearch.getTid(), ayTradeSearch.getStoreId());
////				} catch (Exception e) {
////					LOGGER.logError(sellerNick, ayTradeSearch.getTid(), "合单修复失败: " + e.getMessage(), e);
////				}
////			}
////			if(size < pageSize){
////				break;
////			}
////		}
//	}
//
//	private void mergeOrder(String sellerNick, int pageSize, String sellerId, Retryer<List<AyTradeSearch>> retryer) {
////		Long id = Long.MAX_VALUE;
////		while(true){
////			Long fId = id;
////			List<AyTradeSearch> result;
////			try {
////				result = retryer.call(()->ayTradeSearchDao.queryAllCanMerge(sellerId, fId, pageSize));
////			} catch (Exception e) {
////				LOGGER.logError(sellerNick, "", "查询可合单订单失败: " + e.getMessage(), e);
////				break;
////			}
////			LOGGER.logInfo(sellerNick, "", "查询可合单信息, size=" + (result == null ? 0 : result.size())
////				+ " lastId=" + id + " pageSize=" + pageSize);
////			if(result.isEmpty()){
////				break;
////			}
////			int size = result.size();
////			id = result.get(size - 1).getId();
////
////			for(AyTradeSearch ayTradeSearch : result){
////				limiter.acquire();
////				try{
////					String tid = ayTradeSearch.getTid();
////					// 是否已合单的订单
////					if(BooleanUtils.isTrue(ayTradeSearch.getIsCombine())){
////						LOGGER.logInfo(sellerNick, tid, "已合单订单, 不需要合单");
////					}else{
////						tradeMergeOrderService.directlyMerge(sellerNick, sellerId, ayTradeSearch.getTid(), ayTradeSearch.getStoreId());
////					}
////				} catch (Exception e) {
////					LOGGER.logError(sellerNick, ayTradeSearch.getTid(), "合单失败: " + e.getMessage(), e);
////				}
////			}
////			if(size < pageSize){
////				break;
////			}
////		}
//	}
//}
