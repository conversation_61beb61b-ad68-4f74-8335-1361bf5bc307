//package cn.loveapp.orders.monitor.config;
//
//import cn.loveapp.common.utils.LoggerHelper;
//import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoAppConfig;
//import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoBatchOrdersAppConfig;
//import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
//import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.event.ContextClosedEvent;
//import org.springframework.core.Ordered;
//
///**
// * MonitorOnsConfig
// *
// * <AUTHOR>
// * @date 2019-08-27
// */
//@Configuration
//public class MonitorOnsConfig {
//	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorOnsConfig.class);
//
//	@Autowired
//	private RocketMQTaobaoAppConfig rocketMQTaobaoAppConfig;
//
//	@Autowired
//	private MonitorConfig monitorConfig;
//
//	private DefaultMQPushConsumer consumer = null;
//
//	@Bean(destroyMethod = "", name = "fixMergeOrderOnsConsumer")
//	public DefaultMQPushConsumer ordersBatchDataOnsConsumer() {
//		//启动ONS消息队列
//		try {
//			consumer = new DefaultMQPushConsumer(monitorConfig.getOrderFixMergeOrderConsumerid());
//			consumer.setNamesrvAddr(rocketMQTaobaoAppConfig.getNamesrvAddr());
//			consumer.setConsumeThreadMax(monitorConfig.getOrderFixMergeOrderProducerPoolSize());
//			consumer.setConsumeThreadMin(monitorConfig.getOrderFixMergeOrderProducerPoolSize());
//		} catch (Exception e) {
//			LOGGER.logError("create order batch data ONS Consumer failed", e);
//		}
//		return consumer;
//	}
//
//	@Bean(name = "ordersBatchDataOnsConsumerLifeCycleManager")
//	public OnsLifeCycleManager onsLifeCycleManager() {
//		return new OnsLifeCycleManager();
//	}
//
//	/**
//	 * Ons 生命周期管理
//	 *
//	 * <AUTHOR>
//	 * @date 2018/11/9
//	 */
//	public static class OnsLifeCycleManager implements ApplicationRunner, ApplicationListener<ContextClosedEvent>,
//		Ordered {
//		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);
//
//		@Autowired(required = false)
//		@Qualifier("fixMergeOrderOnsConsumer")
//		private DefaultMQPushConsumer pushConsumer;
//
//		@Autowired
//		private FixMergeOrderConsumer fixMergeOrderConsumer;
//
//		@Autowired
//		private MonitorConfig monitorConfig;
//
//		@Autowired
//		private RocketMQTaobaoBatchOrdersAppConfig rocketMQTaobaoBatchOrdersAppConfig;
//
//		@Autowired
//		private RocketMqQueueHelper rocketMqQueueHelper;
//
//		private volatile boolean started = false;
//
//		@Override
//		public void run(ApplicationArguments args) throws Exception {
//			if(!args.containsOption("orders.fixMergeOrder.switch")){
//				return;
//			}
//			started = true;
//			//启动订单ONS消费者
//			if (pushConsumer != null) {
//				pushConsumer.subscribe(monitorConfig.getOrderFixMergeOrderTopic(), "*");
//				pushConsumer.registerMessageListener(fixMergeOrderConsumer);
//				LOGGER.logInfo("fix merge Consumer is startting");
//				pushConsumer.start();
//				LOGGER.logInfo(
//					"fix merge Consumer is started, Topic: " + rocketMQTaobaoBatchOrdersAppConfig.getTopic()
//						+ " Tag: " + rocketMQTaobaoBatchOrdersAppConfig.getTag());
//			}
//		}
//
//		@Override
//		public void onApplicationEvent(ContextClosedEvent event) {
//			if(!started){
//				return;
//			}
//			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
//				return;
//			}
//			if (pushConsumer != null) {
//				LOGGER.logInfo("正在关闭 fix merge Consumer...");
//				try {
//					fixMergeOrderConsumer.stop();
//					rocketMqQueueHelper.stopOnsConsumer(pushConsumer);
//				} catch (Exception e) {
//					LOGGER.logError(e.getMessage(), e);
//				}
//				LOGGER.logInfo(" fix merge Consumer已关闭");
//			}
//		}
//
//		@Override
//		public int getOrder() {
//			return Ordered.HIGHEST_PRECEDENCE;
//		}
//
//	}
//}
