package cn.loveapp.orders.monitor.dto.operations;

import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 09:55
 * @Description: 问题查询链传输体
 */
public class AyUserProblemQueryChainDTO {
    private AyUserProblemQueryChainRequest request;
    private List<AyUserProblemQueryService> ayUserProblemQueryList;
    private Integer index;

    public AyUserProblemQueryChainDTO(AyUserProblemQueryChainRequest request,
        List<AyUserProblemQueryService> ayUserProblemQueryList, Integer index) {
        this.request = request;
        this.ayUserProblemQueryList = ayUserProblemQueryList;
        this.index = index;
    }

    public AyUserProblemQueryChainRequest ayUserProblemQueryChainRequest() {
        return request;
    }

    public AyUserProblemQueryChainRequest proceed(AyUserProblemQueryChainRequest request) {
        AyUserProblemQueryChainRequest problemQueryChainRequest = null;
        if (ayUserProblemQueryList.size() > index) {
            AyUserProblemQueryChainDTO ayLogChainService =
                new AyUserProblemQueryChainDTO(request, ayUserProblemQueryList, index + 1);
            AyUserProblemQueryService ayUserProblemQueryService = ayUserProblemQueryList.get(index);
            problemQueryChainRequest = ayUserProblemQueryService.logQueryDispose(ayLogChainService);
        }
        return problemQueryChainRequest;
    }
}
