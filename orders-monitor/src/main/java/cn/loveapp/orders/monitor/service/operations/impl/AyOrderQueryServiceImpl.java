package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.monitor.constant.operations.ParamStatusTagConstant;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 10:42
 * @Description: 爱用订单表问题查询服务实现类
 */
@Service
public class AyOrderQueryServiceImpl implements AyUserProblemQueryService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyOrderQueryServiceImpl.class);

    /**
     * 状态标识-不是退款订单
     */
    public static final String NO_REFUND_ORDER = "NO_REFUND";
    @Autowired
    private MonitorAyTradeSearchESDao commonAyTradeSearchESDao;

    @Autowired
    private SubOrderRepository subOrderRepository;

    @Override
    public AyUserProblemQueryChainRequest logQueryDispose(AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO) {
        AyUserProblemQueryChainRequest request = ayUserProblemQueryChainDTO.ayUserProblemQueryChainRequest();
        String sellerNick = request.getSellerNick();
        String sellerId = request.getSellerId();
        String tid = request.getTid();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        request.setNowRefundStatusMap(new HashMap<>());
        ProblemAnalysisResponse problemAnalysisResponse = request.getProblemAnalysisResponse();
        ProblemAnalysisResponse.Param param;

        LOGGER.logInfo(sellerNick, tid, "开始分析ES存单");
        AyTradeSearchES ayTradeSearchEs = AyTradeSearchES.of(sellerId, null, storeId, appName, tid);
        ayTradeSearchEs = commonAyTradeSearchESDao.getById(ayTradeSearchEs);

        List<TcSubOrder> tcSubOrders = subOrderRepository.findAySubOrderByTidAndStoreIdAndSellerId(
            Lists.newArrayList(request.getTid()), request.getStoreId(), request.getSellerId(), request.getAppName());

        if (ayTradeSearchEs == null) {
            LOGGER.logInfo(sellerNick, tid, "ES数据库中没有订单信息");
            param = new ProblemAnalysisResponse.Param("ES数据库", "订单信息不存在", ParamStatusTagConstant.ERRORTAG);
            problemAnalysisResponse.getParams().add(param);
            request.setIsRefundOrder(false);
        } else {
            request.setNowStatus(ayTradeSearchEs.getTaoStatus());
            if (ayTradeSearchEs.getIsRefund()) {
                HashMap<String, String> nowRefundStatusMap = new HashMap<>();
                request.setIsRefundOrder(true);
                for (TcSubOrder tcSubOrder : tcSubOrders) {
                    nowRefundStatusMap.put(tcSubOrder.getOid(), tcSubOrder.getRefundStatus());
                    request.setNowRefundStatusMap(nowRefundStatusMap);
                }
            } else {
                request.setIsRefundOrder(false);
            }
        }
        return ayUserProblemQueryChainDTO.proceed(request);
    }
}
