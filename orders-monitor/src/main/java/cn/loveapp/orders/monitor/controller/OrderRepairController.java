package cn.loveapp.orders.monitor.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.monitor.dto.ClearCrossOrderRequest;
import cn.loveapp.orders.monitor.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 修理问题订单
 */
@RestController
@RequestMapping("/monitor/orderRepair")
public class OrderRepairController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderRepairController.class);

    @Autowired
    private MonitorService monitorService;


    /**
     * 清理串单订单
     * @return
     */
    @RequestMapping("/clearCrossOrder")
    public CommonApiResponse<Map<String, String>> clearCrossOrder(@RequestBody ClearCrossOrderRequest request) {
        return CommonApiResponse.success(monitorService.clearCrossOrder(request));
    }

}
