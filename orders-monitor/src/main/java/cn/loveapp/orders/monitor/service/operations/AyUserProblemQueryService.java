package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 09:41
 * @Description: 爱用日志查询服务接口
 */
public interface AyUserProblemQueryService {

    /**
     * 处理日志查询请求
     *
     * @param ayUserProblemQueryChainDTO
     * @return
     */
    AyUserProblemQueryChainRequest logQueryDispose(AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO);

}
