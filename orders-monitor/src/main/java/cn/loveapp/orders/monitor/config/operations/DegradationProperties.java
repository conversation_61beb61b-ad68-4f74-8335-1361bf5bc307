package cn.loveapp.orders.monitor.config.operations;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * 降级相关配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-20 17:56
 */

@Data
@ConfigurationProperties(prefix = "orders.monitor.degradation.config")
public class DegradationProperties {

	private Apollo apollo;

	@Data
	public static class Apollo {
		private String portalUrl;
		private String token;
	}
}
