package cn.loveapp.orders.monitor.dao.rds;

import cn.loveapp.orders.common.entity.JdpTbTrade;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.List;

/**
 * (JdpTbTrade)表数据库访问层
 *
 * <AUTHOR>
 * @since 2018-11-16 16:05:47
 */
public interface MonitorJdpTbTradeDao {

	/**
	 * 通过时间范围作为筛选条件查询
	 * 通过时间范围作为筛选条件查询数量
	 *
	 * @param minJdpModified 最小时间
	 * @param maxJdpModified 最大时间
	 * @param pageRequest 分页
	 * @return 对象列表
	 */
	List<JdpTbTrade> queryByJdpModified(@Param("minJdpModified") LocalDateTime minJdpModified,
		@Param("maxJdpModified") LocalDateTime maxJdpModified, @Param("page") PageRequest pageRequest);

	/**
	 * 按tid查询数据
	 *
	 * @param tid
	 * @return
	 */
	JdpTbTrade queryByTid(String tid);

	/**
	 * 新增数据
	 *
	 * @param jdpTbTrade 实例对象
	 * @return 影响行数
	 */
	int insert(JdpTbTrade jdpTbTrade);

}
