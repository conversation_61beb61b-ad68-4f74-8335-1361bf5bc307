package cn.loveapp.orders.monitor.dto.operations;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-08 15:13
 * @Description: 问题分析结果返回体
 */
@Data
public class ProblemAnalysisResponse {

    /**
     * 分析结果集合
     */
    private List<Param> params;

    @Data
    @AllArgsConstructor
    public static class Param {

        /**
         * 参数名
         */
        private String paramStatusName;

        /**
         * 参数结果
         */
        private String paramStatusValue;

        /**
         * 参数标签
         */
        private Integer paramStatusTab;
    }
}
