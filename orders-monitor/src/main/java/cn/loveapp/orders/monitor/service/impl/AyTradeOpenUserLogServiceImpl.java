package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.orders.monitor.dao.dream.AyTradeOpenUserLogDao;
import cn.loveapp.orders.monitor.entity.AyTradeOpenUserLog;
import cn.loveapp.orders.monitor.service.AyTradeOpenUserLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 16:04
 * @Description: (ay_trade_open_user_log)开户记录日志表服务实现类
 */
@Service
public class AyTradeOpenUserLogServiceImpl implements AyTradeOpenUserLogService {

    @Autowired
    private AyTradeOpenUserLogDao ayTradeOpenUserLogDao;

    @Override
    public List<AyTradeOpenUserLog> getAllOpenUserLogBySellerNick(String sellerNick) {
        return ayTradeOpenUserLogDao.getAllBySellerNick(sellerNick);
    }
}
