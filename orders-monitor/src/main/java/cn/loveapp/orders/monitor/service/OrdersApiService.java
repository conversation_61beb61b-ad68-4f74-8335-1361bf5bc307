package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import com.alibaba.fastjson.JSONException;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 订单api服务访问service
 *
 * <AUTHOR>
 * @date 2018-12-20
 */
public interface OrdersApiService {
	/**
	 * 访问订单service服务的fullinfo接口
	 *
	 * @param tradeSearch
	 * @return
	 * @throws ApiServerErrorException
	 * @throws JSONException
	 * @throws IOException
	 */
	String fullinfo(AyTradeSearchES tradeSearch) throws ApiServerErrorException, IOException;

	/**
	 * 访问订单service服务的detail.info.get接口
	 *
	 * @param tradeaSearch
	 * @param fields
	 * @return
	 * @throws ApiServerErrorException
	 * @throws IOException
	 */
	String detailInfoGet(AyTradeSearchES tradeaSearch, String fields) throws ApiServerErrorException, IOException;

	/**
	 * 访问订单service服务的soldGet接口
	 *
	 * @param sellerId
	 * @param nick
	 * @param appName
	 * @param status
	 * @param startTime
	 * @param endTime
	 * @param pageNo
	 * @return
	 * @throws ApiServerErrorException
	 * @throws IOException
	 */
	String soldGet(String sellerId, String nick, String appName, String status, LocalDateTime startTime, LocalDateTime endTime,
		long pageNo) throws ApiServerErrorException, IOException;

	/**
	 * 访问订单service服务的searchList接口
	 *
	 * @param sellerId
	 * @param sellerNick
	 * @param fields
	 * @param status
	 * @param startTime
	 * @param endTime
	 * @param pageNo
	 * @param pageSize
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws ApiServerErrorException
	 * @throws IOException
	 */
	String searchList(String sellerId, String sellerNick, String fields, String status, LocalDateTime startTime,
		LocalDateTime endTime, long pageNo, long pageSize, String sort, String platformId, String appName) throws ApiServerErrorException, IOException;
}
