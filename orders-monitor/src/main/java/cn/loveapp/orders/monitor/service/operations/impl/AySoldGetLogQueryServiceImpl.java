package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.config.operations.MonitorPlatformBaseConfig;
import cn.loveapp.orders.monitor.constant.operations.LogEsIndexConstant;
import cn.loveapp.orders.monitor.constant.operations.ParamStatusTagConstant;
import cn.loveapp.orders.monitor.dao.es.MonitorAyLogSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 20:37
 * @Description: 爱用SoldGet日志查询服务接口
 */
@Service
public class AySoldGetLogQueryServiceImpl implements AyUserProblemQueryService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(AySoldGetLogQueryServiceImpl.class);

    /**
     * 拉单中断，session失效标识
     */
    public static final String SOLD_GET_SESSION_ERR = "soldGet 失败, 结束拉单: topsession失效";

    /**
     * 拉单成功标识
     */
    public static final String SOLD_GET_SUCCESS = "结束拉取sold.get订单";

    @Autowired
    private MonitorPlatformBaseConfig monitorPlatformBaseConfig;

    @Autowired
    private MonitorAyLogSearchESDao monitorAyLogSearchESDao;

    @Override
    public AyUserProblemQueryChainRequest logQueryDispose(AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO) {
        AyUserProblemQueryChainRequest request = ayUserProblemQueryChainDTO.ayUserProblemQueryChainRequest();
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        LocalDateTime pullStartDateTime = request.getPullStartDateTime();
        ProblemAnalysisResponse problemAnalysisResponse = request.getProblemAnalysisResponse();
        ProblemAnalysisResponse.Param param;

        // 设定日志查询开始时间(确保能够查询到导致拉单失败的原因)
        LocalDateTime searchLogStatTime = pullStartDateTime.minusMinutes(2);

        if (pullStartDateTime.plusDays(monitorPlatformBaseConfig.getLogSaveDays()).isAfter(LocalDateTime.now())) {
            LOGGER.logInfo(sellerNick, null, "开始查询SoldGet日志");
            List<String> soldGetEndMessage = monitorAyLogSearchESDao.getLogMessageList(sellerNick,
                Lists.newArrayList(SOLD_GET_SUCCESS), DateUtil.convertLocalDateTimetoDate(searchLogStatTime),
                LogEsIndexConstant.getSoldGetIndexPrefix(storeId));
            LOGGER.logInfo(sellerNick, null, "SoldGet日志查询结果为: " + JSON.toJSONString(soldGetEndMessage));
            if (soldGetEndMessage.size() > 0) {
                param = new ProblemAnalysisResponse.Param("SoldGet日志", "成功拉取订单", ParamStatusTagConstant.CORRECTTAG);
                problemAnalysisResponse.getParams().add(param);
            } else {
                param = new ProblemAnalysisResponse.Param("SoldGet日志", "拉单没有正常结束", ParamStatusTagConstant.ERRORTAG);
                problemAnalysisResponse.getParams().add(param);

                List<String> soldGetTopSessionMessage = monitorAyLogSearchESDao.getLogMessageList(sellerNick,
                    Lists.newArrayList(SOLD_GET_SESSION_ERR), DateUtil.convertLocalDateTimetoDate(searchLogStatTime),
                    LogEsIndexConstant.getSoldGetIndexPrefix(storeId));
                if (soldGetTopSessionMessage.size() > 0) {
                    param =
                        new ProblemAnalysisResponse.Param("SoldGet日志", "TopSession失效", ParamStatusTagConstant.ERRORTAG);
                    problemAnalysisResponse.getParams().add(param);
                }
            }
        } else {
            param = new ProblemAnalysisResponse.Param("SoldGet日志", "已超过最大日志保存天数", ParamStatusTagConstant.ERRORTAG);
            problemAnalysisResponse.getParams().add(param);
        }

        return ayUserProblemQueryChainDTO.proceed(request);
    }
}
