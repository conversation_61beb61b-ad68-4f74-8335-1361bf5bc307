package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.entity.OrderSearchTrade;

import java.util.List;

/**
 * 订单记录基本信息(TaobaoOrderSearchTrade)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-23 16:26:06
 */
public interface OrderSearchTradeService {

    /**
     * 获取最新的用户订购信息
     *
     * @param nick
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    OrderSearchTrade getOrderSearchInfo(String nick, String sellerId, String storeId, String appName);

    /**
     * 获取用户订购记录
     *
     * @param sellerNick
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<OrderSearchTrade> getAllOrderSearchBySellerNick(String sellerNick, String sellerId, String storeId,
        String appName);

}
