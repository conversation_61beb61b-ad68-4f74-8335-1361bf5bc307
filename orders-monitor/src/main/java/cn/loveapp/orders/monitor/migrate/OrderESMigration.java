package cn.loveapp.orders.monitor.migrate;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.beust.jcommander.internal.Maps;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.util.DoubleFormat;

/**
 * 订单ES 增量迁移
 *
 * <AUTHOR>
 * @date 2020/12/16
 */
@Component
public class OrderESMigration implements ApplicationRunner, ApplicationListener<ContextClosedEvent>, Ordered {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderESMigration.class);

    private final static String NAME = "ES迁移，";

    private final static String KEY_LAST_TIME = "order:migrate:es:lastTime:";

    // @formatter:off
    private static final Map<String, String> esIndexMap = Maps.newHashMap(
        "ay_trade_search-*", "ay_trade_search",
        "ay_refund_search", "ay_refund_search",
        "ay_printlog_search", "ay_printlog_search",
        "ay_distribute_order_search-0", "ay_distribute_order_search-0",
        "ay_logistics_order_info_search", "ay_logistics_order_info_search",
        "ay_item_search", "ay_item_search");
    // @formatter:on

    @Value("#{${orders.migrate.es.index-enable:{ay_trade_search:'false', ay_refund_search:'false', ay_printlog_search:'false', ay_distribute_order_search:'false',ay_item_search:'false',ay_logistics_order_info_search:'false'}}}")
    private Map<String, Boolean> esIndexEnableMap = new HashMap<>();

    @Value("#{${orders.migrate.es.fast-sync.index-enable:{ay_trade_search:'false', ay_refund_search:'false', ay_printlog_search:'false', ay_distribute_order_search:'false',ay_item_search:'false',ay_logistics_order_info_search:'false'}}}")
    private Map<String, Boolean> esIndexFastSyncEnableMap = new HashMap<>();

    @Value("${orders.migrate.es.auto-pause.enable:false}")
    private volatile boolean enableAutoPause;

    @Value("${orders.migrate.es.auto-pause.beginTime:-1}")
    private int beginEnableTime;

    @Value("${orders.migrate.es.auto-pause.endTime:1000}")
    private int endEnableTime;

    @Value("${orders.migrate.es.manual-pause:true}")
    private volatile boolean manualPaused;

    @Value("${orders.migrate.es.target.uri:}")
    private List<String> targetESUri;

    @Value("${orders.migrate.es.target.password:}")
    private String targetESPassword;

    @Value("${orders.migrate.es.target.username:}")
    private String targetESUsername;

    /**
     * 迁移ES中数据的起始修改时间
     */
    @Value("${orders.migrate.es.startModifiedTime:}")
    private String startModifiedTime;

    /**
     * 迁移ES中 时间增量大小(秒)
     */
    @Value("${orders.migrate.es.timeStep:1}")
    private int timeStep;

    @Value("${orders.migrate.es.pageSize:5}")
    private int queryPageSize;

    @Value("${orders.migrate.es.sleep-time:3}")
    private long sleepTime;

    @Value("${orders.migrate.es.fast-sync.sleep-time:500}")
    private long fastSyncSleepTime;

    @Value("${orders.migrate.es.rollback-time:15}")
    private long rollbackTime;

    @Value("${orders.migrate.es.last-time-limit:5}")
    private long lastTimeLimit;

    /**
     * 迁移限流
     */
    @Value("${orders.migrate.es.limit:5}")
    private void listenLimit(double limit) {
        rateLimiter.setRate(limit);
    }

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MonitorAyTradeSearchESDao esDao;

    private final Map<String, LocalDateTime> lastModifiedTimeMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> speedMap = new ConcurrentHashMap<>();
    private final Map<String, Timer> timerMap = new ConcurrentHashMap<>();
    private final RateLimiter rateLimiter = RateLimiter.create(Integer.MAX_VALUE);

    /**
     * 是否已停止
     */
    private volatile boolean stopped;

    /**
     * 是否已自动停止
     */
    private volatile boolean autoPaused;

    /**
     * 是否已开始
     */
    private volatile boolean started;

    private volatile RestHighLevelClient targetClient;

    private volatile ThreadPoolExecutor pool;

    public OrderESMigration(MeterRegistry registry) {
        for (String index : esIndexMap.keySet()) {
            timerMap.put(index, registry.timer("migrate_es_timer", "index", index));
            speedMap.put(index, new AtomicLong(0));
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!args.containsOption("orders.migrate.es.switch")) {
            return;
        }
        CompletableFuture.runAsync(this::start);
    }

    private void start() {
        if (started) {
            LOGGER.logInfo(NAME + "忽略重复运行");
            return;
        }
        started = true;

        pool = new ThreadPoolExecutor(esIndexMap.size() * 2, esIndexMap.size() * 2, 0, TimeUnit.SECONDS,
            new SynchronousQueue<>(), new ThreadFactoryBuilder().setNameFormat("es-migrate-pool-%d").build(),
            (Runnable r, ThreadPoolExecutor executor) -> {
                if (!executor.isShutdown()) {
                    try {
                        executor.getQueue().put(r);
                    } catch (InterruptedException e) {
                        LOGGER.logError(e.getMessage(), e);
                    }
                }
            });
        for (Map.Entry<String, String> entry : esIndexMap.entrySet()) {
            final String searchIndex = entry.getKey();
            final String writeIndex = entry.getValue();
            final Timer timer = timerMap.get(searchIndex);
            final AtomicLong speed = speedMap.get(searchIndex);
            pool.execute(() -> {
                syncIndex(false, searchIndex, writeIndex, timer, speed);
            });
            pool.execute(() -> {
                syncIndex(true, searchIndex, writeIndex, timer, speed);
            });

        }
    }

    private void syncIndex(boolean fastSync, String searchIndex, String writeIndex, Timer timer, AtomicLong speed) {
        MDC.put("searchIndex", searchIndex);
        String logPrefix = NAME + searchIndex + (fastSync ? " 快速同步 " : " ");
        while (!stopped) {
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;
            try {
                boolean indexEnabled =
                    esIndexEnableMap.getOrDefault(searchIndex.replace("-*", "").replace("-0", ""), false);
                if (fastSync) {
                    indexEnabled = indexEnabled && esIndexFastSyncEnableMap
                        .getOrDefault(searchIndex.replace("-*", "").replace("-0", ""), false);
                }
                if (autoPaused || manualPaused || !indexEnabled) {
                    LOGGER.logInfo(logPrefix + "迁移暂停, startTime=" + startTime + ", endTime=" + endTime);
                    try {
                        TimeUnit.MINUTES.sleep(1);
                    } catch (InterruptedException e) {
                        return;
                    }
                    continue;
                }
                if (targetClient == null) {
                    initTargetES();
                }
                if (fastSync) {
                    endTime = LocalDateTime.now();
                    startTime = endTime.minusSeconds(timeStep);
                } else {
                    startTime = loadLastTime(searchIndex);
                    endTime = startTime.plusSeconds(timeStep);
                }
                int page = 0;
                int pageSize = queryPageSize;
                boolean isEnd;
                do {
                    Timer.Sample sample = Timer.start();
                    List<SearchHit> searchHits =
                        esDao.queryByGmtModified(searchIndex, startTime, endTime, page, pageSize);
                    esDao.bulkIndex(writeIndex, searchHits, targetClient, logPrefix);
                    sample.stop(timer);
                    int size = searchHits.size();
                    isEnd = size < queryPageSize;
                    if (!isEnd) {
                        LocalDateTime lastTime = getLastTime(searchHits, size);
                        if (lastTime.isAfter(endTime)) {
                            throw new RuntimeException(
                                logPrefix + "lastTime " + startTime + " 大于 endTime=" + endTime + ", 数据异常");
                        } else if (lastTime.equals(startTime)) {
                            page++;
                        } else {
                            startTime = lastTime;
                            page = 0;
                        }
                    }

                    if (size > 0) {
                        rateLimiter.acquire(size);
                        speed.addAndGet(size);
                    }
                } while (!isEnd);

                if(fastSync) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(fastSyncSleepTime);
                    } catch (InterruptedException e) {
                        return;
                    }
                }else if (saveLastTime(searchIndex, startTime, endTime)) {
                    try {
                        TimeUnit.SECONDS.sleep(sleepTime);
                    } catch (InterruptedException e) {
                        return;
                    }
                }
            } catch (Exception e) {
                LOGGER.logError(logPrefix + "startTime=" + startTime + ", endTime=" + endTime + ": " + e.getMessage(),
                    e);
            }
        }
    }

    private LocalDateTime getLastTime(List<SearchHit> searchHits, int size) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli((Long)searchHits.get(size - 1).getSortValues()[0]),
            ZoneOffset.UTC);
    }

    /**
     * 初始化目标ES client
     */
    private synchronized void initTargetES() {
        if (targetClient == null) {
            HttpHost[] hosts = targetESUri.stream().map(HttpHost::create).toArray(HttpHost[]::new);
            RestClientBuilder builder = RestClient.builder(hosts);
            // auth
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(targetESUsername, targetESPassword));
            builder.setHttpClientConfigCallback(httpClientConfigCallback -> httpClientConfigCallback
                .setDefaultCredentialsProvider(credentialsProvider));
            // timeout
            builder.setRequestConfigCallback(requestConfigBuilder -> {
                requestConfigBuilder.setConnectTimeout(10 * 1000);
                requestConfigBuilder.setConnectionRequestTimeout(10 * 1000);
                requestConfigBuilder.setSocketTimeout(30 * 1000);
                return requestConfigBuilder;
            });

            targetClient = new RestHighLevelClient(builder);
        }
    }

    private LocalDateTime loadLastTime(String indexName) {
        LocalDateTime lastTime = lastModifiedTimeMap.get(indexName);
        if (lastTime != null) {
            return lastTime;
        }
        String lastTimeStr = stringRedisTemplate.opsForValue().get(generateLastTimeRedisKey(indexName));
        if (StringUtils.isNotEmpty(lastTimeStr)) {
            lastTime = LocalDateTime.parse(lastTimeStr);
        } else {
            lastTime = StringUtils.isNotEmpty(startModifiedTime) ? LocalDateTime.parse(startModifiedTime)
                : LocalDateTime.now().withSecond(0).withNano(0).minusMinutes(1);
        }
        lastModifiedTimeMap.put(indexName, lastTime);
        return lastTime;
    }

    private boolean saveLastTime(String indexName, LocalDateTime startTime, LocalDateTime lastTime) {
        LocalDateTime now = LocalDateTime.now();
        boolean isAheadOfItsTime = false;
        LocalDateTime newLastTime = lastTime;
        if (!newLastTime.isBefore(now.minusSeconds(lastTimeLimit))) {
            if (indexName.contains("_item_")) {
                newLastTime = startTime.minusSeconds(rollbackTime * 2);
            } else {
                newLastTime = startTime.minusSeconds(rollbackTime);
            }
            LOGGER.logInfo(NAME + indexName + " 保存进度, " + lastTime + " 已超过当前时间-" + lastTimeLimit + "s, 回退到: "
                + newLastTime + ", timeStep=" + timeStep);
            isAheadOfItsTime = true;
        }
        lastModifiedTimeMap.put(indexName, newLastTime);
        stringRedisTemplate.opsForValue().set(generateLastTimeRedisKey(indexName), newLastTime.toString(), 7,
            TimeUnit.DAYS);
        return isAheadOfItsTime;
    }

    private static String generateLastTimeRedisKey(String indexName) {
        return KEY_LAST_TIME + indexName;
    }

    /**
     * 检查运行时间
     *
     * @return
     */
    private boolean checkRunTime() {
        int now = LocalTime.now().getHour();
        boolean isOutTime = endEnableTime > beginEnableTime ? now < beginEnableTime || now > endEnableTime
            : now < beginEnableTime && now >= endEnableTime;
        if (isOutTime) {
            LOGGER.logInfo(NAME + "错误的任务时间, 当前时间: " + LocalDateTime.now() + " beginTime:" + beginEnableTime
                + " endTime:" + endEnableTime);
            return false;
        }
        return true;
    }

    /**
     * 周期性打印日志
     */
    @Scheduled(cron = "${orders.migrate.es.auto-pause.cron: 0 * * * * ?}")
    public void checkAutoPauseJob() {
        if (!started || stopped || autoPaused || manualPaused || !enableAutoPause) {
            return;
        }
        autoPaused = !checkRunTime();
    }

    @Scheduled(fixedDelay = 10000)
    public void cronLog() {
        if (!started || stopped || autoPaused || manualPaused) {
            return;
        }
        lastModifiedTimeMap.forEach((index, time) -> {
            long speed = speedMap.get(index).getAndSet(0);
            LOGGER.logInfo(NAME + index + " lastTime=" + time + ", timeStep=" + timeStep + ", speed=" + (speed / 10.0)
                + "/s, timer=" + DoubleFormat.decimal(timerMap.get(index).takeSnapshot().mean(TimeUnit.SECONDS)));
        });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (!started) {
            return;
        }
        LOGGER.logInfo(NAME + "应用准备退出");
        stopped = true;
        pool.shutdown();
        try {
            if (!pool.awaitTermination(1, TimeUnit.MINUTES)) {
                pool.shutdownNow();
            }
        } catch (Exception ignored) {
        }
        LOGGER.logInfo(NAME + "应用退出完毕");
        started = false;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
