package cn.loveapp.orders.monitor.dto;

import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.monitor.service.MonitorUserService;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * soldGet检验详细结果
 *
 * <AUTHOR>
 * @date 2019-01-10
 */
@Data
public class SoldGetCheckResult
	extends BaseOrderApiCheckResult<UserProductionInfoExt, SoldGetCheckResult.FailedSoldGet> {

	@Override
	public FailedSoldGet createOrderApiCheckFailed(UserProductionInfoExt sample, LocalDateTime startTime,
		LocalDateTime endTime) {
		FailedSoldGet failedSoldGet = new FailedSoldGet();
		failedSoldGet.setSellerId(sample.getSellerId());
		failedSoldGet.setSellerNick(sample.getSellerNick());
		failedSoldGet.setStoreId(sample.getStoreId());
		failedSoldGet.setAppName(sample.getAppName());
		failedSoldGet.setStartTime(startTime);
		failedSoldGet.setEndTime(endTime);
		failedSoldGet.setDbId(sample.getDbId());
		return failedSoldGet;
	}

	/**
	 * 失败的订单
	 *
	 * <AUTHOR>
	 * @date 2019-01-11
	 */
	@Data
	public static class FailedSoldGet implements OrderApiCheckFailed {
		private String sellerNick;
		private String sellerId;
		private String storeId;
		private String appName;
		private int dbId;
		private LocalDateTime startTime;
		private LocalDateTime endTime;
		private long searchDbCount;

		/**
		 * 不一致的节点及描述
		 */
		private Map<String, List<String>> diffs = Maps.newHashMap();

		/**
		 * 添加不一致的节点及描述
		 *
		 * @param property
		 * @param desc
		 */
		@Override
		public void addDiffProperty(String property, String desc) {
			List<String> values = diffs.getOrDefault(property, Lists.newArrayList());
			if(values.isEmpty()){
				values.add(desc);
				diffs.put(property, values);
			}else{
				values.add(desc);
			}
		}

		@Override
		public Map<String, List<String>> getDiff() {
			return diffs;
		}
	}
}
