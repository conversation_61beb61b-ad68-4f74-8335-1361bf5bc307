package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.impl.rocketmq.ProducerImpl;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.client.exception.MQClientException;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.client.impl.MQClientAPIImpl;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.client.impl.factory.MQClientInstance;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.client.impl.producer.DefaultMQProducerImpl;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.admin.ConsumeStats;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.ResponseCode;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.body.GroupList;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.body.TopicList;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.route.BrokerData;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.protocol.route.TopicRouteData;
import com.google.common.collect.HashBasedTable;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云rocketmq监控
 *
 * <AUTHOR>
 * @date 2018-12-18
 */
@Service
public class AliyunRocketMQMonitorChecker extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AliyunRocketMQMonitorChecker.class);
	public static final String ONS_IYSAVER = "iysave";

	@Autowired
	private MeterRegistry registry;

	@Autowired(required = false)
	public Producer producer;

	@Autowired
	public MonitorConfig monitorConfig;

	private MQClientInstance mqClientInstance;
	private MQClientAPIImpl mqClientAPI;

	protected ThreadPoolExecutor threadPool = null;

	protected HashBasedTable<String, String, Double> diffs = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> delays = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> tpses = HashBasedTable.create();

	private static final long TIMEOUT_MILLIS = 20000;

	@Override
	public CheckerType type() {
		return CheckerType.ALIYUN_ROCKETMQ;
	}

	@Override
	public String name() {
		return "aliyun rocketmq堆积";
	}

	@Override
	protected boolean enableDebug() {
		return true;
	}

	@PostConstruct
	public void init() {
		if(producer == null){
			return;
		}
		threadPool = new ThreadPoolExecutor(20, 20, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
			new ThreadFactoryBuilder().setNameFormat(name() + "-pool-%d").build(),
			(Runnable r, ThreadPoolExecutor executor) -> {
				if (!executor.isShutdown()) {
					try {
						executor.getQueue().put(r);
					} catch (InterruptedException e) {
						LOGGER.logError(e.toString(), e);
						Thread.currentThread().interrupt();
					}
				}
			});
		threadPool.allowCoreThreadTimeOut(true);

	}

	@Override
	protected synchronized String innerCheck() {
		try {
			if(producer== null || !producer.isStarted()){
				return null;
			}
			if(mqClientAPI == null){
				DefaultMQProducerImpl defaultMQProducer = ((ProducerImpl)producer).getDefaultMQProducer().getDefaultMQProducerImpl();
				mqClientInstance = defaultMQProducer.getmQClientFactory();
				mqClientAPI = mqClientInstance.getMQClientAPIImpl();
			}
			TopicList topicList = mqClientAPI.getTopicListFromNameServer(TIMEOUT_MILLIS);
			for (String topic : topicList.getTopicList()) {
				try {
					if(!topic.contains(ONS_IYSAVER)){
						continue;
					}
					mqClientInstance.updateTopicRouteInfoFromNameServer(topic, false, null);
					TopicRouteData topicRouteData = mqClientInstance.getTopicRouteTable().get(topic);
					GroupList groupList = getGroupList(topic, topicRouteData);
					for (String group : groupList.getGroupList()) {
						if (isStoped) {
							return null;
						}
						threadPool.execute(() -> {
							try {
								ConsumeStats consumeStats = getConsumeStats(topic, group, topicRouteData);
								double diffTotal = 0;
//								if(monitorConfig.isCheckErrorMessageQueue() && topic.startsWith(monitorConfig.getErrorMessageQueueTopic())){
//									diffTotal = checkAndSetErrorOrderRdsMessageQueues(topic, consumeStats, diffTotal);
//								}else{
									diffTotal = consumeStats.computeTotalDiff();
//								}
								double tps = consumeStats.getConsumeTps();
								double delay = tps == 0 ? 0 : diffTotal / tps;
								if (diffTotal > 0 && tps == 0) {
									delay = -1;
								}
								diffs.put(topic, group, diffTotal);
								tpses.put(topic, group, tps);
								delays.put(topic, group, delay);

								Gauge.builder("monitor_aliyun_ons_diff", MonitoringSummaryData.getInstance(), x -> diffs.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_aliyun_ons_tps", MonitoringSummaryData.getInstance(), x -> tpses.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_aliyun_ons_delay", MonitoringSummaryData.getInstance(), x -> delays.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

							} catch (MQClientException e) {
								if(e.getResponseCode() == ResponseCode.CONSUMER_NOT_ONLINE){
									LOGGER.logError("topic=" + topic + " group=" + group + ", consumer不在线");
								}else{
									LOGGER.logError(e.getMessage(), e);
								}
							} catch (Exception e) {
								LOGGER.logError("topic=" + topic + " group=" + group+ " : " + e.getMessage(), e);
							}
						});
					}
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
			}
		} catch (Exception e) {
			LOGGER.logError("监控ONS堆积信息失败", e);
		}
		return null;
	}

	public GroupList getGroupList(String topic, TopicRouteData topicRouteData) throws Exception {
		for (BrokerData bd : topicRouteData.getBrokerDatas()) {
			String addr = bd.selectBrokerAddr();
			if (addr != null) {
				return mqClientAPI.queryTopicConsumeByWho(addr, topic, TIMEOUT_MILLIS);
			}
			break;
		}
		return null;
	}

	public ConsumeStats getConsumeStats(String topic, String consumerGroup, TopicRouteData topicRouteData) throws Exception {
		ConsumeStats result = new ConsumeStats();
		for (BrokerData bd : topicRouteData.getBrokerDatas()) {
			String addr = bd.selectBrokerAddr();
			if (addr != null) {
				ConsumeStats consumeStats =
					mqClientAPI.getConsumeStats(addr, consumerGroup, topic, TIMEOUT_MILLIS * 3);
				result.getOffsetTable().putAll(consumeStats.getOffsetTable());
				double value = result.getConsumeTps() + consumeStats.getConsumeTps();
				result.setConsumeTps(value);
			}
		}
		if (result.getOffsetTable().isEmpty()) {
			throw new MQClientException(ResponseCode.CONSUMER_NOT_ONLINE,
				"Not found the consumer group consume stats, because return offset table is empty, maybe the consumer not consume any message");
		}
		return result;
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		super.onApplicationEvent(event);
		if (!isStoped) {
			return;
		}
	}

}
