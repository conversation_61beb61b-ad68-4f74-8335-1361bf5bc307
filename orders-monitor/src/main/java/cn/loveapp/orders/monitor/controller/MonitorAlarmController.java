package cn.loveapp.orders.monitor.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.biyao.common.domain.AppAuthInfo;
import com.biyao.hongyuan.api.request.AfterSaleGetAcceptanceRequest;
import com.biyao.hongyuan.api.request.AfterSaleUpdateAcceptanceRequest;
import com.biyao.hongyuan.api.request.AfterSaleUpdateReturnLogisticsRequest;
import com.biyao.hongyuan.api.response.AfterSaleGetAcceptanceResponse;
import com.biyao.hongyuan.api.response.AfterSaleUpdateAcceptanceResponse;
import com.biyao.hongyuan.api.response.AfterSaleUpdateReturnLogiosticsResponse;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.biyao.BiyaoHongyuanSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.common.api.request.AyRefundGetRequest;
import cn.loveapp.orders.common.api.request.FullInfoRequest;
import cn.loveapp.orders.common.api.request.MessageRequest;
import cn.loveapp.orders.common.api.response.AyRefundGetResponse;
import cn.loveapp.orders.common.api.response.FullinfoResponse;
import cn.loveapp.orders.common.bo.UserInfoBo;
import cn.loveapp.orders.common.config.taobao.TaobaoFullInfoAppConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoRefundGetConfig;
import cn.loveapp.orders.common.dao.mongo.OrderReceiverDistrictListRepository;
import cn.loveapp.orders.common.dao.mongo.OrderRefundRepository;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderReceiverDistrictList;
import cn.loveapp.orders.common.entity.mongo.TcOrderRefund;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.exception.UnNeedRetryException;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.MessageApiPlatformHandleService;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.checker.MonitorCheckerFactory;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.entity.OrderSearchTrade;
import cn.loveapp.orders.monitor.entity.PromotionActivityTrade;
import cn.loveapp.orders.monitor.service.MonitorService;
import cn.loveapp.orders.monitor.service.OrderSearchTradeService;
import cn.loveapp.orders.monitor.service.PromotionActivityService;

/**
 * 订单监控告警手动控制接口
 *
 * <AUTHOR>
 * @date 2018-12-18
 */
@Validated
@RestController
@RequestMapping("monitor")
public class MonitorAlarmController {
	private final static String CHECK_TYPE = "FULLINFO,ORDER_COUNT,ORDER_DELAY,RDS_PUSH,ONS";
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorAlarmController.class);
	private static final String ALL_NICK = "ALL";

	public static final String ORDER_PGSQL_SLAVE_ALERT = "PostgreSQL备库延时告警";

	private static final Pattern DBID_PATTERN = Pattern.compile(".*?(\\d+)");

    /**
     * 退货退款
     */
    public final static Integer RETURN_GOODS_AND_REFUND = 1;

    /**
     * 仅退款
     */
    public final static Integer REFUND_ONLY = 2;

	@Autowired
	private MonitorCheckerFactory factory;

	@Autowired
	private MonitorService monitorServiceImpl;

	@Autowired
	private MonitorConfig monitorConfig;

	@Autowired
	private UserProductionInfoExtService userProductionInfoExtService;

	@Autowired
	private UserService userService;

	@Autowired
	private MessageApiPlatformHandleService messageCenterService;

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private MonitorAyTradeSearchESDao commonAyTradeSearchESDao;

	@Autowired
	@Qualifier("userInfoStringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;

	@Autowired
	private SubOrderRepository subOrderRepository;

	@Autowired
	private OrderReceiverDistrictListRepository orderReceiverDistrictListRepository;

	@Autowired
	private PromotionActivityService promotionActivityService;

	@Autowired
	private OrderSearchTradeService OrderSearchTradeService;

	@Autowired
	private OrderRefundRepository orderRefundRepository;

	@Autowired
	private TradeApiPlatformHandleService tradeApiPlatformHandleService;

	@Autowired
	private TaobaoFullInfoAppConfig taobaoFullInfoAppConfig;

	@Autowired
	private TaobaoRefundGetConfig taobaoRefundGetConfig;


	/**
	 * 获取监控信息
	 *
	 * @return
	 */
	@RequestMapping("data")
	public CommonApiResponse<MonitoringSummaryData> data() {
		return CommonApiResponse.success(getData());
	}

	/**
	 * 主动触发校验
	 *
	 * @param type 告警类型
	 * @return
	 */
	@RequestMapping("check/{type}")
	public CommonApiResponse check(@PathVariable String type, HttpServletRequest request) {
		CheckerType checkerType = CheckerType.of(type);
		if (factory.getChecker(checkerType) == null) {
			return CommonApiResponse.of(404, "checker 不存在");
		}
		CompletableFuture.runAsync(() -> factory.getChecker(checkerType).check());

		return CommonApiResponse.success();
	}

	/**
	 * 告警回调
	 */
	@RequestMapping("alertCallBack")
	public HttpStatus callback(@RequestBody String request) {
		LOGGER.logInfo("告警回调: " + request);
		if (!monitorConfig.isEnableAutoStopOrder()) {
			LOGGER.logInfo("禁止自动关闭订单对外服务, 忽略告警回调");
			return HttpStatus.OK;
		}
		JSONObject jsonObject = JSON.parseObject(request);
		String state = jsonObject.getString("state");
		String ruleName = jsonObject.getString("ruleName");
		JSONArray evalMatches = jsonObject.getJSONArray("evalMatches");

		boolean alerting = "alerting".equalsIgnoreCase(state);
		boolean ok = "ok".equalsIgnoreCase(state);

		Set<Integer> allDbIds = IntStream.rangeClosed(1, monitorConfig.getDbSize()).boxed().collect(Collectors.toSet());
		if (ok || alerting) {
			boolean isOnlySoldGet = ruleName.equalsIgnoreCase(ORDER_PGSQL_SLAVE_ALERT);
			if (ok) {
				monitorServiceImpl.setOrderServiceEnabled(allDbIds, true, ruleName, isOnlySoldGet);
			} else {
				Set<Integer> dbIds = Sets.newHashSet();
				for (int i = 0; i < evalMatches.size(); i++) {
					JSONObject metricObj = evalMatches.getJSONObject(i);
					String metric = metricObj.getString("metric");
					Matcher matcher = DBID_PATTERN.matcher(metric);
					int dbId = 0;
					if (matcher.find()) {
						dbId = NumberUtils.toInt(matcher.group(1));
						if (dbId > monitorConfig.getDbSize()) {
							LOGGER.logInfo("告警回调, dbId超出最大值, 按dbId-dbSize处理=" + (dbId - monitorConfig.getDbSize()));
							dbId = dbId - monitorConfig.getDbSize();
						}
					} else if ("iysaverdsorders".equalsIgnoreCase(metric)) {
						dbId = 1;
					}
					if (dbId > 0) {
						dbIds.add(dbId);
					}
				}
				if (!dbIds.isEmpty()) {
					monitorServiceImpl.setOrderServiceEnabled(dbIds, false, ruleName, isOnlySoldGet);
					allDbIds.removeAll(dbIds);
					monitorServiceImpl.setOrderServiceEnabled(allDbIds, true, ruleName, isOnlySoldGet);
				}
			}
		}
		return HttpStatus.OK;
	}

	/**
	 * 语音-告警回调
	 */
	@RequestMapping("alertCallBackPhone")
	public HttpStatus callbackPhone(@RequestBody String request) {
		LOGGER.logInfo("告警回调: " + request);
		JSONObject jsonObject = JSON.parseObject(request);
		String state = jsonObject.getString("state");
		boolean alerting = "alerting".equalsIgnoreCase(state);
		if (alerting) {
			String ruleName = jsonObject.getString("ruleName");
			String message = jsonObject.getString("message");
			monitorServiceImpl.sendPhoneMessage(message, ruleName);
		}
		return HttpStatus.OK;
	}

	/**
	 * 重新开通推送
	 */
	@RequestMapping("reopenPush")
	public CommonApiResponse reopenPush(@RequestParam("sellerNick") String sellerNick,
										@RequestParam(value = "platformId", defaultValue = "TAO") String platformId,
										@RequestParam(value = "appName", required = false) String appName,
										@RequestParam(value = "sendSoldGet", defaultValue = "false") boolean sendSoldGet,
										@RequestParam(value = "ignoreSameModified", defaultValue = "true") boolean ignoreSameModified) {
		platformId = OrderUtil.defaultPlatformId(platformId);
		LOGGER.logInfo("重新开通推送: " + sellerNick + " " + platformId + " " + appName);
		String[] sellerNicks = sellerNick.split(",");
		Map<String, String> result = new HashMap<>();
		for (String nick : sellerNicks) {
			result.put(nick, monitorServiceImpl.reopenPush(nick, platformId, appName, sendSoldGet, ignoreSameModified));
		}
		return CommonApiResponse.success(result);
	}


	/**
	 * 获取用户存单设置信息
	 */
	@RequestMapping(value = "userInfoExt", produces = MediaType.APPLICATION_JSON_VALUE)
	public String userInfo(@RequestParam String sellerNick, @RequestParam(defaultValue = CommonPlatformConstants.PLATFORM_TAO) String storeId,
						   @RequestParam(defaultValue = CommonAppConstants.APP_TRADE) String appName) {

		UserInfoBo userInfoBo = userService.getSellerInfoBySellerNick(sellerNick, storeId, appName);
		if (userInfoBo == null){
			return "该用户不存在";
		}

		UserProductionInfoExt ext = userProductionInfoExtService.queryBySellerNick(sellerNick, storeId, appName);
		PromotionActivityTrade promotionInfo = promotionActivityService.getPromotionActivityInfo(sellerNick, storeId, appName);
        OrderSearchTrade orderInfo = null;
        if (ext.getSellerId() != null) {
            orderInfo = OrderSearchTradeService.getOrderSearchInfo(sellerNick, ext.getSellerId(), storeId, appName);
        }

		Map<String, Object> map = Maps.newHashMap();

		try {
			MessageRequest request = new MessageRequest();
			request.setSellerId(userInfoBo.getSellerId());
			request.setSellerNick(userInfoBo.getSellerNick());
			request.setTopSession(userInfoBo.getAccessToken());
			boolean tmc = messageCenterService.isSubscribeUserMessageService(request, storeId, appName);
			boolean rds = messageCenterService.isSubscribeUserDbService(request, storeId, appName);
			map.put("isPushTmcOpened", tmc);
			map.put("isPushRdsOpened", rds);
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "", "获取推送状态失败: " + e.getMessage(), e);
		}

		HashOperations<String, String, String> op = stringRedisTemplate.opsForHash();
        String tagKey = initKey(sellerNick, storeId, appName);
        if (StringUtils.isNotEmpty(tagKey)) {
            String tag = op.get(tagKey, "tag");
            map.put("tag", tag);
        }

		map.put("userInfoBo_" + appName, userInfoBo);
		map.put("ext", ext);
		map.put("promotionInfo", promotionInfo);
		map.put("orderInfo", orderInfo);
		return JSON.toJSONString(map, SerializerFeature.WriteDateUseDateFormat);
	}

	/**
	 * 获取存单订单的基本信息(不包含任何敏感信息)
	 */
	@RequestMapping(value = "order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public String orderStatus(@NotEmpty String sellerNick, @NotEmpty String tid, @RequestParam(defaultValue = "TAO") String storeId, @RequestParam(required = false) String appName) throws UserNeedAuthException, UnNeedRetryException {
		tid = tid.trim();
		sellerNick = sellerNick.trim();
		storeId = storeId.trim();
		UserProductionInfoExt user = userProductionInfoExtService.queryBySellerNick(sellerNick, storeId, appName);
		if (user == null) {
			return "用户不存在";
		}
		TcOrder ayTradeMain = orderRepository.queryByTid(tid, storeId, user.getSellerId(), appName);

		List<String> tids = new ArrayList<>();
		tids.add(tid);
		List<TcSubOrder> tcSubOrders = subOrderRepository.findAySubOrderByTidAndStoreIdAndSellerId(tids, storeId, user.getSellerId(), appName);
		List<TcOrderReceiverDistrictList> tcOrderReceiverDistrictLists = orderReceiverDistrictListRepository.queryByTidAndSellerIdAndStoreId(tid, user.getSellerId(), storeId, appName);

		AyTradeSearchES ayTradeSearchES = AyTradeSearchES.of(user.getSellerId(), user.getCorpId(), storeId, appName, tid);
		ayTradeSearchES = commonAyTradeSearchESDao.getById(ayTradeSearchES);

		Map<String, Object> map = Maps.newHashMap();
		Map<String, Object> ayMap = Maps.newHashMap();
		Map<String, Object> apiMap = Maps.newHashMap();

		// 查询退款表信息
		List<TcOrderRefund> tcOrderRefunds = orderRefundRepository.queryByTid(tid, user.getSellerId(), storeId, appName);
		if (tcOrderRefunds != null) {
			ayMap.put("tcOrderRefunds", tcOrderRefunds);
			ArrayList<AyRefundGetResponse> ayRefundGetResponses = new ArrayList<>();
			// 查询API退款信息
			for (TcOrderRefund tcOrderRefund : tcOrderRefunds) {
				AyRefundGetRequest refundGetRequest = new AyRefundGetRequest();
				refundGetRequest.setRefundId(tcOrderRefund.getRefundId());
				refundGetRequest.setFields(taobaoRefundGetConfig.getFields());
				refundGetRequest.setSellerId(tcOrderRefund.getSellerId());
				AyRefundGetResponse ayRefundGetResponse = tradeApiPlatformHandleService.refundGet(refundGetRequest, sellerNick, storeId, appName);
				ayRefundGetResponses.add(ayRefundGetResponse);
			}
			apiMap.put("ayRefundGetResponses", ayRefundGetResponses);
		}

		// 查询fullInfo信息
		String topSession = userService.getAuthorization(sellerNick, user.getSellerId(), storeId, appName);
		FullInfoRequest fullInfoRequest = new FullInfoRequest();
		fullInfoRequest.setTid(tid);
		fullInfoRequest.setSellerNick(sellerNick);
		fullInfoRequest.setTopSession(topSession);
		if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
			fullInfoRequest.setApiFields(taobaoFullInfoAppConfig.getFileds());
		}
		FullinfoResponse fullInfoResponse = tradeApiPlatformHandleService.fullInfo(fullInfoRequest, storeId, appName);

		if (fullInfoResponse != null) {
			apiMap.put("fullInfoResponse", fullInfoResponse);
		}
		if (ayTradeMain != null) {
//			// 过滤掉敏感信息
//			ayTradeMain.setBuyerNick(secret);
//			ayTradeMain.setBuyerMessage(secret);
			ayMap.put("ayTradeMain", ayTradeMain);
		}
		if (tcSubOrders != null) {
//			// 过滤掉敏感信息
//			tcSubOrders.forEach((tcSubOrder)->{
//				tcSubOrder.setInvoiceNo(secret);
//				tcSubOrder.setLogisticsCompany(secret);
//			});
			ayMap.put("tcSubOrders", tcSubOrders);
		}
		if (tcOrderReceiverDistrictLists != null) {
			ayMap.put("tcOrderReceiverDistrictList", tcOrderReceiverDistrictLists);
		}
		if (ayTradeSearchES != null) {
//			// 过滤掉敏感信息
//			ayTradeSearchES.setBuyerNick(secret);
//			ayTradeSearchES.setReceiver(secret);
//			ayTradeSearchES.setBuyerMessage(ElasticsearchUtil.toList(secret));
//			ayTradeSearchES.setInvoiceNo(ElasticsearchUtil.toList(secret));
//			ayTradeSearchES.setLogisticsCompany(ElasticsearchUtil.toList(secret));
			ayMap.put("ayTradeSearchES", ayTradeSearchES);
		}

		map.put("ay", ayMap);
		map.put("api", apiMap);
		return JSON.toJSONString(map, SerializerFeature.WriteDateUseDateFormat);
	}

	private MonitoringSummaryData getData() {
		MonitoringSummaryData data = MonitoringSummaryData.getInstance();
		return data;
	}


    private String initKey(String sellerNick, String storeId, String appName) {
        try {
            if(StringUtils.isEmpty(storeId) || StringUtils.equalsAnyIgnoreCase(storeId,
                CommonPlatformConstants.PLATFORM_TAO, CommonPlatformConstants.PLATFORM_PDD)){
                if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName) || CommonAppConstants.APP_TRADE_ERP.equals(appName)) {
                    return storeId + ":" + appName + ":" + sellerNick;
                }
                // 兼容淘宝和PDD
                return URLEncoder.encode(sellerNick, "utf-8");
            }else{
                return storeId + ":" + appName + ":" + sellerNick;
            }
        } catch(Exception e) {
            LOGGER.logError(sellerNick, "-", "初始化Key失败", e);
        }
        return null;
    }
}
