package cn.loveapp.orders.monitor.dto;

import cn.loveapp.orders.common.entity.AyTradeSearchES;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * fullinfo检验详细结果
 *
 * <AUTHOR>
 * @date 2019-01-10
 */
@Data
public class FullinfoCheckResult
	extends BaseOrderApiCheckResult<AyTradeSearchES, FullinfoCheckResult.FailedOrder> {

	@Override
	public FailedOrder createOrderApiCheckFailed(AyTradeSearchES sample, LocalDateTime startTime,
		LocalDateTime endTime) {
		FailedOrder failedOrder = new FailedOrder();
		failedOrder.setSellerId(sample.getSellerId());
		failedOrder.setSellerNick(sample.getSellerNick());
		failedOrder.setStoreId(sample.getStoreId());
		failedOrder.setAppName(sample.getAppName());
		failedOrder.setTid(sample.getTid());
		return failedOrder;
	}

	/**
	 * 失败的订单
	 *
	 * <AUTHOR>
	 * @date 2019-01-11
	 */
	@Data
	public static class FailedOrder implements OrderApiCheckFailed {
		private String tid;
		private String sellerNick;
		private String sellerId;
		private String storeId;
		private String appName;
		private int dbId;
		/**
		 * 不一致的节点及描述
		 */
		private Map<String, List<String>> diffs = Maps.newHashMap();

		/**
		 * 添加不一致的节点及描述
		 *
		 * @param property
		 * @param desc
		 */
		@Override
		public void addDiffProperty(String property, String desc) {
			List<String> values = diffs.getOrDefault(property, Lists.newArrayList());
			if(values.isEmpty()){
				values.add(desc);
				diffs.put(property, values);
			}else{
				values.add(desc);
			}
		}

		@Override
		public Map<String, List<String>> getDiff() {
			return diffs;
		}
	}
}
