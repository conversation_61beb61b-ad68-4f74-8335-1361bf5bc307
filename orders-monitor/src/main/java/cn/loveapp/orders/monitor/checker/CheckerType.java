package cn.loveapp.orders.monitor.checker;

/**
 * 监控校验类型
 *
 * <AUTHOR>
 * @date 2018-12-21
 */
public enum CheckerType {

	/**
	 * 淘宝fullinfo正确性监控
	 */
	TAO_BAO_FULLINFO,

	/**
	 * 淘宝soldGet正确性监控
	 */
	TAO_BAO_SOLD_GET,

	/**
	 * 多平台soldGet正确性监控
	 */
	MULTI_SOLD_GET,

	/**
	 * 多平台fullInfo正确性监控
	 */
	MULTI_FULLINFO,

	/**
	 * 订单延时
	 */
	ORDER_DELAY,

	/**
	 * rds推送量监控
	 */
	RDS_PUSH,

	/**
	 * ons堆积量监控
	 */
	ONS,
	/**
	 * ons 订单专用堆积量监控
	 */
	ONS_ORDER,
	/**
	 * aliyun rocketmq堆积量监控
	 */
	ALIYUN_ROCKETMQ,
	/**
	 * es shared监控
	 */
	ES_SHARED,
	/**
	 * 修复订单tao_status
	 */
	FIX_TAO_STATUS,
	/**
	 * 修复订单丢失的MQ订单信息
	 */
	FIX_MISS_ORDER,
	/**
	 * 修复订单丢失的打印记录
	 */
	FIX_MISS_PRINT;

	public static CheckerType of(String type){
		return Enum.valueOf(CheckerType.class, type.toUpperCase());
	}
}
