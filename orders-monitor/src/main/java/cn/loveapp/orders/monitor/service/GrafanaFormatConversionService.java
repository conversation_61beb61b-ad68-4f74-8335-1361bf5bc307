package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.dto.grafana.GrafanaRequest;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-13 11:46
 * @Description: Grafana告警格式转机器人消息格式服务接口
 */
public interface GrafanaFormatConversionService {

	/**
	 * Grafana的Kubernetes消息体转换为机器人的请求体并进行请求
	 *
	 * @param grafanaRequest
	 */
	void convertAndSendByKubernetesAlarm(GrafanaRequest grafanaRequest);

	/**
	 * Grafana的Monitoring消息体转换为机器人的请求体并进行请求
	 *
	 * @param grafanaRequest
	 */
	void convertAndSendByMonitoringAlarm(GrafanaRequest grafanaRequest);

	/**
	 * Grafana的LogAlarm消息体转换为机器人的请求体并进行请求
	 *
	 * @param grafanaRequest
	 */
	void convertAndSendByLogAlarm(GrafanaRequest grafanaRequest);

    /**
     * Grafana的通用飞书转发
     *
     * @param hookId 飞书机器人最后的一串ID ( https://open.feishu.cn/open-apis/bot/v2/hook/{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx})
     * @param grafanaRequest
     */
    void convertAndSendByAlarm(String hookId, GrafanaRequest grafanaRequest);
}
