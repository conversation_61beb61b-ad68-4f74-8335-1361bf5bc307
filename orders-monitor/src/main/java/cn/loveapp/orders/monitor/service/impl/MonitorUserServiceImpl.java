package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.monitor.dao.dream.UserProductinfoTradeExtDao;
import cn.loveapp.orders.monitor.service.MonitorUserService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.cache.annotation.CacheDefaults;
import javax.cache.annotation.CacheResult;
import java.util.List;

/**
 * OrderServiceImpl
 *
 * <AUTHOR>
 * @date 2019-01-12
 */
@Service
@CacheDefaults(cacheName = "users")
public class MonitorUserServiceImpl implements MonitorUserService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorUserServiceImpl.class);

	@Autowired
	private UserProductinfoTradeExtDao userProductionInfoExtDao;

	@Override
	@CacheResult
	public List<UserProductionInfoExt> findAllOrderUser() {
		//从订单用户表获取所有的用户
		int pageSize = 3000;
		List<UserProductionInfoExt> list = Lists.newArrayList();
		Integer lastId = -1;
		for (int i = 0; i < Integer.MAX_VALUE; i++) {
			List<UserProductionInfoExt> result = userProductionInfoExtDao.queryAllByLimit(lastId, pageSize);
			list.addAll(result);
			LOGGER.logInfo(
				"获取用户nick数:" + result.size() + "  page:" + pageSize + " offset:" + (i+1) * pageSize);
			if (CollectionUtils.isEmpty(result) || result.size() < pageSize) {
				break;
			}
			lastId = result.get(result.size()-1).getId();
		}
		return list;
	}

}
