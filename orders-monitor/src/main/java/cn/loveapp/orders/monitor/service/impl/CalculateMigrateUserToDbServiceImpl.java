package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.dto.PrometheusInstanceDTO;
import cn.loveapp.orders.common.dto.PrometheusResultDTO;
import cn.loveapp.orders.common.entity.UserOrderMigrateProgress;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.PrometheusUtil;
import cn.loveapp.orders.monitor.dao.dream.UserOrderMigrateProgressDao;
import cn.loveapp.orders.monitor.service.CalculateMigrateUserToDbService;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: orders-services-group
 * @description: CalculateMigrateUserToDbServiceImpl
 * @author: Jason
 * @create: 2019-10-15 16:34
 **/
@Service
public class CalculateMigrateUserToDbServiceImpl implements CalculateMigrateUserToDbService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CalculateMigrateUserToDbServiceImpl.class);

	@Autowired
	private UserOrderMigrateProgressDao userOrderMigrateProgressDao;

	@Autowired
	private PrometheusUtil prometheusUtil;

	private static final String QUERY_ACTION = "query";

	@Value("${calculate.migrate.pool.size:14}")
	private int poolSize;

	@Value("${calculate.migrate.db.max.size:14}")
	private int maxDbSize;

	@Value("${calculate.migrate.db.new.size:9}")
	private int migrateDbNewSize;

	private HashMap<String, Integer> migrateDbMap;

	private int averageDbOrderCount;


	@Value("${prometheus.query.command:aliyun_acs_rds_dashboard_DiskUsage}")
	private String prometheusQueryCommand;

	private List<RdsVolumnBean> remainRdsVolumnList = new ArrayList<>();

	private HashMap<Integer, List<UserOrderMigrateProgress>> userOrderMigrateProgressMaps = new HashMap<>(32);

	private HashMap<String, String> cacheMap = new HashMap<>();

	ThreadPoolExecutor executor = null;

	/**
	 * 计算哪个用户应该迁移
	 */
	@Override
	public void startCalculateMigrateUserSpace() {
		initInstanceId();
		//1. 判断各db容量,并进行排序
		List<Pair<Integer, Long>> result = calculateDbOrderCount();
		//2. 根据db和需要迁移的数据计算剩余百分比
		boolean checker = checkerPriorityLevelDbVolume(result);
		if (!checker) {
			LOGGER.logWarn("根据db和需要迁移的数据计算剩余百分比, 第2步骤处理失败不能继续进行");
			return;
		}
		//3. 开始组装数据准备更新
		prepareUpdateUserMigrateDb();
		//4. 更新迁移db，准备迁移
		multiWriteMigrateUserData();
	}

	private void multiWriteMigrateUserData() {
		try {
			executor = new ThreadPoolExecutor(poolSize, poolSize, 0, TimeUnit.SECONDS, new ArrayBlockingQueue<>(poolSize), new ThreadFactoryBuilder().setNameFormat("migrate-calculate-order-%d").build());
			for(Map.Entry<Integer, List<UserOrderMigrateProgress>> migrateDataEntry: userOrderMigrateProgressMaps.entrySet()) {
				executor.execute(()->{
					updateUserMigrateDb(migrateDataEntry.getKey(), migrateDataEntry.getValue());
				});
			}
			executor.shutdown();
			executor.awaitTermination(10, TimeUnit.DAYS);
			LOGGER.logInfo(" 计算用户需要迁移的数据处理完毕");
		} catch (InterruptedException e) {
			LOGGER.logInfo(" 计算用户需要迁移的数据执行中断");
		} catch (Exception e) {
			LOGGER.logError(" 计算用户需要迁移的数据, 执行异常: " + e.getMessage(), e);
		}
	}

	private void initInstanceId() {
		migrateDbMap = new HashMap<>();
		migrateDbMap.put("rm-vy1jz1orha5nw9hom", 1);
		migrateDbMap.put("rm-vy1s50u8upxtyp302", 2);
		migrateDbMap.put("rm-vy16s8v8qa04shj62", 3);
		migrateDbMap.put("rm-vy18k6991iw4evn34", 4);
		migrateDbMap.put("rm-vy1wc8j52tigf279i", 5);
		migrateDbMap.put("rm-vy1c7310hv288dguq", 6);
		migrateDbMap.put("rm-vy1618r6a3ydg0hpj", 7);
		migrateDbMap.put("rm-vy1ml3be0l0tlcy13", 8);
		migrateDbMap.put("rm-k2j5dz6qz25a9992l", 9);
		migrateDbMap.put("rm-k2jg43s3nn1lbk39j", 10);
		migrateDbMap.put("rm-k2jr81p64ju5np80p", 11);
		migrateDbMap.put("rm-k2jofgkxhp76y3thc", 12);
		migrateDbMap.put("rm-k2jo2471mg6j46nba", 13);
		migrateDbMap.put("rm-k2jrjk1oz9i051q95", 14);
	}

	private List<Pair<Integer, Long>> calculateDbOrderCount() {
		// 计算总量
		long sumOrderTotal = userOrderMigrateProgressDao.queryBySumOrderTotal();
		// 平均值
		averageDbOrderCount = (int) Math.ceil(sumOrderTotal / (maxDbSize + migrateDbNewSize));
		// 计算每台db订单数量
		List<UserOrderMigrateProgress> userOrderMigrateProgresses = userOrderMigrateProgressDao.queryByGroupByDbId();

		List<Pair<Integer, Long>> result = new ArrayList<Pair<Integer, Long>>();

		for (UserOrderMigrateProgress userOrderMigrateProgress : userOrderMigrateProgresses) {
			Long migrateVolumn = userOrderMigrateProgress.getOrderTotal() - averageDbOrderCount;
			result.add(Pair.of(userOrderMigrateProgress.getFromDbId(), migrateVolumn));
		}
		LOGGER.logInfo("计算订单结果:" + JSON.toJSONString(result));
		return result;

	}

	private boolean checkerPriorityLevelDbVolume(List<Pair<Integer, Long>> orderDbCount) {
		LocalDateTime now = DateUtil.currentDate();
		long nowTimeSecond = DateUtil.parseLocalDateTime(now, DateUtil.SECOND_TIME_TYPE);
		LocalDateTime beforeOneMinutes = now.minusHours(1);
		long beforeOneMinutesSecond = DateUtil.parseLocalDateTime(beforeOneMinutes, DateUtil.SECOND_TIME_TYPE);
		String command = String.format("%s&start=%d&end=%d", prometheusQueryCommand, beforeOneMinutesSecond, nowTimeSecond);
		PrometheusResultDTO prometheusResultDTO = prometheusUtil.executeCommand(command, QUERY_ACTION);
		if (Objects.isNull(prometheusResultDTO)) {
			LOGGER.logWarn("prometheus返回数据异常");
			return false;
		}
		List<RdsVolumnBean> rdsVolumnBeanList = new ArrayList<>();
		for (PrometheusInstanceDTO prometheusInstanceDTO : prometheusResultDTO.getPrometheusDataDTO().getResult()) {
			String instanceId = prometheusInstanceDTO.getMetric().getInstanceId();
			//通过instanceId取回dbId
			if (!migrateDbMap.containsKey(instanceId)) {
				continue;
			}
			int dbId = migrateDbMap.get(instanceId);
			// 取出已经计算好的当前的dbId对应的要迁移的订单总数量
			long migrateCount = orderDbCount.get(dbId - 1).getRight();
			RdsVolumnBean rdsVolumnBean = new RdsVolumnBean();
			rdsVolumnBean.setRemainVolumnPercentage(100 - prometheusInstanceDTO.getValue().get(1));
			rdsVolumnBean.setInstanceId(instanceId);
			rdsVolumnBean.setDbId(dbId);
			rdsVolumnBean.setMigrateOrderCount((int)migrateCount);
			rdsVolumnBeanList.add(rdsVolumnBean);
		}
		if (rdsVolumnBeanList.size() < maxDbSize) {
			LOGGER.logError("prometheus 取回的db总数有问题, dbMap=>" + JSON.toJSONString(rdsVolumnBeanList));
			return false;
		}
		remainRdsVolumnList = rdsVolumnBeanList.stream().sorted(Comparator.comparing(RdsVolumnBean::getRemainVolumnPercentage))
			.collect(Collectors.toList());
		LOGGER.logWarn("对计算结果进行重新排序,按照容量大小进行重组:" + JSON.toJSONString(remainRdsVolumnList));
		return true;
	}

	public void prepareUpdateUserMigrateDb() {
		CompletableCalculate();
	}

	private void CompletableCalculate() {
		int toDbId = maxDbSize + 1;
		int toDbSum = averageDbOrderCount;
		boolean isUnfinished = false;
		bout: for (RdsVolumnBean rdsVolumnBean : remainRdsVolumnList) {
			// 负数的不跑
			if (rdsVolumnBean.getMigrateOrderCount() <= 0) {
				continue;
			}
			// dbId超过了不能跑
			if (toDbId > maxDbSize + migrateDbNewSize) {
				LOGGER.logWarn("当前要去往的dbId还没有准备不能进行工作");
				break;
			}
			MigrateInnerProgressBean migrateInnerProgressBean =
				new MigrateInnerProgressBean(toDbId, toDbSum, rdsVolumnBean, false, 50, "desc").invoke();
			if (Objects.isNull(migrateInnerProgressBean)) {
				LOGGER.logWarn("对dbId:"+ rdsVolumnBean.getDbId() + "迁移准备数据未更新完成");
				isUnfinished = true;
				break bout;
			}
			toDbId = migrateInnerProgressBean.getToDbId();
			toDbSum = migrateInnerProgressBean.getToDbSum();
			rdsVolumnBean.setMigrateOrderCount(0);
			LOGGER.logInfo("对dbId:"+ rdsVolumnBean.getDbId() + "迁移准备数据更新完成");
		}
		if (isUnfinished) {
			LOGGER.logInfo("有数据库没完成迁移，需要看看有没有数据库的容量过剩，导入一些");
			List<RdsVolumnBean> availableExistRdsList = remainRdsVolumnList.stream().filter(c -> c.getMigrateOrderCount() < 0).sorted(Comparator.comparing(RdsVolumnBean::getRemainVolumnPercentage).reversed()).collect(Collectors.toList());
			for (RdsVolumnBean rdsVolumnBean : remainRdsVolumnList) {
				// 负数的不跑
				if (rdsVolumnBean.getMigrateOrderCount() <= 0) {
					continue;
				}
				for (RdsVolumnBean volumnBean : availableExistRdsList) {
					toDbSum = Math.abs(volumnBean.getMigrateOrderCount());
					toDbId = volumnBean.getDbId();
					MigrateInnerProgressBean migrateInnerProgressBean =
						new MigrateInnerProgressBean(toDbId, toDbSum, rdsVolumnBean, true, 20, "asc").invoke();
					if (Objects.isNull(migrateInnerProgressBean)) {
						rdsVolumnBean.setMigrateOrderCount(rdsVolumnBean.getMigrateOrderCount() - toDbSum);
						volumnBean.setMigrateOrderCount(0);
						LOGGER.logInfo("对dbId:"+ rdsVolumnBean.getDbId() + "迁移准备数据未更新完成");
					} else {
						if (migrateInnerProgressBean.getToDbSum() > toDbSum) {
							volumnBean.setMigrateOrderCount(0);
						} else {
							volumnBean.setMigrateOrderCount(0 - migrateInnerProgressBean.getToDbSum());
						}
						rdsVolumnBean.setMigrateOrderCount(0);
						LOGGER.logInfo("原dbId:"+ volumnBean.getDbId() + "数据补充结束, 还可以补充的订单量为:" + migrateInnerProgressBean.getToDbSum());
						LOGGER.logInfo("对dbId:"+ rdsVolumnBean.getDbId() + "迁移准备数据更新完成");
						break;
					}
				}
			}
		}
		LOGGER.logInfo("剩余迁移的各db数据, remainRdsVolumnList: " + JSON.toJSONString(remainRdsVolumnList));
	}

	/**
	 * 更新用户要迁移至的DbId
	 */
	public void updateUserMigrateDb(Integer dbId, List<UserOrderMigrateProgress> userOrderMigrateProgressList) {
		for (UserOrderMigrateProgress userOrderMigrateProgress : userOrderMigrateProgressList) {
			try {
				LOGGER.logInfo(userOrderMigrateProgress.getSellerId(), "-", "更新toDbId完成, toDbId为:" + userOrderMigrateProgress.getToDbId());
				userOrderMigrateProgressDao.migrateOrderUpdate(userOrderMigrateProgress);
			} catch (Exception e) {
				LOGGER.logError(userOrderMigrateProgress.getSellerId(), "-", "更新失败, 原因为:" + e.getMessage(), e);
			}
		}
	}

	@Data
	public static class RdsVolumnBean {
		private Integer dbId;
		private String instanceId;
		private Integer migrateOrderCount;
		private Double remainVolumnPercentage;
	}

	private class MigrateInnerProgressBean {
		private int toDbId;
		private int toDbSum;
		private RdsVolumnBean rdsVolumnBean;
		private boolean skipToDbIdChecker;
		private int skipId;
		private String orderBy;

		public MigrateInnerProgressBean(int toDbId, int toDbSum, RdsVolumnBean rdsVolumnBean, boolean skipToDbIdChecker, int skipId, String orderBy) {
			this.toDbId = toDbId;
			this.toDbSum = toDbSum;
			this.rdsVolumnBean = rdsVolumnBean;
			this.skipToDbIdChecker = skipToDbIdChecker;
			this.skipId = skipId;
			this.orderBy = orderBy;
		}

		public int getToDbId() {
			return toDbId;
		}

		public int getToDbSum() {
			return toDbSum;
		}

		public boolean getSkipToDbIdChecker() {
			return skipToDbIdChecker;
		}

		public MigrateInnerProgressBean invoke() {
			List<UserOrderMigrateProgress> userOrderMigrateProgressList = userOrderMigrateProgressDao.queryByDbId(rdsVolumnBean.getDbId());
			if ("asc".equals(orderBy)) {
				userOrderMigrateProgressList = userOrderMigrateProgressList.stream().sorted(Comparator.comparing(UserOrderMigrateProgress::getOrderTotal))
					.collect(Collectors.toList());
			} else if ("desc".equals(orderBy)) {
				userOrderMigrateProgressList = userOrderMigrateProgressList.stream().sorted(Comparator.comparing(UserOrderMigrateProgress::getOrderTotal).reversed())
					.collect(Collectors.toList());
			}
			LOGGER.logInfo("dbId:" + rdsVolumnBean.getDbId() + ", 需要迁移的订单总数为:" + rdsVolumnBean.getMigrateOrderCount()
				+ ", 当前数据库剩余容量占比" + rdsVolumnBean.getRemainVolumnPercentage() + "%");
			int nextStepIndex = 0;
			int i = 0;
			int k = 0;
			int sum = 0;
			out: while (true) {
				for (; i <= userOrderMigrateProgressList.size(); i++) {
					if (k == userOrderMigrateProgressList.size()) {
						LOGGER.logWarn("当前k值已经与集合总量一致不能再进行处理, k:" + k +",dbId:"+ rdsVolumnBean.getDbId());
						break out;
					}
					// 如果i 等于list集合最大值 最后一笔元素不做处理 退出for循环
					if (i == userOrderMigrateProgressList.size()) {
						LOGGER.logWarn("当前i值已经与集合总量一致,需要i值重新初始化并且进行移位, i:"+i+",dbId:"+ rdsVolumnBean.getDbId());
						nextStepIndex = k + 1;
						k = k + 1;
						i = k;
						break;
					}
					// 如果i 不是下一个要执行的i 不做处理
					if (i != nextStepIndex) {
						continue;
					}
					if (cacheMap.containsKey(userOrderMigrateProgressList.get(i).getSellerId())) {
						nextStepIndex = i + skipId;
						LOGGER.logWarn("当前用户:" + userOrderMigrateProgressList.get(i).getSellerId() + ", 在cache中存在不应该再被计算");
						continue;
					}
					// 计算去向的db容量是否足够 如果不足对去向db+1 处理
					toDbSum-=userOrderMigrateProgressList.get(i).getOrderTotal();
					if (toDbSum < 0) {
						LOGGER.logWarn("当前dbId容量不足, 还需要db空间为:" + (toDbSum+userOrderMigrateProgressList.get(i).getOrderTotal())
							+ ", 需要移位处理, 原始dbId为:" + rdsVolumnBean.getDbId()
							+ ",orderTotal为:" + userOrderMigrateProgressList.get(i).getOrderTotal());
						toDbId += 1;
						// dbId超过了不能跑
						if (toDbId > maxDbSize + migrateDbNewSize || skipToDbIdChecker) {
							LOGGER.logWarn("当前要去往的dbId:"+toDbId+",还没有准备不能进行工作, skipToDbIdChecker=>" + skipToDbIdChecker);
							Long remainNeedSum = toDbSum+userOrderMigrateProgressList.get(i).getOrderTotal();
							rdsVolumnBean.setMigrateOrderCount(remainNeedSum.intValue());
							return null;
						}
						LOGGER.logWarn("截止当前用户>" + userOrderMigrateProgressList.get(i).getSellerNick() + ", 开始移位db处理, 移位后的dbId为>" + toDbId);
						//重置下一节点的db总量
						toDbSum = averageDbOrderCount;
						toDbSum-=userOrderMigrateProgressList.get(i).getOrderTotal();
					}
					// 计算目前迁移总和是否超过预计要迁移的总和 超过就退出 换下一个dbId进来
					if (sum >= rdsVolumnBean.getMigrateOrderCount()) {
						LOGGER.logWarn("目前迁移总和已经超过预计要迁移的总和, 换下一个需要迁移的dbId进来, sum:" + sum
							+ ",dbId:" + rdsVolumnBean.getDbId() + ",migrateOrderCount:" + rdsVolumnBean.getMigrateOrderCount());
						break out;
					}
					LOGGER.logInfo(userOrderMigrateProgressList.get(i).getSellerNick(), "-", "被选中用户将移至新db中, 原始dbId为:" + rdsVolumnBean.getDbId()
						+ ",即将去向的dbId为:" + toDbId + ",orderTotal为:" + userOrderMigrateProgressList.get(i).getOrderTotal());
					sum+=userOrderMigrateProgressList.get(i).getOrderTotal();
					String sellerId = userOrderMigrateProgressList.get(i).getSellerId();
					int fromDbId = userOrderMigrateProgressList.get(i).getFromDbId();
					int pkId = userOrderMigrateProgressList.get(i).getId();
					List<UserOrderMigrateProgress> userOrderMigrateProgresses = userOrderMigrateProgressMaps.getOrDefault(fromDbId, new ArrayList<>());
					UserOrderMigrateProgress userOrderMigrateProgress = new UserOrderMigrateProgress();
					userOrderMigrateProgress.setId(pkId);
					userOrderMigrateProgress.setToDbId(toDbId);
					userOrderMigrateProgress.setSellerId(sellerId);
					userOrderMigrateProgress.setGmtModified(LocalDateTime.now());
					userOrderMigrateProgresses.add(userOrderMigrateProgress);
					userOrderMigrateProgressMaps.put(fromDbId, userOrderMigrateProgresses);
					cacheMap.put(userOrderMigrateProgressList.get(i).getSellerId(), userOrderMigrateProgressList.get(i).getSellerNick());
					// 添加完成一个迁移用户
					// 计算下一次要执行的index
					nextStepIndex = i + skipId;
				}
			}
			return this;
		}
	}
}
