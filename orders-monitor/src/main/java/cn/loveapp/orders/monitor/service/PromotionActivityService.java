package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.entity.PromotionActivityTrade;

/**
 * 交易-营销活动赠送主表(PromotionActivityTrade)表服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-23 10:38
 */
public interface PromotionActivityService {

	/**
	 *  通过sellerNick获取单条最新营销活动赠送信息
	 *
	 * @param sellerNick
	 * @param platformId
	 * @param appName
	 * @return
	 */
	PromotionActivityTrade getPromotionActivityInfo(String sellerNick, String platformId, String appName);

}
