package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.dto.SoldGetCheckResult;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.OrdersApiService;
import cn.loveapp.orders.monitor.service.TaobaoApiService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.api.ApiException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * soldGet正确率校验
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Service
public class SoldGetMonitorChecker extends AbstractOrderApiMonitorChecker<UserProductionInfoExt> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SoldGetMonitorChecker.class);
	public static final String TRADES_SOLD_GET_RESPONSE = "trades_sold_get_response";
	public static final String TRADES = "trades";
	public static final String TRADE = "trade";
	public static final String TID = "tid";
	protected static final int TABLE_COUNT = 1;

	protected Set<String> jsonStringKeys = Sets.newHashSet("trade_attr");

	@Autowired
	private TaobaoApiService taobaoApiService;

	@Autowired
	@Qualifier("ordersApiServiceImpl")
	private OrdersApiService ordersApiService;

	@Autowired
	private MonitorAyTradeSearchESDao ayTradeSearchDao;

	protected SoldGetCheckResult soldGetCheckResult;

	@Override
	public CheckerType type() {
		return CheckerType.TAO_BAO_SOLD_GET;
	}

	@Override
	public String name() {
		return "tao bao soldGet";
	}

	@Override
	protected int getSampleTableCount() {
		return TABLE_COUNT;
	}

	@Override
	protected String getResultJsonRoot() {
		return TRADES_SOLD_GET_RESPONSE;
	}

	@Override
	protected String getStoreId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}

	@Override
	protected BaseOrderApiCheckResult getOrderApiCheckResult() {
		return soldGetCheckResult;
	}

	@Override
	protected Set<String> checkAsJsonStringKeys(){
		return jsonStringKeys;
	}

	@Override
	protected Set<String> getNeedIgnoreKeys(){
		return monitorConfig.getFullinfoIgnores();
	}

	@Override
	protected synchronized String innerCheck() {
		double accuracyRate;
		soldGetCheckResult = new SoldGetCheckResult();
		soldGetCheckResult.setOrderStatus(monitorConfig.getSoldGetStatus());

		accuracyRate = checkSampleInTimeRange(monitorConfig.getSoldGetTimeRange(), null,
			monitorConfig.getSoldGetNumber());

		MonitoringSummaryData.getInstance().setSoldGetAccuracyRate(accuracyRate);

		if (!isStoped) {
			postCheck();
		}

		return String.valueOf(accuracyRate);
	}

	@Override
	protected String requestOrderServiceApi(UserProductionInfoExt sample, LocalDateTime startTime,
		LocalDateTime endTime) throws ApiServerErrorException, IOException {
		return ordersApiService
			.soldGet(sample.getSellerId(), sample.getSellerNick(), null, soldGetCheckResult.getOrderStatus(),
				startTime, endTime, 0L);
	}

	@Override
	protected String requestPlatformApi(UserProductionInfoExt sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException {
		return taobaoApiService.soldGet(sample.getSellerNick(), startTime, endTime);
	}

	@Override
	protected boolean needCompareJson(UserProductionInfoExt sample, OrderApiCheckFailed failedOrder,
		LocalDateTime taobaoApiTime, JSONObject apiJson, JSONObject taobaoJson, LocalDateTime startTime,
		LocalDateTime endTime) {

		String path = "$." + TRADES_SOLD_GET_RESPONSE + "." + TRADES + "." + TRADE;

		JSONArray trades = (JSONArray)JSONPath.eval(taobaoJson, path);
		if (trades == null) {
			return true;
		}
		Set<String> taoTids = trades.stream().map(o -> ((JSONObject)o).getString(TID)).collect(Collectors.toSet());

		Set<String> apiTids = Sets.newHashSet();
		trades = (JSONArray)JSONPath.eval(apiJson, path);
		if (trades != null) {
			apiTids = trades.stream().map(o -> ((JSONObject)o).getString(TID)).collect(Collectors.toSet());
		}

		//获取tid差集
		Collection<String> diff = CollectionUtils.disjunction(taoTids, apiTids);

		if (diff.size() > 0) {
			failedOrder.addDiffProperty("差异tid", StringUtils.join(diff, ","));
			try {
				NetworkUtil.http("http://mcs.aiyongbao.com/1.gif?p=TD20170614170642&n=" + sample.getSellerNick()
						+ "&e=rdslost0102&d1=" + diff.size() + "&m1=" + startTime + "&m2=" + endTime, null, false, "", null,
					false, false, "");
			} catch (IOException e) {
				LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), e.getMessage(), e);
			}
		}
		return true;
	}

	protected void postCheck() {
		soldGetCheckResult.setAccuracyRate(MonitoringSummaryData.getInstance().getSoldGetAccuracyRate());
		soldGetCheckResult.getOrderMissCheckResult().increment(validNumber.longValue());

		for (SoldGetCheckResult.FailedSoldGet failedSoldGet : soldGetCheckResult.getFailedOrders()) {
			long count =
				countOrdersByTime(failedSoldGet.getStartTime(), failedSoldGet.getEndTime(), failedSoldGet.getSellerId());
			failedSoldGet.setSearchDbCount(count);
		}

		//设置dbId
		monitorService.setDbId(soldGetCheckResult);

		//发送校验结果通知
		monitorService.sendCheckResultNotify(CheckerType.TAO_BAO_SOLD_GET.name(), soldGetCheckResult,
			new DecimalFormat("#.####%").format(soldGetCheckResult.getAccuracyRate()),"sold_get");
	}


	/**
	 * 查询指定时间内的不重复的订单卖家nick(seller_nick, seller_id)
	 *
	 * @param startTime 查询起始时间
	 * @param endTime   查询结束时间
	 * @param status    无用
	 * @param listId
	 * @return
	 */
	@Override
	public List<UserProductionInfoExt> querySamples(LocalDateTime startTime, LocalDateTime endTime, String status,
		long listId) {
		List<UserProductionInfoExt> all = monitorUserService.findAllOrderUser();
		all = all.stream().filter(user-> CommonPlatformConstants.PLATFORM_TAO.equals(user.getStoreId())).collect(Collectors.toList());

		Map<Integer, List<UserProductionInfoExt>> maps = all.stream().collect(Collectors.groupingBy(UserProductionInfoExt::getDbId));
		int unit = monitorConfig.getSoldGetNumber()/monitorConfig.getDbSize();

		List<UserProductionInfoExt> result = Lists.newArrayList();
		unit = unit <=0 ? 1: unit;
		for(List<UserProductionInfoExt> dbUsers  : maps.values()){
			if(dbUsers!=null && dbUsers.size() > unit){
				Collections.shuffle(dbUsers);
				List<UserProductionInfoExt> samples = dbUsers.subList(0, unit);
				result.addAll(samples);
			}else{
				result.addAll(dbUsers);
			}
		}
		LOGGER.logInfo(name() + " 样本数:" + result.size());
		return result;
	}

	/**
	 * 查询卖家指定时间内的订单
	 *
	 * @param startTime 查询起始时间
	 * @param endTime   查询结束时间
	 * @param sellerId
	 * @return
	 */
	protected long countOrdersByTime(LocalDateTime startTime, LocalDateTime endTime, String sellerId) {
		return ayTradeSearchDao
			.countOrdersBySellerId(startTime, endTime, monitorConfig.getSoldGetStatus(), sellerId,
				CommonPlatformConstants.PLATFORM_TAO, null);
	}

	@Override
	protected void error(UserProductionInfoExt sample, String message, Throwable e) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message, e);
	}

	@Override
	protected void error(UserProductionInfoExt sample, String message) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void info(UserProductionInfoExt sample, String message) {
		LOGGER.logInfo(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void warn(UserProductionInfoExt sample, String message) {
		LOGGER.logWarn(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}
}
