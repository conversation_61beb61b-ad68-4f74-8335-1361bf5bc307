package cn.loveapp.orders.monitor.service;

import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.ClearCrossOrderRequest;
import org.springframework.dao.DataAccessException;

import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.Set;

/**
 * MonitorService
 *
 * <AUTHOR>
 * @date 2018-12-19
 */
public interface MonitorService {

	/**
	 * 发送校验结果通知
	 *
	 * @param title
	 * @param result
	 * @param titleSuffix
	 * @param templateName 发送邮件使用模版的名称
	 */
	<T> void sendCheckResultNotify(String title, T result, String titleSuffix,String templateName);

	/**
	 * 订单指定dbId的对外服务是否启用
	 *
	 * @param dbId
	 * @param onlySoldGet
	 * @return
	 * @throws DataAccessException
	 */
	boolean isOrderServiceEnabled(int dbId, boolean onlySoldGet) throws DataAccessException;

	/**
	 * 设置校验结果中dbId
	 *
	 * @param result
	 */
	void setDbId(BaseOrderApiCheckResult result);

	/**
	 * 设置订单对外服务是否启用 (enable为false时, 高危操作, 需注意)
	 *
	 * @param dbIds
	 * @param enable
	 * @param reason 原因
	 * @param onlySoldGet 只修改soldget状态
	 * @throws DataAccessException
	 */
	void setOrderServiceEnabled(Set<Integer> dbIds, boolean enable, String reason, boolean onlySoldGet) throws DataAccessException;

	/**
	 * 通过日志发送语音
	 *
	 * @param message
	 * @param source 来源
	 * @return
	 */
	boolean sendPhoneMessage(@NotNull String message, @NotNull  String source);

	/**
	 * 重新开通推送库
	 *
	 * @param sellerNick
	 * @param platformId
	 * @param appName
	 * @param sendSoldGet
	 * @param ignoreSameModified
	 * @return
	 */
	String reopenPush(@NotNull String sellerNick, @NotNull String platformId, String appName, boolean sendSoldGet, boolean ignoreSameModified);


    /**
     * 清理串单用户订单
     * @return
     */
	Map<String, String> clearCrossOrder(ClearCrossOrderRequest request);

}
