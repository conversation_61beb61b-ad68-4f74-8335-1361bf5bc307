package cn.loveapp.orders.monitor.dto.purchase.request;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 采购单查询导出请求request
 * 
 * @program: orders-services-group
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/4/25 18:59
 **/
@Data
public class SearchPurchaseRequest {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否只返回异常订单
     */
    private boolean isOnlyError = true;

    /**
     * 是否发送邮件
     */
    private boolean isSendEmail = false;

}
