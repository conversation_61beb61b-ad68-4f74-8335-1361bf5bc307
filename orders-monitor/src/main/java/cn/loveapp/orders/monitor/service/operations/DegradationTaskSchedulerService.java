package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.entity.operations.DegradationTask;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 降级任务任定时服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-14 22:10
 */
public interface DegradationTaskSchedulerService {
	/**
	 * 获取当前任务
	 *
	 * @return
	 */
	List<String> getTaskList();

	/**
	 * 添加任务
	 *
	 * @param degradationTask
	 * @param taskName
	 * @param taskTime
	 */
	void addTask(DegradationTask degradationTask, String taskName, LocalDateTime taskTime);

	/**
	 * 关闭任务
	 *
	 * @param taskName
	 * @return
	 */
	boolean stopTask(String taskName);
}
