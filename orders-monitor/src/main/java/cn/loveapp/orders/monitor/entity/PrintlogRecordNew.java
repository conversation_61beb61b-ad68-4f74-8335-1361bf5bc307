package cn.loveapp.orders.monitor.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (PrintlogRecordNew)实体类
 *
 * <AUTHOR>
 * @date 2019-08-04 21:32:37
 */
@Data
public class PrintlogRecordNew implements Serializable {
    private static final long serialVersionUID = 306626584474074097L;
    /**
     *编号
     */
    private Integer id;
    /**
     *用户名
     */
    private String sellernick;
    /**
     *订单编号
     */
    private String tid;
    /**
     *运单号(母单号)
     */
    private String voice;
    /**
     *打印时间
     */
    private LocalDateTime operatetime;
    /**
     *快递公司
     */
    private String delivercompany;
	/**
	 *快递单打印状态
	 */
	private Integer expfacestatus;
	/**
	 *电子面单打印状态
	 */
	private Integer elefacestatus;
	/**
	 *快递单打印状态 1：已打
	 */
	private Integer courier;
	/**
	 *发货单打印状态 1：已打
	 */
	private Integer invoice;
	/**
	 *电子面单打印状态 1：已打
	 */
	private Integer surface;
}
