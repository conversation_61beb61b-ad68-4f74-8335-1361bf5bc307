package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.OrdersApiService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * OrdersApiServiceImpl
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Service("ordersApiServiceImpl")
public class OrdersApiServiceImpl implements OrdersApiService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersApiServiceImpl.class);
	private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	@Autowired
	private MonitorConfig monitorConfig;

	@Override
	public String fullinfo(AyTradeSearchES tradeaSearch) throws ApiServerErrorException, IOException {
		// 创建参数队列
		List<NameValuePair> params = new ArrayList<>();
		params.add(new BasicNameValuePair("tid", tradeaSearch.getTid()));
		params.add(new BasicNameValuePair("fields", monitorConfig.getFullinfoFields()));
		return request(tradeaSearch.getSellerId(), tradeaSearch.getCorpId(), tradeaSearch.getSellerNick(),
			"/order/getOrderInfoBySingle", params);
	}

	@Override
	public String soldGet(String sellerId, String sellerNick, String appName, String status, LocalDateTime startTime,
		LocalDateTime endTime, long pageNo) throws ApiServerErrorException, IOException {
		// 创建参数队列
		List<NameValuePair> params = new ArrayList<>();
		params.add(new BasicNameValuePair("fields", monitorConfig.getSoldGetFields()));
		params.add(new BasicNameValuePair("page_no", Long.toString(pageNo <= 0 ? 1L : pageNo)));
		params.add(new BasicNameValuePair("page_size", Long.toString(monitorConfig.getSoldGetPageSize())));
		params.add(new BasicNameValuePair("start_created", dateTimeFormatter.format(startTime)));
		params.add(new BasicNameValuePair("end_created", dateTimeFormatter.format(endTime)));
		params.add(new BasicNameValuePair("use_has_next", Boolean.toString(pageNo >= 1)));
		params.add(new BasicNameValuePair("status", status));
		return request(sellerId, sellerId, sellerNick, "/tradeList/soldGet", params);
	}

	@Override
	public String detailInfoGet(AyTradeSearchES tradeaSearch, String fields) throws ApiServerErrorException, IOException {
		// 创建参数队列
		List<NameValuePair> params = new ArrayList<>();
		params.add(new BasicNameValuePair("taoTid", tradeaSearch.getTid()));
		params.add(new BasicNameValuePair("tid", tradeaSearch.getTid()));
		params.add(new BasicNameValuePair("fields", fields));
		params.add(new BasicNameValuePair("appName", tradeaSearch.getAppName()));
		params.add(new BasicNameValuePair("storeId", tradeaSearch.getStoreId()));
		params.add(new BasicNameValuePair("platformId", tradeaSearch.getStoreId()));
		return request(tradeaSearch.getSellerId(), tradeaSearch.getCorpId(), tradeaSearch.getSellerNick(),
			"/aiyongTrade/detail.info.get", params);
	}

	@Override
	public String searchList(String sellerId, String sellerNick, String fields, String status, LocalDateTime startTime,
		LocalDateTime endTime, long pageNo, long pageSize, String sort, String platformId, String appName) throws ApiServerErrorException, IOException {
		// 创建参数队列
		List<NameValuePair> params = new ArrayList<>();
		params.add(new BasicNameValuePair("fields", fields));
		params.add(new BasicNameValuePair("pageNo", Long.toString(pageNo <= 0 ? 1L : pageNo)));
		params.add(new BasicNameValuePair("pageSize", Long.toString(pageSize)));
		params.add(new BasicNameValuePair("timeFilterBy", "created"));
		params.add(new BasicNameValuePair("startTime", dateTimeFormatter.format(startTime)));
		params.add(new BasicNameValuePair("endTime", dateTimeFormatter.format(endTime)));
		params.add(new BasicNameValuePair("sortField", "created"));
		params.add(new BasicNameValuePair("sortDirection", sort));
		params.add(new BasicNameValuePair("status", status));
		params.add(new BasicNameValuePair("platformId", platformId));
		params.add(new BasicNameValuePair("storeId", platformId));
		params.add(new BasicNameValuePair("appName", appName));
		return request(sellerId, sellerId, sellerNick, "/aiyongTrade/search.list.get", params);
	}

	private void setHearders(HttpPost post) {
		post.setHeader(new BasicHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8"));
		post.setHeader(new BasicHeader("Host", monitorConfig.getServiceHost()));
		if (StringUtils.isNoneEmpty(monitorConfig.getServiceCookie())) {
			post.setHeader(new BasicHeader("Cookie", monitorConfig.getServiceCookie() + "=true"));
		}
	}

	public String request(String sellerId, String corpId, String sellerNick, String api, List<NameValuePair> params)
		throws ApiServerErrorException, IOException {
		// 创建参数队列
		List<NameValuePair> allParams = Lists.newArrayList();
		allParams.add(new BasicNameValuePair("sellerId", sellerId));
		allParams.add(new BasicNameValuePair("nick", sellerNick));
		allParams.add(new BasicNameValuePair("listId", StringUtils.substring(sellerId, -2)));
		allParams.add(new BasicNameValuePair("corpId", corpId));
		allParams.add(new BasicNameValuePair("isVip", "1"));
		allParams.add(new BasicNameValuePair("method", "method"));
		allParams.add(new BasicNameValuePair("trade_source", "TAO"));
		allParams.add(new BasicNameValuePair("source", "monitor"));
		// 禁止显示合单
		allParams.add(new BasicNameValuePair("platform", "ww"));
		if (params != null) {
			allParams.addAll(params);
		}

		CloseableHttpClient httpClient = null;
		CloseableHttpResponse response = null;
		try {
			httpClient = HttpClients.createDefault();

			HttpPost post = new HttpPost(monitorConfig.getServiceUrl() + api);
			setHearders(post);
			post.setEntity(new UrlEncodedFormEntity(allParams, "utf-8"));

			//设置请求和传输超时时间
			RequestConfig requestConfig =
				RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();
			post.setConfig(requestConfig);

			response = httpClient.execute(post);

			String respone = EntityUtils.toString(response.getEntity());
			if (response.getStatusLine().getStatusCode() >= HttpStatus.SC_INTERNAL_SERVER_ERROR) {
				throw new ApiServerErrorException(
					"订单api服务器" + api + "返回httpCode: " + response.getStatusLine().getStatusCode() + " " + respone);
			}
			LOGGER.logInfo("Order接口" + api + "返回: " + respone);
			return respone;
		} catch (IOException e) {
			throw new ApiServerErrorException("连接订单api服务器" + api + "失败: " + e.getMessage(), e);
		} finally {
			if (response != null) {
				response.close();
			}
			if (httpClient != null) {
				httpClient.close();
			}
		}
	}
}
