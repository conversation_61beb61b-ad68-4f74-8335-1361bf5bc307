package cn.loveapp.orders.monitor.dao.dream;

import cn.loveapp.orders.common.entity.UserOrderMigrateProgress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: orders-services-group
 * @description: UserOrderMigrateProgressDao
 * @author: Jason
 * @create: 2019-10-12 11:11
 **/
public interface UserOrderMigrateProgressDao {

	/**
	 * 新增数据
	 *
	 * @param userOrderMigrateProgress 实例对象
	 * @return 影响行数
	 */
	int migrateOrderInsert(UserOrderMigrateProgress userOrderMigrateProgress);

	/**
	 * 更新数据
	 * @param userOrderMigrateProgress
	 * @return
	 */
	int migrateOrderUpdate(UserOrderMigrateProgress userOrderMigrateProgress);

	/**
	 * 更新dbId
	 * @param userOrderMigrateProgress
	 * @return
	 */
	int updateToDbId(UserOrderMigrateProgress userOrderMigrateProgress);

	/**
	 * 更新进度
	 * @param userOrderMigrateProgress
	 * @return
	 */
	int updateProgress(UserOrderMigrateProgress userOrderMigrateProgress);

	UserOrderMigrateProgress queryBySellerId(String sellerId);

	List<UserOrderMigrateProgress> queryByGroupByDbId();

	Long queryBySumOrderTotal();

	List<UserOrderMigrateProgress> queryByDbId(@Param("fromDbId")Integer dbId);

	List<UserOrderMigrateProgress> queryAll(@Param("limit") long limit, @Param("offset") long offset);

	List<UserOrderMigrateProgress> queryAllNeedMigrate(@Param("toDbId") Integer toDbId, @Param("lastId") Integer lastId, @Param("limit") long limit);

}
