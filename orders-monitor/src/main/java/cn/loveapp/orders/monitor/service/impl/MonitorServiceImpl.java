package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.orders.common.api.request.MessageRequest;
import cn.loveapp.orders.common.api.response.MessageResponse;
import cn.loveapp.orders.common.bo.UserDbId;
import cn.loveapp.orders.common.config.rocketmq.RocketMQDefaultConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQMultiSoldGetConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQPDDSoldGetConfig;
import cn.loveapp.orders.common.config.rocketmq.RocketMQTaobaoSoldGetAppConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoAppConfig;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.common.dao.dream.UserTagDao;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.entity.UserTag;
import cn.loveapp.orders.common.platform.api.MessageApiPlatformHandleService;
import cn.loveapp.orders.common.proto.PullSoldGetApiOrdersRequest;
import cn.loveapp.orders.common.proto.PullSoldGetApiOrdersRequestProto;
import cn.loveapp.orders.common.proto.head.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.orders.common.service.UserProductionInfoExtService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.EmailHelper;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.common.utils.RocketMqQueueHelper;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dao.rds.MonitorJdpTbTradeDao;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.ClearCrossOrderRequest;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.service.MonitorService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.MailSendException;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MonitorServiceImpl
 *
 * <AUTHOR>
 * @date 2018-12-19
 */
@Service
public class MonitorServiceImpl implements MonitorService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorServiceImpl.class);
	private Logger phoneLogger = LoggerFactory.getLogger(MonitorServiceImpl.class);

	public static final String TRADE_ONS_STATUS_BUSY = "busy";
	public static final String TRADE_KEY = "aiyong";
	public static final String TRADE_ONS_STATUS_HKEY = "tradeONSStatus_";
	public static final String TRADE_SOLDGET_STATUS_HKEY = "tradeSoldGetStatus_";

	@Autowired
	private MonitorConfig monitorConfig;

	@Autowired
	private TaobaoAppConfig taobaoAppConfig;

	@Autowired
	private RocketMQTaobaoSoldGetAppConfig taobaoOnsSoldGetAppConfig;

	@Autowired
	private RocketMQPDDSoldGetConfig rocketMQPDDSoldGetConfig;

	@Autowired
	private RocketMQMultiSoldGetConfig rocketMQMultiSoldGetConfig;

	@Autowired
	@Qualifier("stringRedisTemplate")
	private StringRedisTemplate redisTemplate;

	@Autowired
	private MonitorJdpTbTradeDao monitorJdpTbTradeDao;

	@Autowired
	private UserProductionInfoExtService userProductionInfoExtService;

	@Autowired
	private UserService userService;

	@Autowired
	private MessageApiPlatformHandleService messageApiPlatformHandleService;

	@Autowired
	private UserTagDao userTagDao;

	@Autowired(required = false)
	private DefaultMQProducer ordersPullSoldGetONSProducer;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired(required = false)
	private EmailHelper emailHelper;

    @Autowired
    private MonitorAyTradeSearchESDao commonAyTradeSearchESDao;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private SubOrderRepository subOrderRepository;

    @Value("#{${orders.migrate.tao.appNameAndRdsName.map:{trade:''}}}")
    private Map<String, List<String>> taoAppNameAndRdsNameMap = Maps.newHashMap();

    @Value("#{${orders.migrate.pdd.appNameAndRdsName.map:{trade:''}}}")
    private Map<String, List<String>> pddAppNameAndRdsNameMap = Maps.newHashMap();

    @Value("#{${orders.migrate.doudian.appNameAndRdsName.map:{tradeERP:''}}}")
    private Map<String, List<String>> doudianAppNameAndRdsNameMap = Maps.newHashMap();


    @Value("${orders.migrate.pdd.guanDian.rds:}")
	private List<String> pddGuanDianRdsNames = Lists.newArrayList();

	@Value("${orders.migrate.pdd.trade.rds:}")
	private List<String> pddTradeRdsNames = Lists.newArrayList();

    /**
     * 预发用户 key为sellerId,value为sellerNick
     */
    @Value("#{${orders.monitor.pretest.users:{}}}")
    private Map<String, String> preTestUser;

    /**
     * 预发用户推送库
     */
    @Value("${orders.monitor.pretest.dbId:9}")
    private int preTestUserDbId;


    @Override
	public <T> void sendCheckResultNotify(String title, T result, String titleSuffix,String templateName) {
		try {
			if (!monitorConfig.getMailTo().isEmpty() && emailHelper != null) {
				String subject = "订单" + title + "校验报告 - " + titleSuffix;
				String from = monitorConfig.getMailFrom();
				String[] to = monitorConfig.getMailTo().toArray(new String[0]);

				try {
					emailHelper.sendEmailByTemplate(subject, from, to, templateName, result);
				} catch (MailSendException e) {
					LOGGER.logError(title + " 发送邮件失败, 稍后重试1次");
					Thread.sleep(5000);
					emailHelper.sendEmailByTemplate(subject, from, to, templateName, result);
				}
			} else {
				LOGGER.logError(title + " 没有收件人, 跳过邮件发送");
			}
		} catch (Throwable ex) {
			LOGGER.logError(title + " 发送邮件失败", ex);
		}
	}

	@Override
	public boolean isOrderServiceEnabled(int dbId, boolean onlySoldGet) throws DataAccessException {
		HashOperations<String, String, String> op = redisTemplate.opsForHash();
		String hashKey = (onlySoldGet ? TRADE_SOLDGET_STATUS_HKEY : TRADE_ONS_STATUS_HKEY) + dbId;
		String tradeStatus = op.get(TRADE_KEY, hashKey);
		if (TRADE_ONS_STATUS_BUSY.equals(tradeStatus)) {
			return false;
		}
		return true;
	}

	@Override
	public void setOrderServiceEnabled(Set<Integer> dbIds, boolean enable, String reason, boolean onlySoldGet) throws DataAccessException {
		if(dbIds == null || dbIds.isEmpty()){
			return;
		}
		List<String> result = Lists.newArrayList();

		String key = onlySoldGet ? TRADE_SOLDGET_STATUS_HKEY : TRADE_ONS_STATUS_HKEY;
		HashOperations<String, String, String> op = redisTemplate.opsForHash();
		String option = enable? "恢复" : "关闭";
		String service = (onlySoldGet? "soldGet":"全部");
		for(Integer dbId : dbIds){
			String hashKey = key + dbId;
			boolean hasEnabled = isOrderServiceEnabled(dbId, onlySoldGet);
			if(hasEnabled == enable){
				continue;
			}
			try {
				op.put(TRADE_KEY, hashKey, enable ? "" : TRADE_ONS_STATUS_BUSY);
				result.add("\r\ndbId " + dbId + " 的对外" + service + "服务已自动" + option);
			} catch (Exception e) {
				LOGGER.logError(e.getMessage(), e);
			}
		}
		if(!result.isEmpty()){
			// 发送邮件通知
			sendCheckResultNotify(CheckerType.ONS.name(), result, String.format("%s: 自动%s订单对外%s服务", reason, option, service),CheckerType.ONS.name());
			LOGGER.logInfo(String.join("", result));
		}
	}

	@Override
	public void setDbId(BaseOrderApiCheckResult result){
		List<OrderApiCheckFailed> faileds = result.getFaileds();
		if(faileds == null){
			return;
		}
		for(OrderApiCheckFailed failed : faileds){
			if(failed.getDbId() <= 0){
				UserDbId dbId = userProductionInfoExtService.getDbIdBySellerNick(failed.getSellerNick(), failed.getStoreId(), failed.getAppName());
				if(dbId != null){
					failed.setDbId(dbId.getDbId());
				}
			}
		}
		result.sortByDbId();
	}

	@Override
	public boolean sendPhoneMessage(@NotNull String message, @NotNull  String source){
		if(StringUtils.isAllEmpty(message, source)){
			return false;
		}
		MDC.put("source", source);
		MDC.put("notifications", "phone");
		try {
			phoneLogger.info(message);
		} catch (Exception e) {
			System.out.println("电话通知日志发送失败: " + e.getMessage());
			e.printStackTrace();
			return false;
		}finally {
			MDC.remove("source");
			MDC.remove("notifications");
		}
		return true;
	}

	@Override
	public String reopenPush(String sellerNick, String platformId, String appName, boolean sendSoldGet, boolean ignoreSameModified) {
        UserProductionInfoExt user = userProductionInfoExtService.queryBySellerNick(sellerNick, platformId, appName);
		if(user == null){
			LOGGER.logError(sellerNick, "", "用户不存在, 无法开通推送库");
			return "用户不存在, 无法开通推送库";
		}
		if(!UserProductionInfoExt.DB_DONE.toString().equals(user.getTopStatus())){
			LOGGER.logError(sellerNick, "", "top_status不是10, 无法开通推送库: " + user.getTopStatus());
			return "top_status不是10, 无法开通推送库: " + user.getTopStatus();
		}
		String sessionKey = userService.getAuthorization(sellerNick, user.getSellerId(), platformId, appName);
		if(StringUtils.isEmpty(sessionKey)){
			LOGGER.logError(sellerNick, "", "用户session失效, 无法开通推送库");
			return "用户session失效, 无法开通推送库";
		}
		int dbId;
        if (MapUtils.isNotEmpty(preTestUser) && preTestUser.containsValue(sellerNick)) {
            dbId = preTestUserDbId;
        } else {
            dbId = user.getDbId();
        }

		boolean needRds = true;
		List<String> rds = null;
		String topic = null;
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
            topic = taobaoOnsSoldGetAppConfig.getTopic();
            rds = taoAppNameAndRdsNameMap.get(appName);

            if (CollectionUtils.isEmpty(rds)) {
                LOGGER.logInfo(sellerNick, platformId, appName + " rds配置不存在, 不开通rds");
                needRds = false;
            }

            if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
                needRds = false;
            }
        } else if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId)) {
            topic = rocketMQPDDSoldGetConfig.getTopic();
            rds = pddAppNameAndRdsNameMap.get(appName);
            if (CollectionUtils.isEmpty(rds)) {
                LOGGER.logInfo(sellerNick, platformId, appName + " rds配置不存在, 不开通rds");
                needRds = false;
            }
        } else if (CommonPlatformConstants.PLATFORM_DOUDIAN.equals(platformId)) {
            topic = rocketMQMultiSoldGetConfig.getTopic();
            rds = doudianAppNameAndRdsNameMap.get(appName);
            if (CollectionUtils.isEmpty(rds)) {
                LOGGER.logInfo(sellerNick, platformId, appName + " rds配置不存在, 不开通rds");
                needRds = false;
            }
        } else {
            topic = rocketMQMultiSoldGetConfig.getTopic();
            needRds = false;
        }

		MessageRequest request = new MessageRequest();
		request.setSellerNick(sellerNick);
        request.setSellerId(user.getSellerId());
		request.setTopSession(sessionKey);
		if(needRds){
			if(rds == null ||  rds.size() <= dbId-1 || StringUtils.isEmpty(rds.get(dbId - 1))){
				LOGGER.logError(sellerNick, "", "没有对应的推送库, 无法开通推送库: " + dbId);
				return "没有对应的推送库, 无法开通推送库: " + dbId;
			}
			request.setRdsName(rds.get(dbId - 1));
			try {
				MessageResponse response = messageApiPlatformHandleService.subscribeUserDbService(request, platformId, appName);
				if (!response.getIsSuccess()) {
					LOGGER.logError(request.getSellerNick(), "-", "rds开通异常 " + JSON.toJSONString(response));
					return "rds开通异常";
				}
			} catch (Exception e){
				LOGGER.logError(request.getSellerNick(), "", "rds开通异常 " + e.getMessage(), e);
				return "rds开通异常";
			}
		}
		try {
			MessageResponse response = messageApiPlatformHandleService.subscribeUserMessageService(request, platformId, appName);
			if (!StringUtils.isEmpty(response.getErrorCode())){
				LOGGER.logError(request.getSellerNick(), "-", "tmc开通异常 " + JSON.toJSONString(response));
//				return "tmc开通异常";
			}
		} catch (Exception e) {
			LOGGER.logError(request.getSellerNick(), "", "tmc开通异常 " + e.getMessage(), e);
//			return "tmc开通异常";
		}

        addTagForNick(sellerNick, appName);
		if(sendSoldGet){
			ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
			comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick);
			comLoveRpcInnerprocessRequestHead.setSellerId(user.getSellerId());
			comLoveRpcInnerprocessRequestHead.setPlatformId(platformId);
			comLoveRpcInnerprocessRequestHead.setAppName(appName);
			PullSoldGetApiOrdersRequest pullSoldGetApiOrdersRequest = new PullSoldGetApiOrdersRequest();
			pullSoldGetApiOrdersRequest.setRefundStatus(TaobaoStatusConstant.getPlatformOrderListRefundStatusAll(platformId));
			pullSoldGetApiOrdersRequest.setTaoStatus(TaobaoStatusConstant.getPlatformOrderListTradeStatusAll(platformId));
			pullSoldGetApiOrdersRequest.setIgnoreSameModified(ignoreSameModified);
			PullSoldGetApiOrdersRequestProto pullSoldGetApiOrdersRequestProto = new PullSoldGetApiOrdersRequestProto();
			pullSoldGetApiOrdersRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
			pullSoldGetApiOrdersRequestProto.setPullHistoryApiOrdersRequest(pullSoldGetApiOrdersRequest);

			if(user.getTopTradeCount() != null && user.getTopTradeCount() >= 20000 && !topic.endsWith(RocketMQDefaultConfig.TOPIC_SUFFIX_BIG)){
				topic = topic + RocketMQDefaultConfig.TOPIC_SUFFIX_BIG;
			}
			rocketMqQueueHelper.push(topic, "*", pullSoldGetApiOrdersRequestProto, ordersPullSoldGetONSProducer);
		}

        String result = "开通推送库成功: " + dbId + " " + (CollectionUtils.isEmpty(rds) ? "" : rds.get(dbId - 1))
            + ", topTradeCount=" + user.getTopTradeCount();
        LOGGER.logInfo(sellerNick, "", result);
        return result;
	}

	/**
	 * 添加tos tag
	 * @param nick
	 */
	public void addTagForNick(String nick, String appName){
		UserTag userTag = new UserTag();
		userTag.setNick(nick);
		userTag.setApp(StringUtils.isEmpty(appName) ? CommonAppConstants.APP_TRADE : appName);
		userTag.setTag("tos");
		List<UserTag> userTagDaos = userTagDao.queryAll(userTag);
		userTag.setCreatedate(DateUtil.convertLocalDateTimetoDate(LocalDateTime.now()));
		userTag.setRemark("手动重新开通RDS推送");
		if (CollectionUtils.isEmpty(userTagDaos) || userTagDaos.stream().noneMatch(a->a.getTag().equalsIgnoreCase("tos"))) {
			LOGGER.logInfo(nick, "-", "添加tag:tos, 当前获取的数据库打标结果:" + userTagDaos);
			userTagDao.insert(userTag);
			// 刷新redis
			try {
				NetworkUtil
					.http(taobaoAppConfig.getRebuildUserUrl() + "?nick=" + URLEncoder.encode(nick, "utf-8"), null, false,
						"", "", false, false, "");
			} catch (Exception e) {
				LOGGER.logError(nick, "", "刷新tags失败: " + e.getMessage(), e);
			}
		}
	}


    @Override
    public Map<String, String> clearCrossOrder(ClearCrossOrderRequest request) {
	    if (MapUtils.isEmpty(preTestUser)) {
	        return Collections.singletonMap("-", "未配置预发用户");
        }
        String storeId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        Boolean isRePush = request.getIsRePush();

        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String> userEntry : preTestUser.entrySet()) {
            String sellerId = userEntry.getKey();
            String sellerNick = userEntry.getValue();
            if (StringUtils.isEmpty(sellerId) || StringUtils.isEmpty(sellerNick)) {
                result.put(sellerId + "-" + sellerNick, "串单用户配置信息有误!");
                continue;
            }
            this.deleteTradeAndRePush(result, sellerId, sellerNick, storeId, appName, isRePush);
        }
        return result;
    }

    private void deleteTradeAndRePush(Map<String, String> result, String sellerId, String sellerNick, String storeId, String appName, Boolean isRePush) {

        int esDeleteCount = 0;
        int mongoMainDeleteCount = 0;
        int mongoSubDeleteCount = 0;

        List<AyTradeSearchES> tradeSearchES = commonAyTradeSearchESDao
            .querySellerIdNotMatchSellerNickTradeList(sellerId, sellerNick, storeId);

        // 防止删除其他用户订单
        tradeSearchES = tradeSearchES.stream().
            filter(trade -> Objects.equals(sellerId, trade.getSellerId()) || Objects.equals(sellerNick, trade.getSellerNick()))
            .collect(Collectors.toList());

        List<String> tids = tradeSearchES.stream().map(AyTradeSearchES::getTid).collect(Collectors.toList());
        esDeleteCount += commonAyTradeSearchESDao.batchSoftDeleteById(tradeSearchES);
        mongoMainDeleteCount += orderRepository.batchRemoveByTids(sellerId, tids, storeId, null);
        mongoSubDeleteCount += subOrderRepository.batchRemoveByTids(sellerId, tids, storeId, null);
        LOGGER.logInfo(sellerNick, sellerId, "删除串单订单: " + JSON.toJSONString(tids));

        // 删除es合单主单正确，但合单子单被删完了的合单主单
        List<AyTradeSearchES> deleteMergeTradeList =
            tradeSearchES.stream().filter(trade -> StringUtils.isNotBlank(trade.getMergeTid()))
                .filter(trade -> commonAyTradeSearchESDao.countByMergeTid(trade.getMergeTid(), storeId) == 1)
                .map(AyTradeSearchES::getMergeTid)
                .collect(Collectors.toSet()).stream()
                .map(mergeTid -> AyTradeSearchES.of(sellerId, null, storeId, null, mergeTid))
                .collect(Collectors.toList());
        int esMergeMainCount = commonAyTradeSearchESDao.batchSoftDeleteById(deleteMergeTradeList);
        LOGGER.logInfo(sellerNick, sellerId, "删除ES合单主单: " +
            JSON.toJSONString(deleteMergeTradeList.stream().map(AyTradeSearchES::getTid).collect(Collectors.toList())));

        String pushResult = BooleanUtils.isTrue(isRePush)
            ? this.reopenPush(sellerNick, storeId, appName, true, false) : "不重新拉单";
        String deleteResult = "删除订单数据 => " + "es数量:" + esDeleteCount + ",mongo主单数量:" + mongoMainDeleteCount
            + ",mongo子单数量:" + mongoSubDeleteCount + ",删除串单合单主单: " + esMergeMainCount;
        LOGGER.logInfo(sellerNick, sellerId, deleteResult + "," + pushResult);
        result.put(sellerId + "-" + sellerNick, deleteResult + "," + pushResult);
    }
}
