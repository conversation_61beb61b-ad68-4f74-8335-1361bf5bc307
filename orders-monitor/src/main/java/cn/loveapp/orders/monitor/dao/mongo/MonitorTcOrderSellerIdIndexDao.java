package cn.loveapp.orders.monitor.dao.mongo;

import cn.loveapp.orders.common.constant.MongoConstant;
import cn.loveapp.orders.common.dao.mongo.impl.BaseMongoDao;
import cn.loveapp.orders.common.entity.mongo.TcOrderSellerIdIndex;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunh<PERSON>
 * @create: 2022/7/26 3:59 PM
 **/
@Repository
public class MonitorTcOrderSellerIdIndexDao extends BaseMongoDao {

    public MonitorTcOrderSellerIdIndexDao(MongoTemplate mongoTemplate) {
        super(mongoTemplate);
    }

    @Override
    protected boolean isShardCollection() {
        return true;
    }

    @Override
    protected String getShardPrimaryKey() {
        return MongoConstant.SELLER_ID_FIELD;
    }

    @Override
    protected String getCollectionName() {
        return mongoTemplate.getCollectionName(TcOrderSellerIdIndex.class);
    }

    public int updateBuyerOpenUidById(List<TcOrderSellerIdIndex> tcOrderSellerIdIndices) {
        if (CollectionUtils.isEmpty(tcOrderSellerIdIndices)){
            return 0;
        }
        BulkOperations bulkOperations = getBulkOps(BulkOperations.BulkMode.UNORDERED);
        for (TcOrderSellerIdIndex tcOrderSellerIdIndex : tcOrderSellerIdIndices){
            Update update = new Update();
            update.set(MongoConstant.BUYER_OPEN_UID_FIELD, tcOrderSellerIdIndex.getBuyerOpenUid());
            bulkOperations.upsert(buildQueryById(tcOrderSellerIdIndex.getId(), tcOrderSellerIdIndex.getSellerId()), update);
        }
        return bulkOperationsExecute(bulkOperations);
    }
}
