package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.common.api.request.FullInfoRequest;
import cn.loveapp.orders.common.api.response.FullinfoResponse;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.FullinfoCheckResult;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.OrdersApiService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.api.ApiException;
import com.taobao.api.internal.util.TaobaoUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 多平台fullInfo正确率校验
 *
 * <AUTHOR>
 * @date 2021/4/16
 */
@Service
public class MultiFullInfoMonitorChecker extends AbstractOrderApiMonitorChecker<AyTradeSearchES> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MultiFullInfoMonitorChecker.class);
	public static final String JSON_ROOT = "trade";

	@Autowired
	private TradeApiPlatformHandleService tradeApiPlatformService;

	@Autowired
	private OrdersApiService ordersApiService;

	@Autowired
	private UserService userService;

	@Autowired
	private MonitorAyTradeSearchESDao ayTradeSearchDao;

	protected FullinfoCheckResult fullinfoCheckResult;

	protected volatile String currentStoreId;

	@Override
	public CheckerType type() {
		return CheckerType.MULTI_FULLINFO;
	}

	@Override
	public String name() {
		return "multi fullInfo";
	}

	@Override
	protected int getSampleTableCount() {
		return 1;
	}

	@Override
	protected String getResultJsonRoot() {
		return JSON_ROOT;
	}

	protected String getCurrentStoreId() {
		return currentStoreId;
	}

	protected void setCurrentStoreId(String storeId) {
		this.currentStoreId = storeId;
	}

	@Override
	protected BaseOrderApiCheckResult getOrderApiCheckResult() {
		return fullinfoCheckResult;
	}

	@Override
	protected Set<String> checkAsJsonStringKeys(){
		return Sets.newHashSet();
	}

	@Override
	public void check() {
		for(String platformId : monitorConfig.getMultiFullInfoPlatforms()){
			setCurrentStoreId(platformId);
			super.check();
		}
	}

	@Override
	protected synchronized String innerCheck() {
		double accuracyRate;
		fullinfoCheckResult = new FullinfoCheckResult();
		fullinfoCheckResult.setOrderStatus(monitorConfig.getMultiFullInfoStatus());

		accuracyRate = checkSampleInTimeRange(monitorConfig.getMultiFullInfoTimeRange(), fullinfoCheckResult.getOrderStatus(),
			Integer.parseInt(monitorConfig.getMultiFullInfoNumbers().get(getCurrentStoreId())));

		MonitoringSummaryData.getInstance().setFullInfoAccuracyRate(accuracyRate);

		if (!isStoped) {
			postCheck();
		}

		return String.valueOf(accuracyRate);
	}

	@Override
	protected String requestOrderServiceApi(AyTradeSearchES sample, LocalDateTime startTime,
		LocalDateTime endTime) throws ApiServerErrorException, IOException {

		String response =  ordersApiService.detailInfoGet(sample, monitorConfig.getMultiFullInfoFields());
		JSONObject responseJson = JSON.parseObject(response);
		if(String.valueOf(CommonApiStatus.ServerError.code()).equals(responseJson.getString("code"))){
			return response;
		}
		JSONObject detailInfoResponse = (JSONObject) JSONPath.eval(JSON.parseObject(response), "$.body.detailInfoResponse");
		if (detailInfoResponse == null) {
			throw new ApiServerErrorException("访问订单服务失败: " + response);
		} else {
			JSONObject trade = detailInfoResponse.getJSONObject("trade");
			if (trade != null) {
				JSONObject orders = trade.getJSONObject("orders");
                JSONObject orderInfoExt = trade.getJSONObject("order_info_ext");
				if (orders != null) {
					JSONArray ordersArray;
					ordersArray = orders.getJSONArray("order");
					if (ordersArray != null) {
						ordersArray = ordersArray.stream()
							.sorted(Comparator.comparing(iter -> ((JSONObject) iter).getString("oid")))
							.collect(Collectors.toCollection(JSONArray::new));
					}
					orders.remove("order");
					orders.put("order", ordersArray);
				}
                if (orderInfoExt != null) {
                    JSONArray subItems = orderInfoExt.getJSONArray("sub_items");
                    if (subItems != null) {
                        subItems = subItems.stream()
                            .sorted(Comparator.comparing(iter -> ((JSONObject)iter).getString("order_id")))
                            .collect(Collectors.toCollection(JSONArray::new));
                    }
                    orderInfoExt.remove("sub_items");
                    orderInfoExt.put("sub_items", subItems);
                }
            }
		}
//		long totalResults = tradeListResponse.getLongValue("total_results");
//		JSONObject root = new JSONObject();
//		JSONObject body = new JSONObject();
//		body.put("totalResults", totalResults);
//		if(totalResults > 0){
//			JSONArray trades = new JSONArray();
//			for (Object ob : tradeListResponse.getJSONObject("trades").getJSONArray("trade")) {
//				trades.add(((JSONObject)ob).get("tid"));
//			}
//			trades.sort(Comparator.comparing(Object::toString));
//			body.put("trades", trades);
//		}
//		root.put("body", body);
		return detailInfoResponse.toJSONString();
	}

	@Override
	protected String requestPlatformApi(AyTradeSearchES sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException {
		String platformId = getCurrentStoreId();
		String appName = sample.getAppName();
		String sellerNick = sample.getSellerNick();
		String topSession = userService.getAuthorization(sellerNick, sample.getSellerId(), platformId, appName);
		if(StringUtils.isEmpty(topSession)){
			throw new UserNeedAuthException(500, "获取topSession失败");
		}
		FullInfoRequest request = new FullInfoRequest();
		request.setTid(sample.getTid());
		request.setApiFields(monitorConfig.getMultiFullInfoPlatformFields().get(platformId));
		request.setTopSession(topSession);
		FullinfoResponse response = tradeApiPlatformService.fullInfo(request, platformId, appName);
		if(!response.isSuccess()){
			throw new ApiException(response.getErrorCode(), response.getMsg(), response.getSubCode(), response.getSubMsg());
		}
		String json = TaobaoUtils.objectToJson(response);
		// 与service返回订单信息对齐数据
		JSONObject root = JSON.parseObject(json);
		root.remove("success");
		JSONObject trade = root.getJSONObject("trade");
		if (trade != null) {
			// 对齐orders
			JSONArray ordersArray = null;
			ordersArray = trade.getJSONArray("orders");

			if (ordersArray != null) {
				ordersArray = ordersArray.stream()
					.sorted(Comparator.comparing(iter -> ((JSONObject) iter).getString("oid")))
					.collect(Collectors.toCollection(JSONArray::new));

				trade.remove("orders");
				JSONObject orders = new JSONObject();
				orders.put("order", ordersArray);
				trade.put("orders", orders);
			}
			// 对齐promotion_details
			JSONArray promotionsArray = trade.getJSONArray("promotion_details");
			if(promotionsArray != null){
				trade.remove("promotion_details");
				JSONObject orders = new JSONObject();
				orders.put("promotion_detail", promotionsArray);
				trade.put("promotion_details", orders);
			}
			// 对齐ext
			for (String key : trade.keySet()) {
				if(key.endsWith("_trade_ext")){
					JSONObject ext = trade.getJSONObject(key);
					trade.remove(key);
                    JSONArray subItems = ext.getJSONArray("sub_items");
                    if (subItems != null) {
                        subItems = subItems.stream()
                            .sorted(Comparator.comparing(iter -> ((JSONObject)iter).getString("order_id")))
                            .collect(Collectors.toCollection(JSONArray::new));
                    }
                    ext.put("sub_items", subItems);
                    trade.put("order_info_ext", ext);
                    break;
				}
			}
		}

		return root.toJSONString();
	}

	@Override
	protected boolean needCompareJson(AyTradeSearchES sample, OrderApiCheckFailed failedOrder,
		LocalDateTime taobaoApiTime, JSONObject apiJson, JSONObject taobaoJson, LocalDateTime startTime,
		LocalDateTime endTime) {
//		Date taoModified = getModified(taobaoJson);
//		Date apiModified = getModified(apiJson);
//		if (taoModified != null && apiModified != null) {
//			LocalDateTime taoDateTime = LocalDateTime.ofInstant(taoModified.toInstant(), ZoneId.systemDefault());
//			long time = taoModified.getTime() - apiModified.getTime();
//			if (time > 0 && taoDateTime.isAfter(taobaoApiTime)) {
//				//当前分钟内的订单修改数据可能尚未接收到, 忽略
//				info(sample, "TaoApi返回的modified在当前分钟内, 默认校验成功, modified:" + taoDateTime);
//				return false;
//			}
//		}
		return true;
	}

//	/**
//	 * 获取订单的modified字段
//	 *
//	 * @param taobaoOrder
//	 * @return
//	 */
//	private Date getModified(JSONObject taobaoOrder) {
//		return TypeUtils.castToDate(JSONPath.eval(taobaoOrder, "$.trade_fullinfo_get_response.trade.modified"));
//	}

	protected void postCheck() {
		fullinfoCheckResult.setAccuracyRate(MonitoringSummaryData.getInstance().getFullInfoAccuracyRate());
		fullinfoCheckResult.getOrderMissCheckResult().increment(validNumber.longValue());

		//设置dbId
		monitorService.setDbId(fullinfoCheckResult);

		String titleSuffix;
		if(fullinfoCheckResult.getOrderMissCheckResult().getTotalCount().longValue() <= 0){
			titleSuffix = "缺少样本";
		}else{
			titleSuffix = new DecimalFormat("#.####%").format(fullinfoCheckResult.getAccuracyRate());
		}
		//发送校验结果通知
		monitorService.sendCheckResultNotify(getCurrentStoreId() + "_FULLINFO", fullinfoCheckResult,
			titleSuffix, "fullInfo");
	}


	/**
	 * 查询指定时间内的订单
	 *
	 * @param startTime 查询起始时间
	 * @param endTime   查询结束时间
	 * @param status    无用
	 * @param listId
	 * @return
	 */
	@Override
	public List<AyTradeSearchES> querySamples(LocalDateTime startTime, LocalDateTime endTime, String status,
		long listId) {
		long fullInfoNumber = Integer.parseInt(monitorConfig.getMultiFullInfoNumbers().get(getCurrentStoreId()));
		int page = 0;
		int limit = monitorConfig.getQueryLimit();
		List<AyTradeSearchES> result = Lists.newArrayList();
		while(true){
			List<AyTradeSearchES> pageResult;
			try {
				pageResult = ayTradeSearchDao
					.queryOrdersByTime(startTime, endTime, status, getCurrentStoreId(), page, limit);
			} finally {
				page++;
			}
			result.addAll(pageResult);
			if(pageResult.size() < limit || result.size() >= fullInfoNumber){
				break;
			}
		}
		LOGGER.logInfo(name() + " 样本数:" + result.size());
		return result;
	}

	@Override
	protected Set<String> getNeedIgnoreKeys(){
		return monitorConfig.getMultiFullInfoIgnores().getOrDefault(getCurrentStoreId(), SetUtils.EMPTY_SET);
	}

	@Override
	protected String getStoreId() {
		return currentStoreId;
	}

	@Override
	protected void error(AyTradeSearchES sample, String message, Throwable e) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message, e);
	}

	@Override
	protected void error(AyTradeSearchES sample, String message) {
		LOGGER.logError(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void info(AyTradeSearchES sample, String message) {
		LOGGER.logInfo(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}

	@Override
	protected void warn(AyTradeSearchES sample, String message) {
		LOGGER.logWarn(sample.getSellerNick(), sample.getSellerId(), name() + ", " + message);
	}
}
