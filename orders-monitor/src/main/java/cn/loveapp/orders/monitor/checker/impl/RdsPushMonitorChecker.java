package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.JdpTbTrade;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dao.rds.MonitorJdpTbTradeDao;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * rds延时校验
 *
 * <AUTHOR>
 * @date 2019-01-03
 */
@Service
public class RdsPushMonitorChecker extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RdsPushMonitorChecker.class);

	@Autowired
	private MonitorJdpTbTradeDao jdpTbTradeDao;

	@Override
	protected String name() {
		return "rdsPush";
	}

	@Override
	protected String innerCheck() {
		double accuracyRate = 1.0;
		double median = -1.0;
		long count = 0;
		try {
			LocalDateTime endTime = LocalDateTime.now();
			LocalDateTime startTime = endTime.minusSeconds(monitorConfig.getRdsTimeRange().getSeconds());

			double errorCount = 0;
			int pageSize = 5000;

			List<Long> times = Lists.newArrayList();
			for (int i = 0; i < Integer.MAX_VALUE; i++) {
				PageRequest pageRequest = PageRequest.of(i, pageSize);
				if(isStoped){
					return null;
				}
				List<JdpTbTrade> tbTrades = jdpTbTradeDao.queryByJdpModified(startTime, endTime, pageRequest);
				if (tbTrades.isEmpty()) {
					if (i == 0) {
						LOGGER.logWarn(name() + " " + startTime + " : " + endTime + " 之间没有RDS订单推送数据");
					}
					break;
				}
				count += tbTrades.size();

				LOGGER.logDebug(i + " 页获取到 " + tbTrades.size() + " 单数据");
				for (JdpTbTrade tbTrade : tbTrades) {
					Duration duration = java.time.Duration.between(tbTrade.getModified(), tbTrade.getJdpModified());
					long time = duration.getSeconds();
					times.add(time);
					if (time > monitorConfig.getRdsDelayTime().getSeconds()) {
						LOGGER.logError(tbTrade.getSellerNick(), String.valueOf(tbTrade.getTid()),
							name() + " 推送超时,  JdpModified:" + tbTrade.getJdpModified() + " Modified:" + tbTrade
								.getModified() + " 超时:" + duration.getSeconds() + "秒");
						errorCount++;
					}
				}
				if (count < pageSize || count >= monitorConfig.getRdsDelayNumber()) {
					break;
				}
			}

			if(count > 0){
				times.sort(Long::compare);
				median = times.get(times.size()/2);
			}else if(count == 0){
				median = 600;
			}
			LOGGER.logInfo("rds 在 " + startTime + " : " + endTime + " 之间推送了 " + count + " 单数据, 延时中位数=" + median);

			accuracyRate = count <= 0 ? 1.0 : (count - errorCount) / count * 1.0;

			setValidNumber(count);

		} catch (Exception e) {
			LOGGER.logError(name() + " 监控失败", e);
		} finally {
			MonitoringSummaryData.getInstance().setRdsDelayAccuracyRate(accuracyRate);
			MonitoringSummaryData.getInstance().setRdsDelayMedianTime(median);
		}

		return String.valueOf(accuracyRate);
	}

	@Override
	public CheckerType type() {
		return CheckerType.RDS_PUSH;
	}
}
