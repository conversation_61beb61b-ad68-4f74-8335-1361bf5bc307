package cn.loveapp.orders.monitor.controller.operations;

import java.util.ArrayList;
import java.util.List;

import cn.loveapp.orders.common.api.entity.AyRefund;
import cn.loveapp.orders.common.exception.UnNeedRetryException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.common.api.request.AyRefundGetRequest;
import cn.loveapp.orders.common.api.request.FullInfoRequest;
import cn.loveapp.orders.common.api.response.AyRefundGetResponse;
import cn.loveapp.orders.common.api.response.FullinfoResponse;
import cn.loveapp.orders.common.config.taobao.TaobaoFullInfoAppConfig;
import cn.loveapp.orders.common.config.taobao.TaobaoRefundGetConfig;
import cn.loveapp.orders.common.dao.mongo.OrderRefundRepository;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dao.mongo.SubOrderRepository;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.entity.mongo.TcOrderRefund;
import cn.loveapp.orders.common.entity.mongo.TcSubOrder;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.monitor.constant.operations.ResponseConstant;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;
import cn.loveapp.orders.monitor.utils.MonitorPlatformRequestParamProcessing;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 17:27
 * @Description: 订单信息控制器
 */
@RestController
@RequestMapping(value = "monitor/operations/orderinfo")
public class OrderInfoController {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderInfoController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private MonitorAyTradeSearchESDao commonAyTradeSearchESDao;

    @Autowired
    private SubOrderRepository subOrderRepository;

    @Autowired
    private OrderRefundRepository orderRefundRepository;

    @Autowired
    private TaobaoFullInfoAppConfig taobaoFullInfoAppConfig;

    @Autowired
    private TaobaoRefundGetConfig taobaoRefundGetConfig;

    @Autowired
    private TradeApiPlatformHandleService tradeApiPlatformHandleService;

    @Autowired
    private MonitorPlatformRequestParamProcessing monitorPlatformRequestParamProcessing;

    @RequestMapping(value = "/esorderinfo.list.get")
    public CommonApiResponse<List<AyTradeSearchES>> getEsOrderInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        // 检查参数
        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }

        AyTradeSearchES ayTradeSearchEs = AyTradeSearchES.of(request.getSellerId(), null, request.getStoreId(),
            request.getAppName(), request.getTid());
        ayTradeSearchEs = commonAyTradeSearchESDao.getById(ayTradeSearchEs);

        ArrayList<AyTradeSearchES> ayTradeSearchEsResponse = new ArrayList<>();
        ayTradeSearchEsResponse.add(ayTradeSearchEs);
        return CommonApiResponse.success(ayTradeSearchEsResponse);
    }

    @RequestMapping(value = "/mongoomainoderinfo.list.get")
    public CommonApiResponse<List<TcOrder>> getMongoMainOrderInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }
        TcOrder ayTradeMain = orderRepository.queryByTid(request.getTid(), request.getStoreId(), request.getSellerId(),
            request.getAppName());
        ArrayList<TcOrder> tcOrders = new ArrayList<>();
        tcOrders.add(ayTradeMain);
        return CommonApiResponse.success(tcOrders);
    }

    @RequestMapping(value = "/mongosuborderinfo.list.get")
    public CommonApiResponse<List<TcSubOrder>> getMongoTcSubOrderInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }

        List<TcSubOrder> tcSubOrders = subOrderRepository.findAySubOrderByTidAndStoreIdAndSellerId(
            Lists.newArrayList(request.getTid()), request.getStoreId(), request.getSellerId(), request.getAppName());
        return CommonApiResponse.success(tcSubOrders);
    }

    @RequestMapping(value = "refundorderinfo.list.get")
    public CommonApiResponse<List<TcOrderRefund>> getRefundOrderInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }
        List<TcOrderRefund> tcOrderRefunds = orderRefundRepository.queryByTid(request.getTid(), request.getSellerId(),
            request.getStoreId(), request.getAppName());
        return CommonApiResponse.success(tcOrderRefunds);
    };

    @RequestMapping(value = "/orderfullinfo.list.get")
    public CommonApiResponse<List<Order>> getOrderFullInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        String tid = request.getTid();

        String topSession = userService.getAuthorization(sellerNick, request.getSellerId(), storeId, appName);

        if (StringUtils.isEmpty(topSession)) {
            return CommonApiResponse.failed(ResponseConstant.USER_SESSION_ISNUll.getCode(),
                ResponseConstant.USER_SESSION_ISNUll.getMessage());
        }

        FullInfoRequest fullInfoRequest = new FullInfoRequest();
        fullInfoRequest.setTid(tid);
        fullInfoRequest.setSellerNick(sellerNick);
        fullInfoRequest.setTopSession(topSession);

        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            fullInfoRequest.setApiFields(taobaoFullInfoAppConfig.getFileds());
        }
        List<Order> orders = null;
        FullinfoResponse fullInfoResponse = null;
        try {
            fullInfoResponse = tradeApiPlatformHandleService.fullInfo(fullInfoRequest, storeId, appName);
        } catch (Exception e) {
            LOGGER.logWarn(sellerNick, tid, "调取平台fullinfo异常" + e.getMessage());
            return CommonApiResponse.failed(ResponseConstant.INVOK_PLATFROM_API_ERROR.getCode(),
                ResponseConstant.INVOK_PLATFROM_API_ERROR.getMessage());
        }
        if (fullInfoResponse != null) {
            if (fullInfoResponse.isSuccess()) {
                if (fullInfoResponse.getTrade() != null) {
                    orders = fullInfoResponse.getTrade().getOrders();
                }
            }
        }
        return CommonApiResponse.success(orders);
    }

    @RequestMapping(value = "/refundorderapiinfo.list.get")
    public CommonApiResponse<List<AyRefund>> getRefundOrderApiInfo(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }
        String sellerNick = request.getSellerNick();
        String sellerId = request.getSellerId();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        String tid = request.getTid();

        List<TcOrderRefund> tcOrderRefunds = orderRefundRepository.queryByTid(tid, sellerId, storeId, appName);
        List<AyRefund> refundArrayList = new ArrayList<>();
        if (tcOrderRefunds.size() > 0) {
            // 查询API退款信息
            for (TcOrderRefund tcOrderRefund : tcOrderRefunds) {
                AyRefundGetRequest refundGetRequest = new AyRefundGetRequest();
                refundGetRequest.setRefundId(tcOrderRefund.getRefundId());
                refundGetRequest.setFields(taobaoRefundGetConfig.getFields());
                refundGetRequest.setSellerId(tcOrderRefund.getSellerId());
                AyRefundGetResponse ayRefundGetResponse = null;
                try {
                    ayRefundGetResponse =
                        tradeApiPlatformHandleService.refundGet(refundGetRequest, sellerNick, storeId, appName);
                    List<AyRefund> refund = ayRefundGetResponse.getRefund();
                    refundArrayList.addAll(refund);
                } catch (UserNeedAuthException e) {
                    LOGGER.logError(sellerNick, tid, "调用refundAPI失败" + e.getMessage());
                    return CommonApiResponse.failed(ResponseConstant.INVOK_PLATFROM_API_ERROR.getCode(),
                        ResponseConstant.INVOK_PLATFROM_API_ERROR.getMessage());
                } catch (UnNeedRetryException e) {
                    LOGGER.logError(sellerNick, tid, "调用refundAPI参数校验失败" + e.getMessage());
                    return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                        ResponseConstant.PARAMSE_RROR.getMessage());
                }
            }
        }
        return CommonApiResponse.success(refundArrayList);
    }
}
