package cn.loveapp.orders.monitor.controller.operations;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.monitor.annotation.MonitorOperationsAuth;
import cn.loveapp.orders.monitor.constant.operations.DegradedState;
import cn.loveapp.orders.monitor.dto.operations.DegradationAddRequest;
import cn.loveapp.orders.monitor.dto.operations.DegradationListResponse;
import cn.loveapp.orders.monitor.dto.operations.degradation.*;
import cn.loveapp.orders.monitor.entity.operations.DegradationTask;
import cn.loveapp.orders.monitor.entity.operations.DegradationTaskLog;
import cn.loveapp.orders.monitor.service.operations.DegradationExecuteService;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskLogService;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskSchedulerService;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskService;
import jodd.util.StringUtil;

/**
 * <AUTHOR>
 * @date 2022-03-31 14:24
 * @Description: 运维平台降级操作控制器
 */
@Validated
@RestController
@RequestMapping("monitor/operations/degradation")
public class DegradationTaskController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DegradationTaskController.class);

    public static final String OPEN_TASK_SUFFIX = "-on";
    public static final String CLOSE_TASK_SUFFIX = "-off";

    @Autowired
    private DegradationTaskService degradationTaskService;
    @Autowired
    private DegradationTaskLogService degradationTaskLogService;
    @Autowired
    private DegradationExecuteService degradationExecuteService;
    @Autowired
    private DegradationTaskSchedulerService degradationTaskSchedulerService;

    /**
     * 删除任务
     *
     * @param degradationCommonRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping("/degradation.task.delete")
    public CommonApiResponse<String> deleteTask(@RequestBody DegradationCommonRequest degradationCommonRequest) {
        if (degradationTaskService.removeDegradationTask(degradationCommonRequest.getTaskName()) > 0) {
            return CommonApiResponse.success("删除成功");
        } else {
            return CommonApiResponse.of(CommonApiStatus.ServerError.code(), "删除失败");
        }
    }

    /**
     * 添加任务
     *
     * @param degradationAddRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.task.add", method = RequestMethod.POST)
    public CommonApiResponse<String> addTask(@RequestBody DegradationAddRequest degradationAddRequest) {
        DegradationTask.DegradationTaskParameter degradationTaskParameter =
            DegradationTask.DegradationTaskParameter.of(degradationAddRequest);
        String degradationConfigConfigInfo = JSON.toJSONString(degradationTaskParameter);

        if (degradationTaskService.getDegradationTask(degradationAddRequest.getTaskName()) == null) {
            DegradationTask degradationTask = new DegradationTask();
            degradationTask.setName(degradationAddRequest.getTaskName());
            degradationTask.setDescription(degradationAddRequest.getDescription());
            degradationTask.setSwitchStatus(DegradedState.DEGRADATION_SWITCH_OFF);
            degradationTask.setTimerStatus(DegradedState.TIMER_STATUS_NULL);
            degradationTask.setConfigurationParameter(degradationConfigConfigInfo);
            degradationTaskService.setDegradationTask(degradationTask);
            return CommonApiResponse.success("添加成功");
        } else {
            return CommonApiResponse.of(CommonApiStatus.Failed.code(), "任务已存在");
        }
    }

    /**
     * 获取任务列表
     *
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.list.get", method = RequestMethod.POST)
    @ResponseBody
    public CommonApiResponse<DegradationListResponse>
        taskList(@RequestBody DegradationListRequest degradationListRequest) {
        Pageable pageRequest =
            PageRequest.of(degradationListRequest.getStartIndex(), degradationListRequest.getPageSize());
        return CommonApiResponse
            .success(degradationTaskService.pageDegradationTask(degradationListRequest.getTaskName(), pageRequest));
    }

    /**
     * 获取任务日志
     *
     * @param degradationLogListRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.log.get", method = RequestMethod.POST)
    public CommonApiResponse<DegradationLogListResponse>
        taskLogList(@RequestBody DegradationLogListRequest degradationLogListRequest) {
        Pageable pageRequest =
            PageRequest.of(degradationLogListRequest.getStartIndex(), degradationLogListRequest.getPageSize());
        return CommonApiResponse.success(
            degradationTaskLogService.pageDegradationTaskLog(degradationLogListRequest.getTaskName(), pageRequest));
    }

    /**
     * 设置任务自动降级和恢复的时间
     *
     * @param degradationTimerRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.task.timer", method = RequestMethod.POST)
    public CommonApiResponse<String> settingDegradeTask(@RequestBody DegradationTimerRequest degradationTimerRequest) {

        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DegradationTask degradationTaskInfo =
            degradationTaskService.getDegradationTask(degradationTimerRequest.getTaskName());

        DegradationTask degradationTask = new DegradationTask();
        DegradationTask.DegradationTaskParameter configParameter =
            degradationTask.getDDegradationTaskConfig(degradationTaskInfo.getConfigurationParameter());

        if (DegradedState.CONFIGURATION_INTERFACE_MYSQL.equals(configParameter.getConfigurationInterface())) {
            return CommonApiResponse.of(CommonApiStatus.ServerError.code(), "mysql无法开启定时任务，请手动开启");
        } else {
            if (StringUtil.isNotEmpty(degradationTimerRequest.getStartTime())
                && StringUtil.isNotEmpty(degradationTimerRequest.getEndTime())) {
                if (Timestamp.valueOf(degradationTimerRequest.getStartTime()).getTime() > Timestamp
                    .valueOf(LocalDateTime.now()).getTime()) {
                    degradationTaskSchedulerService.addTask(degradationTaskInfo,
                        degradationTaskInfo.getName() + OPEN_TASK_SUFFIX,
                        LocalDateTime.parse(degradationTimerRequest.getStartTime(), dateFormat));
                    degradationTaskInfo
                        .setStartTime(LocalDateTime.parse(degradationTimerRequest.getStartTime(), dateFormat));
                } else {
                    LOGGER.logError("连接时间小于当前时间");
                    return CommonApiResponse.of(CommonApiStatus.Failed.code(), "定时任务开始时间小于当前时间");
                }

                if (Timestamp.valueOf(degradationTimerRequest.getEndTime()).getTime() > Timestamp
                    .valueOf(degradationTimerRequest.getStartTime()).getTime()) {
                    degradationTaskSchedulerService.addTask(degradationTaskInfo,
                        degradationTaskInfo.getName() + CLOSE_TASK_SUFFIX,
                        LocalDateTime.parse(degradationTimerRequest.getEndTime(), dateFormat));
                    degradationTaskInfo
                        .setEndTime(LocalDateTime.parse(degradationTimerRequest.getEndTime(), dateFormat));
                } else {
                    LOGGER.logError("结束任务时间小于开始任务时间");
                    return CommonApiResponse.of(CommonApiStatus.Failed.code(), "定时任务结束时间大于任务开始时间");
                }
            } else {
                if (StringUtil.isNotEmpty(degradationTimerRequest.getStartTime())) {
                    if (DegradedState.DEGRADATION_SWITCH_OFF.equals(degradationTaskInfo.getSwitchStatus())) {
                        degradationTaskSchedulerService.addTask(degradationTaskInfo,
                            degradationTaskInfo.getName() + OPEN_TASK_SUFFIX,
                            LocalDateTime.parse(degradationTimerRequest.getStartTime(), dateFormat));
                        degradationTaskInfo
                            .setStartTime(LocalDateTime.parse(degradationTimerRequest.getStartTime(), dateFormat));
                    } else {
                        return CommonApiResponse.of(CommonApiStatus.Failed.code(), "任务已经开启，无需定时开始");
                    }
                }
                if (StringUtil.isNotEmpty(degradationTimerRequest.getEndTime())) {
                    if (DegradedState.DEGRADATION_SWITCH_ON.equals(degradationTaskInfo.getSwitchStatus())) {
                        degradationTaskSchedulerService.addTask(degradationTaskInfo,
                            degradationTaskInfo.getName() + CLOSE_TASK_SUFFIX,
                            LocalDateTime.parse(degradationTimerRequest.getEndTime(), dateFormat));
                        degradationTaskInfo
                            .setStartTime(LocalDateTime.parse(degradationTimerRequest.getEndTime(), dateFormat));
                    } else {
                        return CommonApiResponse.of(CommonApiStatus.Failed.code(), "任务已经关闭，无需定时关闭");
                    }
                }
            }
        }
        degradationTaskService.updateDegradationTask(degradationTaskInfo);
        return CommonApiResponse.success("定时任务开启");
    }

    /**
     * 取消降级任务
     *
     * @param degradationTimerRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.task.canceltimer", method = RequestMethod.POST)
    public CommonApiResponse<String> cancelDegradeTask(@RequestBody DegradationTimerRequest degradationTimerRequest) {

        List<String> taskList = degradationTaskSchedulerService.getTaskList();
        String taskName = degradationTimerRequest.getTaskName();
        if (taskList.contains(taskName + OPEN_TASK_SUFFIX) || taskList.contains(taskName + CLOSE_TASK_SUFFIX)) {
            DegradationTaskLog degradationTaskLog = new DegradationTaskLog();
            degradationTaskLog.setTaskName(taskName);
            if (taskList.contains(taskName + OPEN_TASK_SUFFIX)) {
                degradationTaskSchedulerService.stopTask(taskName + OPEN_TASK_SUFFIX);
                degradationTaskLog.setContent("动态定时任务 [" + taskName + OPEN_TASK_SUFFIX + "] ,取消定时任务成功");
                degradationTaskLogService.addDegradationTaskLog(degradationTaskLog);
            }
            if (taskList.contains(taskName + CLOSE_TASK_SUFFIX)) {
                degradationTaskSchedulerService.stopTask(taskName + CLOSE_TASK_SUFFIX);
                degradationTaskLog.setContent("动态定时任务 [" + taskName + CLOSE_TASK_SUFFIX + "] ,取消定时任务成功");
                degradationTaskLogService.addDegradationTaskLog(degradationTaskLog);
            }
            // 取消任务的同时将保存的定时时间设为当前时间
            degradationTaskService.updateDegradationTimer(taskName, LocalDateTime.now(), LocalDateTime.now());

            return CommonApiResponse.success("取消定时任务成功");
        } else {
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), "该降级无定时任务");
        }
    }

    /**
     * 手动设置降级开关
     *
     * @param degradationControlRequest
     * @return
     */
    @MonitorOperationsAuth
    @RequestMapping(value = "/degradation.task.control", method = RequestMethod.POST)
    public CommonApiResponse<DegradationControlResponse>
        manualControlDegradation(@RequestBody DegradationControlRequest degradationControlRequest) {
        // 查询当前任务状态信息
        DegradationTask degradationSchemeInfo =
            degradationTaskService.getDegradationTask(degradationControlRequest.getTaskName());
        if (degradationSchemeInfo.getSwitchStatus() != Integer.parseInt(degradationControlRequest.getSwitchStatus())) {
            // 判断是否存在定时任务
            List<String> taskList = degradationTaskSchedulerService.getTaskList();
            // for (String taskName : taskList) {
            if (taskList.contains(degradationControlRequest.getTaskName() + OPEN_TASK_SUFFIX)) {
                degradationTaskSchedulerService.stopTask(degradationControlRequest.getTaskName() + OPEN_TASK_SUFFIX);
                degradationTaskService.updateDegradationTimer(degradationControlRequest.getTaskName(),
                    LocalDateTime.now(), null);
            }
            if (taskList.contains(degradationControlRequest.getTaskName() + CLOSE_TASK_SUFFIX)) {
                degradationTaskSchedulerService.stopTask(degradationControlRequest.getTaskName() + CLOSE_TASK_SUFFIX);
                degradationTaskService.updateDegradationTimer(degradationControlRequest.getTaskName(), null,
                    LocalDateTime.now());
            }
            // }
            DegradationTask stateInfo = degradationExecuteService.executeDegradationTask(degradationSchemeInfo);
            if (stateInfo == null) {
                return CommonApiResponse.of(CommonApiStatus.Failed.code(), "降级验证未通过，请检查参数");
            } else {
                DegradationControlResponse degradationControlResponse = new DegradationControlResponse();
                degradationControlResponse.setTaskName(stateInfo.getName());
                degradationControlResponse.setSwitchStatus(stateInfo.getSwitchStatus());
                return CommonApiResponse.success(degradationControlResponse);
            }

        } else {
            return CommonApiResponse.of(CommonApiStatus.Failed.code(), "任务已经开启");
        }
    }
}
