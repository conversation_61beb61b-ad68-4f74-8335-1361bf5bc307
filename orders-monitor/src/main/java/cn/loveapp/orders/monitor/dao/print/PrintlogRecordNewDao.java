package cn.loveapp.orders.monitor.dao.print;

import cn.loveapp.orders.monitor.entity.PrintlogRecordNew;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * (PrintlogRecordNew)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-08-04 21:32:37
 */
public interface PrintlogRecordNewDao {

	/**
	 * 依据时间查询指定行数据
	 *
	 * @param offset 查询起始位置
	 * @param limit 查询条数
	 * @return 对象列表
	 */
	List<PrintlogRecordNew> queryAllByLimit(@Param("sellerNick") String sellerNick, @Param("startTime") LocalDateTime startTime,
		@Param("endTime") LocalDateTime endTime, @Param("offset") long offset, @Param("limit") int limit);

}
