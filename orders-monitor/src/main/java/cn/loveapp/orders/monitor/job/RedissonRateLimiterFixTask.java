package cn.loveapp.orders.monitor.job;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhongzijie
 * @Date: 2024/4/15 18:55
 * @Description: redisson限流器在阿里云redis cluster模式下会出现令牌数降至0后不再生成令牌的情况，这里做一个定时检测并修复的工作
 */
@Component
public class RedissonRateLimiterFixTask {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RedissonRateLimiterFixTask.class);

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Value("${orders.scheduler.redisson.ratelimiter.enabled:false}")
    private boolean enabled;

    @Value("${orders.scheduler.redisson.ratelimiter.checkTimeout.minutes:5}")
    private Integer checkTimeoutMinutes;

    @Value("${orders.scheduler.redisson.ratelimiter.fixKeys:pdd.list.get.all.seller.rate.limit}")
    private List<String> fixKeys = Lists.newArrayList();

    @Autowired
    private MonitorConfig monitorConfig;

    private Map<String, LocalDateTime> checkBeginMap = Maps.newHashMap();

    /**
     * 检测并修复限流令牌数，防止长时间处于0导致不可用的情况
     */
    @Scheduled(cron = "${orders.scheduler.redisson.ratelimiter.checkAndFixRateValueTask.corn:0 0/1 * * * ?}")
    public void checkAndFixRateValueTask() {
        if (!enabled || !monitorConfig.isEnableScheduling()) {
            return;
        }
        MDC.put("taskName", "检测并修复限流令牌数");
        try {
            for (String key : fixKeys) {
                checkAndFix(key);
            }
        } finally {
            MDC.remove("taskName");
        }
    }

    /**
     * 检测并修复单个限流器
     *
     * @param key
     */
    private void checkAndFix(String key) {
        String rateValueKey = getRateValueKey(key);
        LOGGER.logInfo(rateValueKey, "-", "开始检测限流器");
        ValueOperations<String, String> opsForValue = stringRedisTemplate.opsForValue();

        String value = opsForValue.get(rateValueKey);
        LocalDateTime checkBegin = checkBeginMap.get(rateValueKey);
        if (value != null && Integer.parseInt(value) == 0) {
            if (checkBegin == null) {
                checkBegin = LocalDateTime.now();
                checkBeginMap.put(rateValueKey, checkBegin);
            }
        } else {
            checkBegin = null;
            checkBeginMap.put(rateValueKey, null);
        }

        if (checkBegin != null && checkBegin.plusMinutes(checkTimeoutMinutes).isBefore(LocalDateTime.now())) {
            stringRedisTemplate.delete(rateValueKey);
            checkBeginMap.put(rateValueKey, null);
            LOGGER.logInfo(rateValueKey, "-", "检测到限流器令牌数长时间处于0，删除key");
        }
        LOGGER.logInfo(rateValueKey, "-", "结束检测限流器");
    }

    /**
     * 获得记录限流器令牌数的key
     *
     * @param key
     * @return
     */
    private String getRateValueKey(String key) {
        if (key.contains("{")) {
            return key + ":value";
        }
        return "{" + key + "}:value";
    }

}
