package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.service.MonitorService;
import com.google.common.collect.HashBasedTable;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.impl.factory.MQClientInstance;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.admin.ConsumeStats;
import org.apache.rocketmq.common.admin.OffsetWrapper;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.common.protocol.ResponseCode;
import org.apache.rocketmq.common.protocol.body.GroupList;
import org.apache.rocketmq.common.protocol.body.TopicList;
import org.apache.rocketmq.common.protocol.route.BrokerData;
import org.apache.rocketmq.common.protocol.route.TopicRouteData;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.apache.rocketmq.tools.admin.DefaultMQAdminExt;
import org.apache.rocketmq.tools.admin.DefaultMQAdminExtImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * OrderOnsCheckServiceImpl
 *
 * <AUTHOR>
 * @date 2018-12-18
 */
@Service
public class OnsMonitorChecker extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsMonitorChecker.class);
	public static final String ONS_IYSAVER = "iysaverdsorders";

	@Autowired
	private MonitorService monitorService;

	@Autowired
	private MeterRegistry registry;

	@Autowired
	private DefaultMQProducer producer;

	@Autowired
	public MonitorConfig monitorConfig;

	private DefaultMQAdminExt defaultMQAdminExt;

	private MQClientInstance mqClientInstance;

	protected ThreadPoolExecutor threadPool = null;

	protected HashBasedTable<String, String, Double> diffs = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> delays = HashBasedTable.create();
	protected HashBasedTable<String, String, Double> tpses = HashBasedTable.create();

	@Override
	public CheckerType type() {
		return CheckerType.ONS;
	}

	@Override
	public String name() {
		return "ons堆积";
	}

	@Override
	protected boolean enableDebug() {
		return true;
	}

	@PostConstruct
	public void init() {
		threadPool = new ThreadPoolExecutor(20, 20, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
			new ThreadFactoryBuilder().setNameFormat(name() + "-pool-%d").build(),
			(Runnable r, ThreadPoolExecutor executor) -> {
				if (!executor.isShutdown()) {
					try {
						executor.getQueue().put(r);
					} catch (InterruptedException e) {
						LOGGER.logError(e.toString(), e);
						Thread.currentThread().interrupt();
					}
				}
			});
		threadPool.allowCoreThreadTimeOut(true);

		defaultMQAdminExt = new DefaultMQAdminExt();
		defaultMQAdminExt.setNamesrvAddr(producer.getNamesrvAddr());
		try {
			defaultMQAdminExt.start();
			DefaultMQAdminExtImpl defaultMQAdminExtImpl = (DefaultMQAdminExtImpl)FieldUtils.readDeclaredField(defaultMQAdminExt, "defaultMQAdminExtImpl", true);
			mqClientInstance = (MQClientInstance)FieldUtils.readDeclaredField(defaultMQAdminExtImpl, "mqClientInstance", true);
		} catch (Exception e) {
			LOGGER.logError(e.getMessage(), e);
		}
	}

	@Override
	protected synchronized String innerCheck() {
		try {
			TopicList topicList = defaultMQAdminExt.fetchAllTopicList();
			for (String topic : topicList.getTopicList()) {
				try {
					GroupList groupList = defaultMQAdminExt.queryTopicConsumeByWho(topic);
					for (String group : groupList.getGroupList()) {
						if (isStoped) {
							return null;
						}
						threadPool.execute(() -> {
							try {
								ConsumeStats consumeStats = examineConsumeStats(group, topic);
								double diffTotal = consumeStats.computeTotalDiff();
								double tps = consumeStats.getConsumeTps();
								double delay = tps == 0 ? 0 : diffTotal / tps;
								if (diffTotal > 0 && tps == 0) {
									delay = -1;
								}
								diffs.put(topic, group, diffTotal);
								tpses.put(topic, group, tps);
								delays.put(topic, group, delay);

								Gauge.builder("monitor_ons_diff", MonitoringSummaryData.getInstance(), x -> diffs.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_ons_tps", MonitoringSummaryData.getInstance(), x -> tpses.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

								Gauge.builder("monitor_ons_delay", MonitoringSummaryData.getInstance(), x -> delays.get(topic, group))
									.tag("topic", topic).tag("groupId", group).register(registry);

							} catch (MQClientException e) {
								if(e.getResponseCode() == ResponseCode.CONSUMER_NOT_ONLINE) {
//									LOGGER.logError("topic=" + topic + " group=" + group + ", consumer不在线");
								}else if(e.getResponseCode() == ResponseCode.TOPIC_NOT_EXIST){
//									LOGGER.logError("topic=" + topic + " group=" + group + ", topic不存在");
								}else{
									LOGGER.logError(e.getMessage(), e);
								}
							} catch (Exception e) {
								LOGGER.logError(e.getMessage(), e);
							}
						});
					}
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
			}
		} catch (Exception e) {
			LOGGER.logError("监控ONS堆积信息失败", e);
		}
		return null;
	}

	/**
	 *
	 * @param topic
	 * @param consumeStats
	 * @param diffTotal
	 * @return
	 */
	private double checkAndSetErrorOrderRdsMessageQueues(String topic, ConsumeStats consumeStats, double diffTotal) {
		HashMap<MessageQueue, OffsetWrapper> offsetTable = consumeStats.getOffsetTable();
		double max = 0;
		double min = Long.MAX_VALUE;
		for(OffsetWrapper offsetWrapper : offsetTable.values()){
			long diff = offsetWrapper.getBrokerOffset() - offsetWrapper.getConsumerOffset();
			diffTotal += diff;
			max = Math.max(max, diff);
			min = Math.min(min, diff);
		}
		return diffTotal;
	}

	private ConsumeStats examineConsumeStats(String consumerGroup,
		String topic) throws RemotingException, MQClientException,
		InterruptedException, MQBrokerException {
		String retryTopic = MixAll.getRetryTopic(consumerGroup);
		TopicRouteData topicRouteData = defaultMQAdminExt.examineTopicRouteInfo(retryTopic);
		ConsumeStats result = new ConsumeStats();

		for (BrokerData bd : topicRouteData.getBrokerDatas()) {
			HashMap<Long, String> brokerAddrs = bd.getBrokerAddrs();
			if(brokerAddrs == null || brokerAddrs.isEmpty()){
				continue;
			}
			List<String> addrs = brokerAddrs.keySet().stream().sorted(Comparator.comparingLong(Long::longValue).reversed()).map(
				brokerAddrs::get).collect(Collectors.toList());
			for(String addr : addrs){
				if (addr != null) {
					ConsumeStats consumeStats =
						mqClientInstance.getMQClientAPIImpl().getConsumeStats(addr, consumerGroup, topic, 5000 * 3);
					HashMap<MessageQueue, OffsetWrapper> newOffsetTable = consumeStats.getOffsetTable();
					HashMap<MessageQueue, OffsetWrapper> oldOffsetTable = result.getOffsetTable();
					if(oldOffsetTable.isEmpty()){
						oldOffsetTable.putAll(newOffsetTable);
					}else if(newOffsetTable != null){
						for (MessageQueue messageQueue : oldOffsetTable.keySet()) {
							OffsetWrapper oldOffsetWrapper = oldOffsetTable.get(messageQueue);
							OffsetWrapper newOffsetWrapper = newOffsetTable.get(messageQueue);
							if(newOffsetWrapper != null && newOffsetWrapper.getConsumerOffset() > oldOffsetWrapper.getConsumerOffset()){
								oldOffsetTable.put(messageQueue, newOffsetWrapper);
							}
						}
					}
					double value = result.getConsumeTps() + consumeStats.getConsumeTps();
					result.setConsumeTps(value);
				}
			}
		}

		if (result.getOffsetTable().isEmpty()) {
			throw new MQClientException(ResponseCode.CONSUMER_NOT_ONLINE,
				"Not found the consumer group consume stats, because return offset table is empty, maybe the consumer not consume any message");
		}

		return result;
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		super.onApplicationEvent(event);
		if (!isStoped) {
			return;
		}
		if (defaultMQAdminExt != null) {
			try {
				defaultMQAdminExt.shutdown();
			} catch (Exception e) {
			}
		}
	}

}
