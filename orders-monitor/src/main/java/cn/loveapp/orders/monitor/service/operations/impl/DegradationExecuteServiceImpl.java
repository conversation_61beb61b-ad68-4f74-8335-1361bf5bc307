package cn.loveapp.orders.monitor.service.operations.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.NamespaceGrayDelReleaseDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.config.operations.DegradationConfiguration;
import cn.loveapp.orders.monitor.constant.operations.DegradedState;
import cn.loveapp.orders.monitor.entity.operations.DegradationTask;
import cn.loveapp.orders.monitor.entity.operations.DegradationTaskLog;
import cn.loveapp.orders.monitor.service.operations.DegradationExecuteService;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskLogService;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskService;
import jodd.util.StringUtil;

/**
 * 降级接口配置操作实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-02 14:33
 */
@Service
public class DegradationExecuteServiceImpl implements DegradationExecuteService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DegradationExecuteServiceImpl.class);

    @Autowired
    private DegradationTaskLogService degradationTaskLogService;
    @Autowired
    private DegradationTaskService degradationTaskService;
    @Autowired
    private DegradationConfiguration degradationConfiguration;
    @Autowired
    private ApolloOpenApiClient apolloOpenApiClient;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public DegradationTask executeDegradationTask(DegradationTask degradationTask) {

        DegradationTask.DegradationTaskParameter degradationTaskConfig =
            degradationTask.getDDegradationTaskConfig(degradationTask.getConfigurationParameter());
        DegradationTask stateInfo = null;
        if (DegradedState.CONFIGURATION_INTERFACE_USER_REDIS
            .equals(degradationTaskConfig.getConfigurationInterface())) {
            stateInfo =
                executeRedisInteriorDegradationTaks(degradationTask, degradationConfiguration.getUserRedisTemplate());
        }
        if (DegradedState.CONFIGURATION_INTERFACE_STRING_REDIS
            .equals(degradationTaskConfig.getConfigurationInterface())) {
            stateInfo =
                executeRedisInteriorDegradationTaks(degradationTask, degradationConfiguration.getUserRedisTemplate());
        }
        if (DegradedState.CONFIGURATION_INTERFACE_MYSQL.equals(degradationTaskConfig.getConfigurationInterface())) {
            stateInfo = executeMysqlInteriorDegradationTask(degradationTask);
        }
        if (DegradedState.CONFIGURATION_INTERFACE_APOLLO.equals(degradationTaskConfig.getConfigurationInterface())) {
            stateInfo = executeApolloInteriorDegradationTask(degradationTask);
        }
        return stateInfo;

    }

    /**
     * 降级任务处理-mysql
     *
     * @param degradationTask
     * @return
     */
    private DegradationTask executeMysqlInteriorDegradationTask(DegradationTask degradationTask) {
        DegradationTask.DegradationTaskParameter degradationTaskConfig =
            degradationTask.getDDegradationTaskConfig(degradationTask.getConfigurationParameter());
        int update;
        String sql = degradationTaskConfig.getKey();
        try {
            update = jdbcTemplate.update(sql);
        } catch (DataAccessException e) {
            LOGGER.logError("mysql语句错误", e);
            update = 0;
        }
        DegradationTaskLog degradationTaskLog = new DegradationTaskLog();
        degradationTaskLog.setTaskName(degradationTask.getName());
        if (update > 0) {
            // 添加日志
            degradationTaskLog
                .setContent("降级任务[" + degradationTask.getName() + "]执行成功\r\nSQL：" + sql + "\r\n结果：" + update);
            LOGGER.logInfo(degradationTask.getName(), null, "降级操作执行启动");
        } else {
            degradationTaskLog.setContent("降级任务[" + degradationTask.getName() + "]执行失败");
            return null;
        }
        degradationTaskLogService.addDegradationTaskLog(degradationTaskLog);
        return degradationTask;
    }

    /**
     * Apollo配置处理
     *
     * @param degradationTask
     * @return
     */
    private DegradationTask executeApolloInteriorDegradationTask(DegradationTask degradationTask) {
        DegradationTask.DegradationTaskParameter degradationTaskConfig =
            degradationTask.getDDegradationTaskConfig(degradationTask.getConfigurationParameter());
        String env = "dev";
        // apollo中项目id
        String appId = degradationTaskConfig.getProjectId();
        // 操作的用户
        String opUser = degradationTaskConfig.getUserName();
        // 集群名称
        String cluster = degradationTaskConfig.getAddressName();
        // namespace名称
        String namespace = degradationTaskConfig.getNameSpace();
        String key = degradationTaskConfig.getKey();

        OpenItemDTO openItemDTO = new OpenItemDTO();
        openItemDTO.setKey(key);
        openItemDTO.setDataChangeCreatedBy(opUser);
        OpenItemDTO changeBeforeItem = apolloOpenApiClient.getItem(appId, env, cluster, namespace, key);
        System.out.println(changeBeforeItem.getValue());
        if (DegradedState.DEGRADATION_SWITCH_OFF.equals(degradationTask.getSwitchStatus())) {
            // 开启降级
            openItemDTO.setValue(degradationTaskConfig.getOpenStatusParameter());
            degradationTask.setSwitchStatus(DegradedState.DEGRADATION_SWITCH_ON);
            LOGGER.logInfo(degradationTask.getName(), null, "降级启动");
        } else {
            // 恢复
            openItemDTO.setValue(degradationTaskConfig.getCloseStatusParameter());
            degradationTask.setSwitchStatus(DegradedState.DEGRADATION_SWITCH_OFF);
            LOGGER.logInfo(degradationTask.getName(), null, "降级恢复");
        }
        apolloOpenApiClient.createOrUpdateItem(appId, env, cluster, namespace, openItemDTO);
        // 刷新说明
        NamespaceGrayDelReleaseDTO namespaceGrayDelReleaseDTO = new NamespaceGrayDelReleaseDTO();
        namespaceGrayDelReleaseDTO.setReleaseTitle(System.currentTimeMillis() + "-release");
        namespaceGrayDelReleaseDTO.setReleaseComment("auto release");
        namespaceGrayDelReleaseDTO.setReleasedBy(opUser);
        apolloOpenApiClient.publishNamespace(appId, env, cluster, namespace, namespaceGrayDelReleaseDTO);

        OpenItemDTO changeAfterItem = apolloOpenApiClient.getItem(appId, env, cluster, namespace, key);
        if (!changeBeforeItem.getValue().equals(changeAfterItem.getValue())) {
            LOGGER.logInfo(degradationTask.getName(), null, "降级验证成功");
            int i = degradationTaskService.updateDegradationTask(degradationTask);
            // 添加日志
            DegradationTaskLog degradationTaskLog = new DegradationTaskLog();
            degradationTaskLog.setTaskName(degradationTask.getName());
            if (i > 0) {
                degradationTaskLog.setContent("降级任务[" + degradationTask.getName() + "]此时状态为"
                    + (DegradedState.DEGRADATION_SWITCH_OFF.equals(degradationTask.getSwitchStatus()) ? "未降级" : "已降级"));
            } else {
                degradationTaskLog.setContent("降级任务[" + degradationTask.getName() + "]此时状态未发生改变");
            }
            degradationTaskLogService.addDegradationTaskLog(degradationTaskLog);
            return degradationTask;
        } else {
            LOGGER.logInfo(degradationTask.getName(), null, "降级验证失败");
            return null;
        }

    }

    /**
     * redis配置处理
     *
     * @param degradationTask
     * @return
     */
    private DegradationTask executeRedisInteriorDegradationTaks(DegradationTask degradationTask,
        StringRedisTemplate stringRedisTemplate) {
        DegradationTask.DegradationTaskParameter degradationTaskConfig =
            degradationTask.getDDegradationTaskConfig(degradationTask.getConfigurationParameter());
        try {
            // 降级
            if (DegradedState.DEGRADATION_SWITCH_OFF.equals(degradationTask.getSwitchStatus())) {
                if (StringUtil.isNotEmpty(degradationTaskConfig.getNameSpace())) {

                    Object changeBeforeValue = stringRedisTemplate.opsForHash()
                        .get(degradationTaskConfig.getNameSpace(), degradationTaskConfig.getKey());
                    stringRedisTemplate.opsForHash().put(degradationTaskConfig.getNameSpace(),
                        degradationTaskConfig.getKey(), degradationTaskConfig.getOpenStatusParameter());
                    Object changeAfterValue = stringRedisTemplate.opsForHash().get(degradationTaskConfig.getNameSpace(),
                        degradationTaskConfig.getKey());
                    if (!changeBeforeValue.equals(changeAfterValue)) {
                        LOGGER.logInfo(degradationTask.getName(), null, "降级启动验证成功");
                    } else {
                        LOGGER.logInfo(degradationTask.getName(), null, "降级启动验证失败");
                        return null;
                    }
                } else {
                    String changeBeforeValue = stringRedisTemplate.opsForValue().get(degradationTaskConfig.getKey());
                    stringRedisTemplate.opsForValue().set(degradationTaskConfig.getKey(),
                        degradationTaskConfig.getOpenStatusParameter());
                    String changeAfterValue = stringRedisTemplate.opsForValue().get(degradationTaskConfig.getKey());

                    if (changeAfterValue == null || changeBeforeValue == null) {
                        return null;
                    } else {
                        if (!changeBeforeValue.equals(changeAfterValue)) {
                            LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证成功");
                        } else {
                            LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证失败");
                            return null;
                        }
                    }
                }
                degradationTask.setSwitchStatus(DegradedState.DEGRADATION_SWITCH_ON);
                LOGGER.logInfo(degradationTask.getName(), null, "降级启动");

            } else {
                // 恢复
                if (StringUtil.isNotEmpty(degradationTaskConfig.getNameSpace())) {
                    Object changeBeforeValue = stringRedisTemplate.opsForHash()
                        .get(degradationTaskConfig.getNameSpace(), degradationTaskConfig.getKey());
                    stringRedisTemplate.opsForHash().put(degradationTaskConfig.getNameSpace(),
                        degradationTaskConfig.getKey(), degradationTaskConfig.getCloseStatusParameter());
                    Object changeAfterValue = stringRedisTemplate.opsForHash().get(degradationTaskConfig.getNameSpace(),
                        degradationTaskConfig.getKey());
                    if (!changeBeforeValue.toString().equals(changeAfterValue.toString())) {
                        LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证成功");
                    } else {
                        LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证失败");
                        return null;
                    }
                } else {
                    String changeBeforeValue = stringRedisTemplate.opsForValue().get(degradationTaskConfig.getKey());
                    stringRedisTemplate.opsForValue().set(degradationTaskConfig.getKey(),
                        degradationTaskConfig.getCloseStatusParameter());
                    String changeAfterValue = stringRedisTemplate.opsForValue().get(degradationTaskConfig.getKey());
                    if (changeAfterValue == null || changeBeforeValue == null) {
                        return null;
                    } else {
                        if (!changeBeforeValue.equals(changeAfterValue)) {
                            LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证成功");
                        } else {
                            LOGGER.logInfo(degradationTask.getName(), null, "降级恢复验证失败");
                            return null;
                        }
                    }
                }
                degradationTask.setSwitchStatus(DegradedState.DEGRADATION_SWITCH_OFF);
                LOGGER.logInfo(degradationTask.getName(), null, "降级恢复");
            }
        } catch (Exception e) {
            LOGGER.logError("redis连接错误", e);
            return null;
        }
        int i = degradationTaskService.updateDegradationTask(degradationTask);
        // 添加日志
        DegradationTaskLog degradationTaskLog = new DegradationTaskLog();
        degradationTaskLog.setTaskName(degradationTask.getName());
        if (i > 0) {
            degradationTaskLog.setContent("降级任务[" + degradationTask.getName() + "]此时状态为"
                + (DegradedState.DEGRADATION_SWITCH_OFF.equals(degradationTask.getSwitchStatus()) ? "未降级" : "已降级"));
        } else {
            degradationTaskLog.setContent("降级任务[" + degradationTask.getName() + "]此时状态未发生改变");
        }
        degradationTaskLogService.addDegradationTaskLog(degradationTaskLog);
        return degradationTask;
    }
}
