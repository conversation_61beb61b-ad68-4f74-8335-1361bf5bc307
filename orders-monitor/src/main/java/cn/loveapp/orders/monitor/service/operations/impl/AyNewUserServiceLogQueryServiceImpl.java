package cn.loveapp.orders.monitor.service.operations.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.monitor.constant.operations.LogEsIndexConstant;
import cn.loveapp.orders.monitor.constant.operations.ParamStatusTagConstant;
import cn.loveapp.orders.monitor.dao.es.MonitorAyLogSearchESDao;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainDTO;
import cn.loveapp.orders.monitor.dto.operations.AyUserProblemQueryChainRequest;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.service.operations.AyUserProblemQueryService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 20:22
 * @Description: 爱用NewUser-service日志查询服务接口
 */
@Service
public class AyNewUserServiceLogQueryServiceImpl implements AyUserProblemQueryService {
    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyNewUserServiceLogQueryServiceImpl.class);

    /**
     * NewUser-Service 异常日志字段
     */
    public static final String RDS_SUB_ERR = "该子帐号无此操作权限";

    @Autowired
    private MonitorAyLogSearchESDao monitorAyLogSearchESDao;

    @Override
    public AyUserProblemQueryChainRequest logQueryDispose(AyUserProblemQueryChainDTO ayUserProblemQueryChainDTO) {
        AyUserProblemQueryChainRequest request = ayUserProblemQueryChainDTO.ayUserProblemQueryChainRequest();
        String sellerNick = request.getSellerNick();
        ProblemAnalysisResponse problemAnalysisResponse = request.getProblemAnalysisResponse();
        ProblemAnalysisResponse.Param param;
        List<String> newUserSchedulerMessage = monitorAyLogSearchESDao.getLogMessageList(sellerNick,
            Lists.newArrayList(sellerNick, RDS_SUB_ERR), LogEsIndexConstant.ORDER_NEWUSER_SERVICE_INDEX_PREFIX);
        if (newUserSchedulerMessage.size() > 0) {
            param = new ProblemAnalysisResponse.Param("NewUser-service日志", "子账号无此权限，需要主账号开通权限",
                ParamStatusTagConstant.ERRORTAG);
            problemAnalysisResponse.getParams().add(param);
            return request;
        }
        return ayUserProblemQueryChainDTO.proceed(request);
    }
}
