package cn.loveapp.orders.monitor.service.operations.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import cn.loveapp.orders.monitor.dao.dream.DegradationTaskDao;
import cn.loveapp.orders.monitor.dto.operations.DegradationListResponse;
import cn.loveapp.orders.monitor.entity.operations.DegradationTask;
import cn.loveapp.orders.monitor.service.operations.DegradationTaskService;

import java.time.LocalDateTime;

/**
 * 降级任务方案表(DegradationTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-29 11:31:42
 */
@Service
public class DegradationTaskServiceImpl implements DegradationTaskService {
    @Autowired
    private DegradationTaskDao degradationTaskDao;

    @Override
    public DegradationListResponse pageDegradationTask(String taskName, Pageable pageable) {
        DegradationListResponse degradationListResponse = new DegradationListResponse();
        degradationListResponse.setDegradationTaskList(degradationTaskDao.findAll(taskName, pageable));
        degradationListResponse.setTotal(degradationTaskDao.queryTotal(taskName));
        return degradationListResponse;
    }

    @Override
    public DegradationTask getDegradationTask(String name) {
        return degradationTaskDao.queryByName(name);
    }

    @Override
    public void setDegradationTask(DegradationTask degradationTask) {
        degradationTaskDao.insert(degradationTask);
    }

    @Override
    public int updateDegradationTask(DegradationTask degradationTask) {
        return degradationTaskDao.updateByName(degradationTask);
    }

    @Override
    public int removeDegradationTask(String name) {
        return degradationTaskDao.deleteByName(name);
    }

    @Override
    public int updateDegradationTimer(String name, LocalDateTime startTime, LocalDateTime endTime) {
        return degradationTaskDao.updateTimer(name, startTime, endTime);
    }

}
