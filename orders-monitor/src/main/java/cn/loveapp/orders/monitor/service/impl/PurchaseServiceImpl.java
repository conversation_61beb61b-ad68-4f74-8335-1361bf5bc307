package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.biyao.BiyaoHongyuanSDKService;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.api.entity.AyTrade;
import cn.loveapp.orders.common.api.request.FullInfoRequest;
import cn.loveapp.orders.common.api.response.FullinfoResponse;
import cn.loveapp.orders.common.constant.AyPurchaseConstant;
import cn.loveapp.orders.common.constant.EsFields;
import cn.loveapp.orders.common.constant.MongoConstant;
import cn.loveapp.orders.common.dao.mongo.OrderRepository;
import cn.loveapp.orders.common.dto.AuthorizationInfoDTO;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.mongo.TcOrder;
import cn.loveapp.orders.common.platform.api.TradeApiPlatformHandleService;
import cn.loveapp.orders.common.service.UserCenterService;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.EmailHelper;
import cn.loveapp.orders.monitor.config.MonitorConfig;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.purchase.dto.PurchaseExcelDTO;
import cn.loveapp.orders.monitor.dto.purchase.request.SearchPurchaseRequest;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.PurchaseService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.biyao.common.domain.AppAuthInfo;
import com.biyao.hongyuan.api.domain.OrderInfo;
import com.biyao.hongyuan.api.request.OrderGetRequest;
import com.biyao.hongyuan.api.response.OrderGetResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * @program: orders-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2023/4/26 15:15
 **/
@Service
public class PurchaseServiceImpl implements PurchaseService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PurchaseServiceImpl.class);

    public DateTimeFormatter formatter = DateTimeFormatter.ofPattern(AyTradeSearchES.MINUTE_SECOND_FORMATER);

    @Value("${orders.purchase.exportToExcel.maxSize:10000}")
    private long maxExportSize;

    @Autowired
    private MonitorAyTradeSearchESDao esDao;

    @Autowired
    private TradeApiPlatformHandleService tradeApiPlatformHandleService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private BiyaoHongyuanSDKService hongyuanClient;

    @Autowired
    private EmailHelper emailHelper;

    @Autowired
    private MonitorConfig monitorConfig;

    @Override
    public void exportToExcel(SearchPurchaseRequest request, HttpServletResponse response)
        throws ApiServerErrorException, IOException {
        BoolQueryBuilder queryBuilder = createBoolQuery(request);
        // 查询es
        int count = (int)esDao.countByBuilder(queryBuilder);
        if (count > maxExportSize) {
            throw new ApiServerErrorException("查询数量：" + count + "，超出可导出最大数量");
        } else if (count <= 0) {
            throw new ApiServerErrorException("无可导出订单");
        }

        // 创建一个临时文件
        File tempFile = File.createTempFile(
            "purchase-export-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), ".xlsx");
        // 使用EasyExcel创建ExcelWriter，设置临时文件作为写入目标
        ExcelWriter excelWriter = EasyExcel.write(tempFile, PurchaseExcelDTO.class).build();

        // 计数
        AtomicInteger resultCount = new AtomicInteger();
        String scrollId = null;
        try {
            ScrolledPage<AyTradeSearchES> scrolledPage = null;
            do {
                scrolledPage = esDao.scrollQueryByBuilder(queryBuilder, scrollId, 100,
                    new String[] {EsFields.tid, EsFields.appName, EsFields.storeId});
                if (scrolledPage == null || CollectionUtils.isEmpty(scrolledPage.getContent())) {
                    break;
                }
                scrollId = scrolledPage.getScrollId();
                List<AyTradeSearchES> searchESList = scrolledPage.getContent();
                List<PurchaseExcelDTO> purchaseExcelDTOS = new ArrayList<>();
                // 将查询结果转为导出对象
                searchESList.forEach(es -> {
                    TcOrder order = orderRepository.queryByTid(es.getTid(), es.getStoreId(), es.getAppName(),
                        Lists.newArrayList(MongoConstant.PURCHASE_TRADE_INFO_FIELDS));
                    if (Objects.isNull(order)) {
                        return;
                    }
                    PurchaseExcelDTO purchaseExcel = createPurchaseExcel(order);
                    if (request.isOnlyError() && !purchaseExcel.isError()) {
                        return;
                    }
                    resultCount.getAndIncrement();
                    purchaseExcelDTOS.add(purchaseExcel);
                });
                // 将 purchaseExcelDTOS 写入到 sheet 中
                // 创建 sheet
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.write(purchaseExcelDTOS, writeSheet);
            } while (scrolledPage != null && scrolledPage.hasNext());

            if (resultCount.get() <= 0) {
                tempFile.delete();
                throw new ApiServerErrorException("无可导出订单");
            }
        } catch (Exception e) {
            LOGGER.logError("", "", "查询时发生异常，错误原因： " + e.getMessage(), e);
            tempFile.delete();
            throw new ApiServerErrorException("查询时发生异常，错误原因:" + e.getMessage(), e);
        } finally {
            excelWriter.finish();
            // 清除窗口
            esDao.clearScroll(scrollId);
        }

        OutputStream outputStream = response.getOutputStream();
        FileInputStream fileInputStream = new FileInputStream(tempFile);
        try {
            if (request.isSendEmail()) {
                // 发送邮件
                String from = monitorConfig.getMailFrom();
                String[] to = monitorConfig.getMailTo().toArray(new String[0]);
                emailHelper.sendEmailByFile("必要采购单状态核对", from, to, tempFile);
            }

            // 设置文件名
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + tempFile.getName());
            byte[] bytes = new byte[1024];
            int len;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.flush();
        } finally {
            outputStream.close();
            fileInputStream.close();
            // 删除临时文件
            tempFile.delete();
        }
    }

    /**
     * 封装Excel对象
     *
     * @param order
     * @return
     */
    private PurchaseExcelDTO createPurchaseExcel(TcOrder order) {
        PurchaseExcelDTO purchaseExcel = new PurchaseExcelDTO();
        purchaseExcel.setSourceStoreId(order.getSourceStoreId());

        // 订单采购状态
        String distributeStatus = order.getDistributeStatus();
        Integer biyaoOrderStatus = -1;

        if (!AyPurchaseConstant.CREATE_FAIL.equals(distributeStatus)) {
            purchaseExcel.setPurchaseId(order.getTid());
        }
        purchaseExcel.setPurchaseAyStatusCode(distributeStatus);
        purchaseExcel.setPurchaseAyStatusName(AyPurchaseConstant.getPurcaseStatusName(distributeStatus));

        // 查询三方api
        AyTrade ayTrade = fullinfoByApi(order);
        purchaseExcel.setCpTid(order.getBizParentOrderId());
        if (ayTrade != null) {
            purchaseExcel.setCpTid(order.getBizParentOrderId());
            purchaseExcel.setCpStatusCode(ayTrade.getStatus());
            purchaseExcel.setCpStatusName(AyPurchaseConstant.getCpStatusName(ayTrade.getStatus()));
            purchaseExcel.setCreateTime(DateUtil.convertDatetoString(ayTrade.getCreated()));
        }

        // 查询必要采购单api
        OrderInfo orderInfo = queryByHyApi(order);
        if (orderInfo != null) {
            purchaseExcel.setPurchaseHyStatusCode(orderInfo.getOrderStatus());
            biyaoOrderStatus = orderInfo.getOrderStatus();
            purchaseExcel.setPurchaseHyStatusName(AyPurchaseConstant.getHyStatusName(biyaoOrderStatus));
        }

        // todo 查询售后
        // if( hasRefund)

        // 校验订单是否异常
        // todo 售后校验订单是否异常可以加这个方法里也可以单独设置
        if (AyPurchaseConstant.checkPurchaseStatus(distributeStatus, biyaoOrderStatus)) {
            purchaseExcel.setError(true);
        }

        return purchaseExcel;
    }

    private OrderInfo queryByHyApi(TcOrder order) {
        // 查询必要api
        AppAuthInfo appAuthInfo =
            userService.getAppAuthInfo(order.getDistributorNick(), String.valueOf(order.getDistributorId()),
                CommonAppConstants.APP_DISTRIBUTE, CommonPlatformConstants.PLATFORM_BIYAO);
        // 订单已产生，走查询逻辑返回订单
        OrderGetRequest orderGetRequest = new OrderGetRequest();
        orderGetRequest.setHyOrderId(Long.valueOf(order.getTid()));
        OrderGetResponse orderGetResponse =
            hongyuanClient.execute(orderGetRequest, appAuthInfo, CommonAppConstants.APP_DISTRIBUTE);
        if (orderGetResponse != null && orderGetResponse.isSuccess()) {
            List<OrderInfo> data = orderGetResponse.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                return data.get(0);
            }
        }
        LOGGER.logError("采购单订单获取失败: " + JSON.toJSONString(orderGetResponse));
        return null;
    }

    /**
     * 三方平台
     *
     * @param tcOrder
     * @return
     */
    private AyTrade fullinfoByApi(TcOrder tcOrder) {
        FullInfoRequest fullInfoRequest = new FullInfoRequest();
        fullInfoRequest.setTid(tcOrder.getBizParentOrderId());
        AuthorizationInfoDTO authorizationInfoDTO = userCenterService.getTopSession(tcOrder.getSourceStoreId(), tcOrder.getSourceAppName(),
            tcOrder.getSourceSellerNick(), tcOrder.getSourceSellerId(), null);
        if (authorizationInfoDTO == null || StringUtils.isEmpty(authorizationInfoDTO.getTopSession())) {
            LOGGER.logError("三方平台授权异常");
            return null;
        }

        String topSession = authorizationInfoDTO.getTopSession();
        fullInfoRequest.setTopSession(topSession);
        fullInfoRequest.setSellerNick(tcOrder.getSellerNick());
        FullinfoResponse response = tradeApiPlatformHandleService.fullInfo(fullInfoRequest, tcOrder.getSourceStoreId(),
            tcOrder.getSourceAppName());
        if (response != null) {
            return response.getTrade();
        }
        return null;
    }

    private BoolQueryBuilder createBoolQuery(SearchPurchaseRequest request) {

        BoolQueryBuilder boolQueryBuilder =
            QueryBuilders.boolQuery().must(rangeQuery(EsFields.modified).lt(formatter.format(request.getEndTime())))
                .must(rangeQuery(EsFields.created).gt(formatter.format(request.getStartTime())))
                .mustNot(existsQuery(EsFields.mergeTradeStatus))
                .must(termQuery(EsFields.storeId, CommonPlatformConstants.PLATFORM_BIYAO));
        return boolQueryBuilder;
    }

}
