package cn.loveapp.orders.monitor.aop;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.monitor.constant.operations.ResponseConstant;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022-08-02 14:45
 * @Description: 运维平台鉴权切面
 */
@Aspect
@Component
public class MonitorOperationsAuthAspect {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(MonitorOperationsAuthAspect.class);

    private static final String MONITRO_PLATFORM_PREFIX = "monitor:platform:user:";

    @Autowired
    @Qualifier("userInfoStringRedisTemplate")
    private StringRedisTemplate template;

    @Pointcut("@annotation(cn.loveapp.orders.monitor.annotation.MonitorOperationsAuth)")
    public void monitorPlatformAuthPointcut() {

    }

    @Around("monitorPlatformAuthPointcut()")
    public Object aroundMonitorPlatformAuth(ProceedingJoinPoint pjp) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes attributes = (ServletRequestAttributes)requestAttributes;
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("token");
        if (StringUtils.isNotEmpty(token)) {
            if (template.opsForValue().get(token) != null) {
                Object proceed = pjp.proceed();
                return proceed;
            }
            return CommonApiResponse.failed(ResponseConstant.TOKEN_LOSE.getCode(),
                ResponseConstant.TOKEN_LOSE.getMessage());
        } else {
            return CommonApiResponse.failed(ResponseConstant.TOKEN_LOSE.getCode(),
                ResponseConstant.TOKEN_LOSE.getMessage());
        }
    }
}
