package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.entity.AyTradeSearchES;
import cn.loveapp.orders.common.entity.UserProductionInfoExt;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import cn.loveapp.orders.monitor.checker.CheckerType;
import cn.loveapp.orders.monitor.config.MonitoringSummaryData;
import cn.loveapp.orders.monitor.dao.es.MonitorAyTradeSearchESDao;
import cn.loveapp.orders.monitor.dto.BaseOrderApiCheckResult;
import cn.loveapp.orders.monitor.dto.FullinfoCheckResult;
import cn.loveapp.orders.monitor.dto.OrderApiCheckFailed;
import cn.loveapp.orders.monitor.exception.ApiServerErrorException;
import cn.loveapp.orders.monitor.service.OrdersApiService;
import cn.loveapp.orders.monitor.service.TaobaoApiService;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.api.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单正确率校验
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
@Service
public class FullinfoMonitorChecker extends AbstractOrderApiMonitorChecker<AyTradeSearchES> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(FullinfoMonitorChecker.class);
	public static final String TRADE_FULLINFO_GET_RESPONSE = "trade_fullinfo_get_response";
	private static final long TIMEOUT = 10000;
	protected static final int TABLE_COUNT = 1;
	protected Set<String> jsonStringKeys = Sets.newHashSet("trade_attr");

	@Autowired
	private TaobaoApiService taobaoApiService;

	@Autowired
	@Qualifier("ordersApiServiceImpl")
	private OrdersApiService ordersApiService;

	@Autowired
	private MonitorAyTradeSearchESDao monitorAyTradeSearchESDao;

	protected FullinfoCheckResult fullinfoCheckResult;

	@Override
	public CheckerType type() {
		return CheckerType.TAO_BAO_FULLINFO;
	}

	@Override
	public String name() {
		return "tao bao fullinfo";
	}

	@Override
	protected int getSampleTableCount() {
		return TABLE_COUNT;
	}

	@Override
	protected String getResultJsonRoot() {
		return TRADE_FULLINFO_GET_RESPONSE;
	}

	@Override
	protected String getStoreId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}

	@Override
	protected BaseOrderApiCheckResult getOrderApiCheckResult() {
		return fullinfoCheckResult;
	}

	@Override
	protected Set<String> checkAsJsonStringKeys(){
		return jsonStringKeys;
	}

	@Override
	protected Set<String> getNeedIgnoreKeys(){
		return monitorConfig.getFullinfoIgnores();
	}

	@Override
	protected synchronized String innerCheck() {
		fullinfoCheckResult = new FullinfoCheckResult();
		fullinfoCheckResult.setOrderStatus(monitorConfig.getFullinfoStatus());
		double accuracyRate;
		try {
			accuracyRate =
				checkSampleInTimeRange(monitorConfig.getFullinfoTimeRange(), fullinfoCheckResult.getOrderStatus(),
					monitorConfig.getFullinfoNumber());

			MonitoringSummaryData.getInstance().setFullInfoAccuracyRate(accuracyRate);

			if (!isStoped) {
				postCheck();
			}
		} finally {
			fullinfoCheckResult = null;
		}
		return String.valueOf(accuracyRate);
	}

	@Override
	protected String requestOrderServiceApi(AyTradeSearchES sample, LocalDateTime startTime, LocalDateTime endTime)
		throws ApiServerErrorException, IOException {
		return ordersApiService.fullinfo(sample);
	}

	@Override
	protected String requestPlatformApi(AyTradeSearchES sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException {
		return taobaoApiService.fullinfo(sample.getSellerNick(), Long.parseLong(sample.getTid()));
	}

	@Override
	protected boolean needCompareJson(AyTradeSearchES sample, OrderApiCheckFailed failedOrder,
		LocalDateTime taobaoApiTime, JSONObject apiJson, JSONObject taobaoJson, LocalDateTime startTime,
		LocalDateTime endTime) {
		Date taoModified = getModified(taobaoJson);
		Date apiModified = getModified(apiJson);
		if (taoModified != null && apiModified != null) {
			LocalDateTime taoDateTime = LocalDateTime.ofInstant(taoModified.toInstant(), ZoneId.systemDefault());
			long time = taoModified.getTime() - apiModified.getTime();
			if (time > 0 && taoDateTime.isAfter(taobaoApiTime)) {
				//当前分钟内的订单修改数据可能尚未接收到, 忽略
				info(sample, "TaoApi返回的modified在当前分钟内, 默认校验成功, modified:" + taoDateTime);
				return false;
			} else if (time >= TIMEOUT) {
				warn(sample, "TaoApi返回的modified比orderApi晚 " + (time / 1000) + " 秒, 订单可能延时");
			}
		}
		return true;
	}

	@Override
	public List<AyTradeSearchES> querySamples(LocalDateTime startTime, LocalDateTime endTime, String status, long listId) {
		long fullinfoNumber = monitorConfig.getFullinfoNumber();
		int page = 0;
		int limit = monitorConfig.getQueryLimit();
		List<AyTradeSearchES> result = Lists.newArrayList();
		while(true){
			List<AyTradeSearchES> pageResult;
			try {
				pageResult = monitorAyTradeSearchESDao
					.queryOrdersByTime(startTime, endTime, status, getStoreId(), page, limit);
			} finally {
				page++;
			}
			result.addAll(pageResult);
			if(pageResult.size() < limit || result.size() >= fullinfoNumber){
				break;
			}
		}
		LOGGER.logInfo(name() + " 样本数:" + result.size());
		return result;
	}

	@Override
	protected List<AyTradeSearchES> filterSamples(List<AyTradeSearchES> allSamples) {
		List<UserProductionInfoExt> allUsers = monitorUserService.findAllOrderUser();
		allUsers = allUsers.stream().filter(user->CommonPlatformConstants.PLATFORM_TAO.equals(user.getStoreId())).collect(Collectors.toList());

		return commonFilterSamples(allUsers,allSamples);
	}

	protected List<AyTradeSearchES> commonFilterSamples(List<UserProductionInfoExt> allUsers,List<AyTradeSearchES> allSamples){
		Set<String> nicks = allUsers.stream().map(UserProductionInfoExt::getSellerNick).collect(Collectors.toSet());
		LOGGER.logInfo(name() + " 过滤没有开通订单服务的用户");
		allSamples =
			allSamples.stream().filter(trade -> nicks.contains(trade.getSellerNick())).collect(Collectors.toList());
		return allSamples;
	}

	/**
	 * 交验后收尾处理
	 */
	protected void postCheck() {
		fullinfoCheckResult.setAccuracyRate(MonitoringSummaryData.getInstance().getFullInfoAccuracyRate());
		fullinfoCheckResult.getOrderMissCheckResult().increment(validNumber.longValue());

		//设置dbId
		monitorService.setDbId(fullinfoCheckResult);

		//发送校验结果通知
		monitorService.sendCheckResultNotify(CheckerType.TAO_BAO_FULLINFO.name(), fullinfoCheckResult,
			new DecimalFormat("#.####%").format(fullinfoCheckResult.getAccuracyRate()),"fullinfo");

	}

	/**
	 * 获取订单的modified字段
	 *
	 * @param taobaoOrder
	 * @return
	 */
	private Date getModified(JSONObject taobaoOrder) {
		return TypeUtils.castToDate(JSONPath.eval(taobaoOrder, "$.trade_fullinfo_get_response.trade.modified"));
	}

	@Override
	protected void error(AyTradeSearchES sample, String message, Throwable e) {
		LOGGER.logError(sample.getSellerNick(), sample.getTid(),
			name() + ", sellerId " + sample.getSellerId() + ", " + message, e);
	}

	@Override
	protected void error(AyTradeSearchES sample, String message) {
		LOGGER.logError(sample.getSellerNick(), sample.getTid(),
			name() + ", sellerId " + sample.getSellerId() + ", " + message);
	}

	@Override
	protected void info(AyTradeSearchES sample, String message) {
		LOGGER.logInfo(sample.getSellerNick(), sample.getTid(),
			name() + ", sellerId " + sample.getSellerId() + ", " + message);
	}

	@Override
	protected void warn(AyTradeSearchES sample, String message) {
		LOGGER.logWarn(sample.getSellerNick(), sample.getTid(),
			name() + ", sellerId " + sample.getSellerId() + ", " + message);
	}

}
