package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.orders.monitor.dao.dream.PromotionActivityTradeDao;
import cn.loveapp.orders.monitor.entity.PromotionActivityTrade;
import cn.loveapp.orders.monitor.service.PromotionActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 交易-营销活动赠送主表(PromotionActivityTrade)表服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-23 10:42
 */
@Service
public class PromotionActivityServiceImpl implements PromotionActivityService {

	@Autowired
	private PromotionActivityTradeDao promotionActivityTradeDao;

	/**
	 * 获取营销活动赠送信息
	 *
	 * @param sellerNick
	 * @param platformId
	 * @param appName
	 * @return
	 */
	@Override
	public PromotionActivityTrade getPromotionActivityInfo(String sellerNick, String platformId, String appName) {
		return promotionActivityTradeDao.queryBySellerNick(sellerNick);
	}
}
