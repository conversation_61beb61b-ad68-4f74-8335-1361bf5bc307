package cn.loveapp.orders.monitor.controller.operations;

import java.util.ArrayList;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.orders.monitor.constant.operations.ResponseConstant;
import cn.loveapp.orders.monitor.dto.operations.ProblemAnalysisResponse;
import cn.loveapp.orders.monitor.dto.operations.UserProblemAnalysisRequest;
import cn.loveapp.orders.monitor.service.operations.MonitorPlatformProblemAnalysisService;
import cn.loveapp.orders.monitor.utils.MonitorPlatformRequestParamProcessing;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 16:36
 * @Description: 问题分析控制器
 */
@RestController
@RequestMapping(value = "monitor/operations/analysis")
public class ProblemAnalysisController {
    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(ProblemAnalysisController.class);

    @Autowired
    private MonitorPlatformProblemAnalysisService monitorPlatformProblemAnalysisService;

    @Autowired
    private MonitorPlatformRequestParamProcessing monitorPlatformRequestParamProcessing;

    @RequestMapping(value = "/user.problem.analysis", method = RequestMethod.POST)
    public CommonApiResponse<ProblemAnalysisResponse>
        userProblemAnalysis(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isEmpty(request.getSellerNick())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }
        monitorPlatformRequestParamProcessing.examineParams(request);
        // 开始诊断分析用户信息
        ProblemAnalysisResponse problemAnalysisResponse = new ProblemAnalysisResponse();
        problemAnalysisResponse.setParams(new ArrayList<>());
        LOGGER.logInfo(request.getSellerNick(), request.getTid(), "开始分析用户信息");

        monitorPlatformProblemAnalysisService.getUserProblemAnalysisResult(request, problemAnalysisResponse);
        return CommonApiResponse.success(problemAnalysisResponse);
    }

    @RequestMapping(value = "/order.problem.analysis", method = RequestMethod.POST)
    public CommonApiResponse<ProblemAnalysisResponse>
        orderProblemAnalysis(@RequestBody UserProblemAnalysisRequest request) {
        if (StringUtils.isAnyEmpty(request.getSellerNick(), request.getTid())) {
            return CommonApiResponse.failed(ResponseConstant.PARAMSE_RROR.getCode(),
                ResponseConstant.PARAMSE_RROR.getMessage());
        }

        monitorPlatformRequestParamProcessing.examineParams(request);
        if (request.getSellerId() == null) {
            return CommonApiResponse.failed(ResponseConstant.USER_NOT_EXIST.getCode(),
                ResponseConstant.USER_NOT_EXIST.getMessage());
        }

        // 开始诊断分析订单信息
        ProblemAnalysisResponse problemAnalysisResponse = new ProblemAnalysisResponse();
        problemAnalysisResponse.setParams(new ArrayList<>());
        LOGGER.logInfo(request.getSellerNick(), request.getTid(), "开始分析订单");

        monitorPlatformProblemAnalysisService.getOrderProblemAnalysisResult(request, problemAnalysisResponse);
        return CommonApiResponse.success(problemAnalysisResponse);
    }
}
