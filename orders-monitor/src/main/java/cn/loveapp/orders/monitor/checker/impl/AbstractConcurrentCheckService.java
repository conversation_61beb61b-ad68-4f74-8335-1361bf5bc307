package cn.loveapp.orders.monitor.checker.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.exception.UserNeedAuthException;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.api.ApiException;
import org.springframework.context.event.ContextClosedEvent;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发校验
 *
 * <AUTHOR>
 * @date 2018/12/15
 */
public abstract class AbstractConcurrentCheckService<T> extends AbstractMonitorChecker {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractConcurrentCheckService.class);

	protected ThreadPoolExecutor threadPool = null;

	@PostConstruct
	private void init() {
		threadPool =
			new ThreadPoolExecutor(getPoolSize(), getPoolSize(), 2L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000),
				new ThreadFactoryBuilder().setNameFormat(name() + "-pool-%d").build());
		threadPool.allowCoreThreadTimeOut(true);

	}

	protected int getPoolSize() {
		return monitorConfig.getThreadPoolSize();
	}

	/**
	 * 查询指定时间范围内的样本
	 *
	 * @param startTime
	 * @param endTime
	 * @param status
	 * @param listId
	 * @return
	 */
	protected abstract List<T> querySamples(LocalDateTime startTime, LocalDateTime endTime, String status, long listId);

	/**
	 * 校验样本
	 *
	 * @param sample    要校验的样本
	 * @param startTime 校验的起始时间
	 * @param endTime   校验的结束时间
	 * @return
	 * @throws UserNeedAuthException
	 * @throws ApiException
	 */
	protected abstract boolean checkSample(T sample, LocalDateTime startTime, LocalDateTime endTime)
		throws UserNeedAuthException, ApiException;

	/**
	 * 获取样本表数量, 每个表单会单独查询样本
	 *
	 * @return
	 */
	protected abstract int getSampleTableCount();

	/**
	 * 校验指定时间范围之内的样本
	 *
	 * @param timeRange 样本时间范围
	 * @param status    样本状态
	 * @param numberOfSamples    样本数量
	 * @return
	 */
	protected double checkSampleInTimeRange(Duration timeRange, String status, int numberOfSamples) {
		double accuracyRate = 0.0;
		AtomicInteger errorNumber = new AtomicInteger();
		try {
			//到5分钟前结束
			LocalDateTime endTime = LocalDateTime.now().minusMinutes(5);
			LocalDateTime startTime = endTime.minusSeconds(timeRange.getSeconds());
			List<T> list = getNeedCheckSamples(startTime, endTime, status, numberOfSamples);

			if (list.isEmpty()) {
				LOGGER.logWarn(name() + " 在 " + startTime + " - " + endTime + " 之间查询不到数据");
				return accuracyRate;
			} else {
				int shard = list.size() / threadPool.getCorePoolSize();
				shard = shard <= 0 ? 1 : shard;
				List<List<T>> partitions = Lists.partition(list, shard);

				LOGGER.logInfo(
					name() + " 在 " + startTime + " - " + endTime + " 之间查询到总数量: " + list.size() + " 分片: " + shard);

				List<Callable<Void>> callables = Lists.newArrayList();

				setValidNumber(list.size());

				AtomicInteger progress = new AtomicInteger();
				for (List<T> partition : partitions) {
					callables.add(() -> {
						for (T sample : partition) {
							if (isStoped) {
								return null;
							}
							int p = progress.incrementAndGet();
							if (p % 10 == 0 || p == validNumber.intValue()) {
								LOGGER.logInfo(name() + " 当前进度 " + p + "/" + list.size());
							}
							try {
								if (!checkSample(sample, startTime, endTime)) {
									errorNumber.incrementAndGet();
								}
							} catch (UserNeedAuthException e) {
								userNeedAuthNumber.incrementAndGet();
								decrementValidNumber();
							} catch (Exception e) {
								LOGGER.logError(e.getMessage(), e);
								return null;
							}
						}
						return null;
					});
				}

				threadPool.invokeAll(callables);

			}
			if(validNumber.doubleValue() > 0){
				accuracyRate = (validNumber.doubleValue() - errorNumber.intValue()) / validNumber.doubleValue();
			}else{
				accuracyRate = 0.0;
			}

		} catch (Throwable e) {
			LOGGER.logError(name() + ": 正确率校验异常", e);
		}
		return accuracyRate;
	}
	
	/**
	 * 获取需要交验的样本
	 *
	 * @param startTime
	 * @param endTime
	 * @param status
	 * @param numberOfSamples
	 * @return
	 */
	protected List<T> getNeedCheckSamples(LocalDateTime startTime, LocalDateTime endTime, String status,
		int numberOfSamples) {
		numberOfSamples = numberOfSamples <= 0 ? 1 : numberOfSamples;
		List<T> samples = Lists.newArrayList();
		LOGGER.logInfo(name() + " 开始查询订单表...");

		int tableCount = numberOfSamples < getSampleTableCount() ? numberOfSamples : getSampleTableCount();

		if (tableCount == 1) {
			samples = querySamples(startTime, endTime, status, 0);
		} else {
			List<Callable<Collection<T>>> callables = Lists.newArrayList();

			for (int i = 0; i < tableCount; i++) {
				int n = i;
				callables.add(() -> {
					if (isStoped) {
						return Lists.newArrayList();
					}
					if (n % 10 == 0 || n == tableCount - 1) {
						LOGGER.logInfo(name() + " 查询监控数据: " + n + "/" + (tableCount - 1));
					}
					return querySamples(startTime, endTime, status, n);
				});
			}

			try {
				List<Future<Collection<T>>> futures = threadPool.invokeAll(callables);
				for (Future<Collection<T>> future : futures) {
					samples.addAll(future.get());
				}
			} catch (InterruptedException e) {
			} catch (ExecutionException e) {
				LOGGER.logError(name() + ": 正确率校验异常", e);
			}
		}

		if (isStoped) {
			return Lists.newArrayList();
		}

		samples = filterSamples(samples);

		numberOfSamples =
			numberOfSamples <= 0 ? 1 : (numberOfSamples > samples.size() ? samples.size() : numberOfSamples);

		LOGGER.logInfo(name() + " 查询订单结束, 获取样本数量: " + numberOfSamples + " from " + samples.size());
		if (numberOfSamples < samples.size()) {
			//随机获取
			Collections.shuffle(samples);
			return samples.subList(0, numberOfSamples);
		} else {
			return samples;
		}
	}

	/**
	 * 过滤样本集合
	 *
	 * @param allSamples
	 * @return
	 */
	protected List<T> filterSamples(List<T> allSamples) {
		return allSamples;
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event){
		if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
			return;
		}
		super.onApplicationEvent(event);
		threadPool.shutdown();
		try {
			threadPool.awaitTermination(30, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

}
