package cn.loveapp.orders.monitor.dao.dream;


import cn.loveapp.orders.monitor.entity.PromotionActivityTrade;

/**
 * 交易-营销活动赠送主表(PromotionActivityTrade)表数据库访问层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-22 16:41
 */
public interface PromotionActivityTradeDao {

	/**
	 * 通过sellerNick查询单条数据
	 *
	 * @param sellerNick 主键
	 * @return 实例对象
	 */
	PromotionActivityTrade queryBySellerNick(String sellerNick);

}
