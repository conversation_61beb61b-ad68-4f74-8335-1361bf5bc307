package cn.loveapp.orders.monitor.checker;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MonitorCheckerFactory
 *
 * <AUTHOR>
 * @date 2018-12-22
 */
@Component
public class MonitorCheckerFactory {
	@Autowired
	private List<MonitorCheckerInterface> checkers;

	public MonitorCheckerInterface getChecker(CheckerType type) {
		return checkers.stream().filter(c->c.type()==type).limit(1).findFirst().get();
	}
}
