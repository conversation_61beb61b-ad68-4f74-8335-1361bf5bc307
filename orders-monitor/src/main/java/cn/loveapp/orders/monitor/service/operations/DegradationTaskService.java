package cn.loveapp.orders.monitor.service.operations;

import cn.loveapp.orders.monitor.dto.operations.DegradationListResponse;
import cn.loveapp.orders.monitor.entity.operations.DegradationTask;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;

/**
 * 降级任务方案表(DegradationTask)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-29 11:31:41
 */
public interface DegradationTaskService {

    /**
     * 获取所有的任务列表
     *
     * @param
     * @return
     */
    DegradationListResponse pageDegradationTask(String taskName, Pageable pageable);

    /**
     * 通过taskName查询单条任务信息
     *
     * @param name
     * @return
     */
    DegradationTask getDegradationTask(String name);

    /**
     * 新增任务
     *
     * @param degradationTask
     */
    void setDegradationTask(DegradationTask degradationTask);

    /**
     * 修改任务信息
     *
     * @param degradationTask
     * @return
     */
    int updateDegradationTask(DegradationTask degradationTask);

    /**
     * 删除任务
     *
     * @param name
     * @return
     */
    int removeDegradationTask(String name);

    /**
     * 修改定时降级任务时间
     *
     * @param name
     * @param startTime
     * @param endTime
     * @return
     */
    int updateDegradationTimer(String name, LocalDateTime startTime, LocalDateTime endTime);

}
