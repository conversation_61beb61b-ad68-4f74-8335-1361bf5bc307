package cn.loveapp.orders.monitor.entity.operations;

import lombok.Data;

import java.io.Serializable;

/**
 * 降级任务管理员(DegradationTaskAdmin)实体类
 *
 * <AUTHOR>
 * @since 2022-04-15 09:51:22
 */
@Data
public class MonitorPlatformAdmin implements Serializable {
	private static final long serialVersionUID = -55111659637141633L;

	private Integer id;

    /**
     * 用户名
     */
	private String username;

    /**
     * 用户密码
     */
	private String password;

    /**
     * 用户级别
     */
    private Integer userLevel;

    /**
     * 账户是否启用
     */
	private boolean enable;

}

