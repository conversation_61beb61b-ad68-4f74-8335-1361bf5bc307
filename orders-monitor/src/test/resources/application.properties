loveapp.apollo.enabled=false

## æ°æ®æº1éç½®
loveapp.datasources.lacrmDataSource.url = **************************************************************************************************************************************************
loveapp.datasources.lacrmDataSource.username = aiyong
loveapp.datasources.lacrmDataSource.password = aiyong
loveapp.datasources.lacrmDataSource.driverClassName = com.mysql.jdbc.Driver
loveapp.datasources.lacrmDataSource.min-idle = 5
loveapp.datasources.lacrmDataSource.initial-size = 10
loveapp.datasources.lacrmDataSource.max-active = 10
loveapp.datasources.lacrmDataSource.max-wait = 60000
loveapp.datasources.lacrmDataSource.name = lacrmDataSource
## mybatiséç½®
loveapp.mybatis.lacrm.datasource = lacrmDataSource
loveapp.mybatis.lacrm.mapper-package =cn.loveapp.orders.common.dao.dream

mybatis.configuration.mapUnderscoreToCamelCase = true
mybatis.mapper-locations = classpath:mapper/**/*.xml
