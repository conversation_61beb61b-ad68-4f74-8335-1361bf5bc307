package cn.loveapp.orders.monitor.service.impl;

import org.junit.Ignore;
import org.junit.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-20 17:32
 */
public class DemoPasswodeProductionTest {
	@Ignore
	@Test
	public void getTestDegradationPassword(){
		BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
		String degradationPassword = bCryptPasswordEncoder.encode("爱用");
		System.out.println(degradationPassword);
	}
}
