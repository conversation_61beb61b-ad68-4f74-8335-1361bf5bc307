package cn.loveapp.orders.monitor.service.impl;

import cn.loveapp.orders.monitor.dto.FullinfoCheckResult;
import cn.loveapp.orders.monitor.dto.OrderMissCheckResult;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.expression.SPELVariableExpressionEvaluator;
import org.thymeleaf.standard.StandardDialect;
import org.thymeleaf.templateresolver.FileTemplateResolver;

import java.nio.file.Paths;
import java.util.Properties;

public class MonitorServiceImplTest {

	@Ignore
	@Test
	public void sendCheckResultNotify() throws Exception {
		FileTemplateResolver templateResolver = new FileTemplateResolver();
		System.out.println(getClass().getResource("/").getFile());
		templateResolver.setPrefix(
			Paths.get(getClass().getResource("/").getFile(), "/../../src/main/resources/templates/").normalize()
				.toString() + "/");
		templateResolver.setSuffix(".html");

		TemplateEngine templateEngine = new TemplateEngine();
		templateEngine.setTemplateResolver(templateResolver);
		StandardDialect standardDialect = new StandardDialect();
		standardDialect.setVariableExpressionEvaluator(SPELVariableExpressionEvaluator.INSTANCE);
		templateEngine.setDialect(standardDialect);

		FullinfoCheckResult result = new FullinfoCheckResult();
		result.setAccuracyRate(0.9);
		FullinfoCheckResult.FailedOrder failedOrder = new FullinfoCheckResult.FailedOrder();
		failedOrder.setTid("1111");
		failedOrder.setSellerNick("2222");
		failedOrder.setSellerId("asdadasdadasdad");
		failedOrder.addDiffProperty("asdasdad1111", "asdasdadd123123122312313113223");
		failedOrder.addDiffProperty("asdasdad22", "asdasda11231231111111dd");
		failedOrder.addDiffProperty("asdasdad333", "asdas123123dadd");
		failedOrder.addDiffProperty("asdasdad13231312314", "asdasd1231312313132123add");
		failedOrder.addDiffProperty("asdasdad11115", "asdasd11231112333312132313213213213312312313123add");

		FullinfoCheckResult.FailedOrder failedOrder2 = new FullinfoCheckResult.FailedOrder();
		failedOrder2.setTid("3333");
		failedOrder2.setSellerNick("33333");
		failedOrder2.setSellerId("asdadasdadas1dad");
		failedOrder2.addDiffProperty("basdasdad21111", "asdasd");
		failedOrder2.addDiffProperty("basdasda23d22", "asdasda112312");
		failedOrder2.addDiffProperty("baaaaa", "asdas123123dadd");
		failedOrder2.addDiffProperty("basdasdad13231312314", "asdasd1");
		failedOrder2.addDiffProperty("basdasdad11115", "asdasd11231112333312132");
		result.addFailed(failedOrder2);

		OrderMissCheckResult missDescription = new OrderMissCheckResult();
		missDescription.increment(10000L);
		result.setOrderMissCheckResult(missDescription);

		Context context = new Context();
		context.setVariable("result", result);

		JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
		mailSender.setDefaultEncoding("utf-8");
		mailSender.setHost("smtp.mxhichina.com");
		mailSender.setPassword("Ayb1234567");
		mailSender.setUsername("<EMAIL>");
		mailSender.setPort(465);
		mailSender.setProtocol("smtp");
		Properties properties = new Properties();
		properties.setProperty("mail.smtp.connectiontimeout", "10000");
		properties.setProperty("mail.smtp.timeout", "10000");
		properties.setProperty("mail.smtp.writetimeout", "10000");
		properties.setProperty("mail.smtp.auth", "true");
		properties.setProperty("mail.smtp.starttls.enable", "true");
		properties.setProperty("mail.smtp.starttls.required", "true");
		properties.setProperty("mail.smtp.ssl.enable", "true");
		properties.setProperty("mail.smtp.ssl.socketFactory.fallback", "false");
		properties.setProperty("mail.smtp.ssl.socketFactory.class", "com.sun.mail.util.MailSSLSocketFactory");
		mailSender.setJavaMailProperties(properties);

		String html = templateEngine.process("fullinfo", context);
		System.out.println(templateEngine.process("fullinfo", context));

//		MimeMessage message = mailSender.createMimeMessage();
//		MimeMessageHelper helper = new MimeMessageHelper(message);
//		helper.setSubject("订单 fullinfo 校验报告 - " + LocalDateTime.now());
//		helper.setFrom("<EMAIL>");
//		helper.setTo("<EMAIL>");
//		helper.setText(html, true);
//
//		mailSender.send(helper.getMimeMessage());
	}
}
