<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>orders-services-group</artifactId>
		<groupId>cn.loveapp.orders</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<name>订单-监控服务</name>
	<description>爱用基础服务-订单服务-监控告警</description>
	<artifactId>orders-monitor</artifactId>
	<version>1.0-SNAPSHOT</version>

	<properties>
		<orders-common.version>1.0-SNAPSHOT</orders-common.version>
		<javax.mail.version>1.6.2</javax.mail.version>
		<main-class>cn.loveapp.orders.monitor.OrdersMonitorApplication</main-class>
		<easyexcel.version>3.2.1</easyexcel.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-api</artifactId>
			<version>${orders-api.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-common</artifactId>
			<version>${orders-common.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-spring-boot-web-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.hibernate.validator/hibernate-validator -->
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>6.0.16.Final</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-tools</artifactId>
			<version>${rocketmq.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.rholder</groupId>
			<artifactId>guava-retrying</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-openapi</artifactId>
			<version>${apollo.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.2.1</version>
		</dependency>


	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
