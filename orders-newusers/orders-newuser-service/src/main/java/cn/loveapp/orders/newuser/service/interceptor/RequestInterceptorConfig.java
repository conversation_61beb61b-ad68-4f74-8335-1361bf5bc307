package cn.loveapp.orders.newuser.service.interceptor;

import cn.loveapp.orders.newuser.service.web.VersionGrayHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @program: uac-service-group
 * @description: RequestInterceptorConfig
 * @author: <PERSON>
 * @create: 2020-05-26 17:45
 **/
@Configuration
public class RequestInterceptorConfig implements WebMvcConfigurer {

	@Autowired(required = false)
	private VersionGrayHandler versionGrayHandler;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {


		if (versionGrayHandler != null) {
			registry.addInterceptor(versionGrayHandler).addPathPatterns("/**");
		}
	}
}
