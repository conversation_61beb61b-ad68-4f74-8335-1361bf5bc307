package cn.loveapp.orders.newuser.service;

import cn.loveapp.common.utils.LoggerHelper;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;

@EnableScheduling
@EnableFeignClients(basePackages = {"cn.loveapp.schedule.api"})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, RedisRepositoriesAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class,
	DataSourceAutoConfiguration.class, MybatisAutoConfiguration.class,
	RedisReactiveAutoConfiguration.class},scanBasePackages = {"cn.loveapp.orders.common","cn.loveapp.orders.newuser.common","cn.loveapp.orders.newuser.service"})
public class OrdersNewuserServiceApplication {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersNewuserServiceApplication.class);
	private static final String APOLLO_ENV = "env";

	public static void main(String[] args) {
		new SpringApplicationBuilder(OrdersNewuserServiceApplication.class)
			.initializers((ConfigurableApplicationContext applicationContext) -> {
				//初始化apollo的env配置
				if (StringUtils.isEmpty(System.getProperty(APOLLO_ENV))) {
					String env = applicationContext.getEnvironment().getProperty(APOLLO_ENV);
					if (!StringUtils.isEmpty(env)) {
						System.setProperty(APOLLO_ENV, env);
					}
				}
			}).application().run(args);
	}

}
