package cn.loveapp.orders.newuser.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.common.bo.UserInfoBo;
import cn.loveapp.orders.common.entity.TargetSellerInfo;
import cn.loveapp.orders.common.service.UserService;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.newuser.common.dto.SaveOrderCourseDTO;
import cn.loveapp.orders.newuser.common.dto.SaveOrderDTO;
import cn.loveapp.orders.newuser.common.dto.UserInfoDTO;
import cn.loveapp.orders.newuser.common.service.PrepareSaveOrderService;
import cn.loveapp.orders.newuser.common.service.UserSaveOrderService;
import cn.loveapp.orders.newuser.common.utils.SessionValidateUtil;
import cn.loveapp.orders.newuser.service.request.BatchSaveOrderCourseRequest;
import cn.loveapp.orders.newuser.service.response.BatchSaveOrderCourseResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

/**
 * 新用户接口
 *
 * <AUTHOR> Shuaifei
 * @email  <EMAIL>
 * @create 2019-08-07 下午3:31
 */
@RestController
@RequestMapping("newuser")
public class NewuserController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NewuserController.class);
	@Autowired
	private SessionValidateUtil sessionValidateUtil;

	@Autowired
	private UserSaveOrderService userSaveOrderService;

	@Autowired
	private PrepareSaveOrderService prepareSaveOrderService;

    @Autowired
    private UserService userService;

	@RequestMapping("/saveOrderCourse")
	public Object saveOrderCourse(HttpServletRequest request, TargetSellerInfo targetSellerInfo){
		CommonApiResponse<UserInfoDTO> userInfoDTOCommonApiResponse = checkSessionInfo(request);
		if (!userInfoDTOCommonApiResponse.isSuccess()) {
			return userInfoDTOCommonApiResponse;
		}
        UserInfoDTO session;
        if (targetSellerInfo != null && StringUtils.isNoneEmpty(targetSellerInfo.getTargetStoreId(), targetSellerInfo.getTargetNick())) {
            UserInfoBo targetUser = userService.getSellerInfoBySellerNick(targetSellerInfo.getTargetNick(), targetSellerInfo.getTargetStoreId(), targetSellerInfo.getTargetAppName());
            session = new UserInfoDTO();
            session.setNick(targetUser.getSellerNick());
            session.setSellerId(targetUser.getSellerId());
            session.setAppName(targetSellerInfo.getTargetAppName());
            session.setVipFlag(String.valueOf(targetUser.getVipFlag()));
            session.setStoreId(targetUser.getStoreId());
            session.setCorpId(targetUser.getCorpId());
        } else {
            session = userInfoDTOCommonApiResponse.getBody();
        }
		SaveOrderCourseDTO saveOrderCourseDTO = userSaveOrderService.getPullOrderProgress(session);

		if (saveOrderCourseDTO.getCurrentCount() == null || saveOrderCourseDTO.getSumCount() == null){
			LOGGER.logInfo(session.getNick(),"-","获取进度失败");
			return CommonApiResponse.of(CommonApiStatus.ServerError.code(),"获取进度失败,请稍后再试");
		}

		if (saveOrderCourseDTO.getSumCount() < saveOrderCourseDTO.getCurrentCount()){
			LOGGER.logInfo(session.getNick(),"-","拉取订单的数量比近三个月的还多");
			saveOrderCourseDTO.setSumCount(saveOrderCourseDTO.getCurrentCount());
		}
		return CommonApiResponse.success(saveOrderCourseDTO);
	}

    @RequestMapping("/batchSaveOrderCourse")
    public CommonApiResponse<BatchSaveOrderCourseResponse> batchSaveOrderCourse(HttpServletRequest request,
        BatchSaveOrderCourseRequest batchSaveOrderCourseRequest) {
        CommonApiResponse<UserInfoDTO> userInfoDTOCommonApiResponse = checkSessionInfo(request);
        UserInfoDTO userInfoDTO = userInfoDTOCommonApiResponse.getBody();
        if (!userInfoDTOCommonApiResponse.isSuccess()) {
            return CommonApiResponse.failed(userInfoDTOCommonApiResponse.getCode(),
                userInfoDTOCommonApiResponse.getMessage());
        }

        BatchSaveOrderCourseResponse batchSaveOrderCourseResponse = new BatchSaveOrderCourseResponse();
        if (!Objects.isNull(batchSaveOrderCourseRequest)) {
            if (CollectionUtils.isEmpty(batchSaveOrderCourseRequest.getTargetSellerInfoList())) {
                TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
                targetSellerInfo.setTargetNick(userInfoDTO.getNick());
                targetSellerInfo.setTargetSellerId(userInfoDTO.getSellerId());
                targetSellerInfo.setTargetCorpId(userInfoDTO.getCorpId());
                targetSellerInfo.setTargetStoreId(userInfoDTO.getStoreId());
                targetSellerInfo.setTargetAppName(userInfoDTO.getAppName());
                targetSellerInfo.setTargetVipFlag(Integer.valueOf(userInfoDTO.getVipFlag()));
                batchSaveOrderCourseRequest.setTargetSellerInfoList(Lists.newArrayList(targetSellerInfo));
            }

            List<SaveOrderCourseDTO> saveOrderCourseDTOList = Lists.newArrayList();
            for (TargetSellerInfo targetSellerInfo : batchSaveOrderCourseRequest.getTargetSellerInfoList()) {
                if (StringUtils.isNoneEmpty(targetSellerInfo.getTargetStoreId(), targetSellerInfo.getTargetNick())) {
                    UserInfoBo targetUser = userService.getSellerInfoBySellerNick(targetSellerInfo.getTargetNick(),
                        targetSellerInfo.getTargetStoreId(), targetSellerInfo.getTargetAppName());
                    UserInfoDTO session = new UserInfoDTO();
                    session.setNick(targetUser.getSellerNick());
                    session.setSellerId(targetUser.getSellerId());
                    session.setAppName(targetSellerInfo.getTargetAppName());
                    session.setVipFlag(String.valueOf(targetUser.getVipFlag()));
                    session.setStoreId(targetUser.getStoreId());
                    session.setCorpId(targetUser.getCorpId());

                    SaveOrderCourseDTO saveOrderCourseDTO = userSaveOrderService.getPullOrderProgress(session);
                    saveOrderCourseDTO.setSellerId(targetUser.getSellerId());
                    saveOrderCourseDTO.setSellerNick(targetUser.getSellerNick());
                    saveOrderCourseDTO.setStoreId(targetUser.getStoreId());
                    saveOrderCourseDTO.setAppName(targetSellerInfo.getTargetAppName());
                    if (saveOrderCourseDTO.getCurrentCount() == null || saveOrderCourseDTO.getSumCount() == null) {
                        LOGGER.logInfo(session.getNick(), "-", "获取进度失败");
                        saveOrderCourseDTO.setFailReason("获取进度失败,请稍后再试");
                    }

                    if (saveOrderCourseDTO.getSumCount() < saveOrderCourseDTO.getCurrentCount()) {
                        LOGGER.logInfo(session.getNick(), "-", "拉取订单的数量比近三个月的还多");
                        saveOrderCourseDTO.setSumCount(saveOrderCourseDTO.getCurrentCount());
                    }

                    saveOrderCourseDTOList.add(saveOrderCourseDTO);
                }
            }

            batchSaveOrderCourseResponse.setSaveOrderCourseDTOList(saveOrderCourseDTOList);
        }

        return CommonApiResponse.success(batchSaveOrderCourseResponse);
    }

	/**
	 * 准备给用户开通存单接口, 设置为待
	 * @return 成功与失败
	 */
	@RequestMapping("/prepare")
	public Object prepare(SaveOrderDTO saveOrderDTO) throws UnsupportedEncodingException {

		//调用预存单服务
		if (saveOrderDTO == null || StringUtils.isAnyEmpty(saveOrderDTO.getSellerNick())){
			LOGGER.logInfo("-","-","参数异常");
			return CommonApiResponse.of(400,"参数异常");
		}
		saveOrderDTO.setPlatform(OrderUtil.defaultPlatformId(saveOrderDTO.getPlatform()));
		saveOrderDTO.setServiceName(OrderUtil.defaultAppName(saveOrderDTO.getPlatform(), saveOrderDTO.getServiceName()));

		boolean result = prepareSaveOrderService.prepareSaveOrder(saveOrderDTO);

		return CommonApiResponse.of(200,result?"提交成功":"提交失败",result);
	}




	/**
	 * 通用校验session
	 * @param request
	 * @return
	 */
	private CommonApiResponse<UserInfoDTO> checkSessionInfo(HttpServletRequest request) {
		UserInfoDTO session = sessionValidateUtil.checkSessionInfo(request,LOGGER);
		if(CommonApiStatus.ServerError.code() == session.getCode()) {
			LOGGER.logInfo(session.getNick(),"-","校验身份失败的原因:"+session.getMsg());
			return CommonApiResponse.failed(CommonApiStatus.ServerError.code(),session.getMsg());
		}
		LOGGER.logInfo("-","session中信息",session.toString());
		return CommonApiResponse.success(session);
	}




}
