package cn.loveapp.orders.newuser.service.export;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.newuser.common.dto.UserCloseCheckTaskDTO;
import cn.loveapp.orders.newuser.common.service.UserSaveOrderService;
import cn.loveapp.schedule.api.request.CallbackRequest;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;

/**
 * 订单用户内部接口
 *
 * <AUTHOR>
 * @Date 2025/5/8 15:39
 */
@RestController
@RequestMapping("export/newuser")
public class UserNewuserApiService {

    @Autowired
    private UserSaveOrderService userSaveOrderService;

    /**
     * 离网调度任务回调
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/close.check.callback")
    public Object closeCheckCallBack(@RequestBody CallbackRequest request) throws UnsupportedEncodingException {
        if (request == null || StringUtils.isEmpty(request.getData())) {
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), "参数异常");
        }
        UserCloseCheckTaskDTO userCloseCheckTaskDTO = null;
        try {
            userCloseCheckTaskDTO = JSON.parseObject(request.getData(), UserCloseCheckTaskDTO.class);
        } catch (Exception e) {
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), "参数格式化异常");
        }

        userSaveOrderService.closeCheck(userCloseCheckTaskDTO);

        return CommonApiResponse.success();
    }
}
