spring.application.name=orders-newuser-service
spring.profiles.active=dev

loveapp.apollo.enabled=true
dubbo.enabled=false
## apollo
app.id=cn.loveapp.trade
apollo.bootstrap.enabled = ${loveapp.apollo.enabled}
apollo.bootstrap.namespaces=newuser-consumer,application,service-registry

env=${spring.profiles.active}

management.server.port=8455
management.metrics.tags.application=${spring.application.name}
management.metrics.export.prometheus.enabled=true
management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=prometheus
management.metrics.export.logging.enabled = false
management.metrics.export.logging.step = 15s

loveapp.mybatis.newuserDream.mapper-package = cn.loveapp.orders.newuser.common.dao.dream
loveapp.mybatis.newuserDream.datasource = dreamDataSource

spring.task.scheduling.pool.size=20

