package cn.loveapp.orders.newuser.service.controller;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.orders.newuser.common.dto.SaveOrderDTO;
import cn.loveapp.orders.newuser.common.service.PrepareSaveOrderService;
import cn.loveapp.orders.newuser.common.service.UserSaveOrderService;
import cn.loveapp.orders.newuser.common.utils.SessionValidateUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.nio.charset.StandardCharsets;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@RunWith(SpringRunner.class)
@WebMvcTest({NewuserController.class, PrepareSaveOrderService.class})
public class NewuserControllerTest {

	@MockBean
	private SessionValidateUtil sessionValidateUtil;

	@MockBean
	private UserSaveOrderService userSaveOrderService;

	@MockBean
	private PrepareSaveOrderService prepareSaveOrderService;

	@Autowired
	private MockMvc mockMvc;

	private final String sellerNick = "张三";

	MultiValueMap<String,String> params = new LinkedMultiValueMap<String,String>(){{
		set("sellerNick","张三");
		set("platform","TAO");
		set("serviceName",CommonAppConstants.APP_GUANDIAN);
	}};

	@Test
	public void prepareSuccess() throws Exception {
		SaveOrderDTO saveOrderDTO = new SaveOrderDTO();
		saveOrderDTO.setSellerNick(sellerNick);
		saveOrderDTO.setPlatform(CommonPlatformConstants.PLATFORM_TAO);
		saveOrderDTO.setServiceName(CommonAppConstants.APP_GUANDIAN);
		when(prepareSaveOrderService.prepareSaveOrder(saveOrderDTO)).thenReturn(true);
		MvcResult mvcResult = mockMvc.perform(post("/newuser/prepare")
			.params(params))
			.andExpect(status().isOk())
			.andReturn();

		Assert.assertTrue(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8).contains(""));
	}

	@Test
	public void prepareFailed() throws Exception {
		SaveOrderDTO saveOrderDTO = new SaveOrderDTO();
		saveOrderDTO.setSellerNick(sellerNick);
		saveOrderDTO.setPlatform(CommonPlatformConstants.PLATFORM_TAO);
		saveOrderDTO.setServiceName(CommonAppConstants.APP_GUANDIAN);
		when(prepareSaveOrderService.prepareSaveOrder(saveOrderDTO)).thenReturn(false);
		MvcResult mvcResult = mockMvc.perform(post("/newuser/prepare")
			.params(params))
			.andExpect(status().isOk())
			.andReturn();

		Assert.assertTrue(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8).contains("提交失败"));
	}

	@Test
	public void prepareParamError() throws Exception {
		SaveOrderDTO saveOrderDTO = new SaveOrderDTO();
		saveOrderDTO.setSellerNick(sellerNick);
		saveOrderDTO.setPlatform(CommonPlatformConstants.PLATFORM_TAO);
		saveOrderDTO.setServiceName(CommonAppConstants.APP_GUANDIAN);
		when(prepareSaveOrderService.prepareSaveOrder(saveOrderDTO)).thenReturn(false);
		MvcResult mvcResult = mockMvc.perform(post("/newuser/prepare"))
			.andExpect(status().isOk())
			.andReturn();

		Assert.assertTrue(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8).contains("参数异常"));
		Assert.assertTrue(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8).contains("400"));
	}
}
