<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>orders-newusers</artifactId>
		<groupId>cn.loveapp.orders</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<name>订单-新用户-API服务</name>
	<description>订单-新用户-API服务</description>
	<artifactId>orders-newuser-service</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<properties>
		<orders-newuser-common.version>0.0.1-SNAPSHOT</orders-newuser-common.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.orders</groupId>
			<artifactId>orders-newuser-common</artifactId>
			<version>${orders-newuser-common.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
