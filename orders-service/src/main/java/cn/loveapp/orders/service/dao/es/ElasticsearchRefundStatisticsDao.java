package cn.loveapp.orders.service.dao.es;

import static org.elasticsearch.index.query.QueryBuilders.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.orders.common.config.elasticsearch.ElasticsearchConfiguration;
import cn.loveapp.orders.common.constant.*;
import cn.loveapp.orders.common.entity.AyRefundSearchES;
import cn.loveapp.orders.common.entity.TargetSellerInfo;
import cn.loveapp.orders.common.utils.CurrencyUtils;
import cn.loveapp.orders.common.utils.DateUtil;
import cn.loveapp.orders.common.utils.MathUtil;
import cn.loveapp.orders.common.utils.OrderUtil;
import cn.loveapp.orders.service.config.ServiceConfig;
import cn.loveapp.orders.service.config.service.AiyongTradeApiServiceConfig;
import cn.loveapp.orders.service.constant.ReportFormDataStaticsTagEnum;
import cn.loveapp.orders.service.dto.*;
import cn.loveapp.orders.service.enums.AySearchTypeEnum;
import cn.loveapp.orders.service.request.DatacenterRequest;
import cn.loveapp.orders.service.response.DatacenterResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.*;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedReverseNested;
import org.elasticsearch.search.aggregations.bucket.nested.ReverseNestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import cn.loveapp.orders.common.constant.AyStatusConstant;
import cn.loveapp.orders.common.constant.EsRefundFields;
import cn.loveapp.orders.common.constant.TaobaoStatusConstant;
import cn.loveapp.orders.service.dto.UserInfoDTO;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * ES-数据统计dao
 * <AUTHOR>
 * @Since 2020/7/22 12:01
 */
@Repository
public class ElasticsearchRefundStatisticsDao extends AbstractElasticsearchRefundServiceDao {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchRefundStatisticsDao.class);

    private static final String COUNT_TERMS = "countTerms";

    private static final String REFUND_INFO = "refundInfo";

    private static final String[] TOP_HIT_FIELDS = new String[]{EsRefundFields.oid, EsRefundFields.tid};

    @Autowired
    private ServiceConfig serviceConfig;

    @Autowired
    private AiyongTradeApiServiceConfig aiyongTradeApiServiceConfig;

    public ElasticsearchRefundStatisticsDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations, RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }


    /**
     * 统计订单商品排行
     * 支持多店
     *
     * @param request
     * @param userInfoDTO
     */
    public List<DatacenterResponse.GoodsRank> countRefundGoodsRank(DatacenterRequest request, UserInfoDTO userInfoDTO, BoolQueryBuilder refundGoodsRankCondition) {
        //订单商品排行
        BoolQueryBuilder queryCondition = getRefundGoodsRankCondition(request, refundGoodsRankCondition);
        NativeSearchQueryBuilder builder = aiyongListGetCommonBuilder(userInfoDTO, null);
        builder.withQuery(filterQuery(queryCondition));

        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms(COUNT_TERMS)
            .size(serviceConfig.getDatacenterCalcRankMax())
            .order(BucketOrder.aggregation(request.getSortBy().name(), request.getSort().val))
            .subAggregation(AggregationBuilders.topHits(REFUND_INFO).size(1).fetchSource(TOP_HIT_FIELDS, null));

        for (DatacenterRequest.StatisticalItem statisticalItem : request.getStatisticalItemList()) {
            switch (statisticalItem) {
                case REFUND_COUNT:
                    if (DatacenterRequest.StatisticalItem.REFUND_COUNT.equals(request.getSortBy())) {
                        aggBuilder.order(BucketOrder.aggregation(DOC_COUNT, request.getSort().val));
                    }
                    break;
                case REFUND_AMOUNT:
                    aggBuilder.subAggregation(AggregationBuilders.sum(statisticalItem.name()).field(EsRefundFields.refundFee));
            }
        }

        aggBuilder.field(request.getDimension().getRefundAggField());

        Aggregations aggs = aggs(builder.build(), aggBuilder);
        List<DatacenterResponse.GoodsRank> goodsRankList = new ArrayList<>();
        Terms aggregation = aggs.get(COUNT_TERMS);
        for (Terms.Bucket bucket : aggregation.getBuckets()) {
            DatacenterResponse.GoodsRank goodsRank = new DatacenterResponse.GoodsRank();

            goodsRank.setSoldCount(bucket.getDocCount());

            ParsedSum sumAggregation = bucket.getAggregations().get(DatacenterRequest.StatisticalItem.REFUND_AMOUNT.name());
            if (sumAggregation != null) {
                goodsRank.setSoldAmount(new BigDecimal(sumAggregation.getValue() + "").divide(new BigDecimal(OrderUtil.YUAN_FEN_MONEY_CONVER_RATE)));
            }
            Aggregation refundInfo = bucket.getAggregations().get(REFUND_INFO);
            SearchHit hit = ((ParsedTopHits) refundInfo).getHits().getHits()[0];
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            if (sourceAsMap != null) {
                Object tid = sourceAsMap.get(EsRefundFields.tid);
                goodsRank.setTid(Objects.toString(tid, null));

                Object oid = sourceAsMap.get(EsRefundFields.oid);
                goodsRank.setOid(Objects.toString(oid, null));
            }
            goodsRankList.add(goodsRank);
        }
        return goodsRankList;
    }


    /**
     * 获取商品排行 - 售后单查询条件builder
     * @param request
     * @param queryCondition
     */
    private BoolQueryBuilder getRefundGoodsRankCondition(DatacenterRequest request, BoolQueryBuilder queryCondition){
        queryCondition.mustNot(existsQuery(EsRefundFields.ayRefundOrderType));
        //必须是退款成功、退款金额大于0的
        queryCondition.must(termsQuery(EsRefundFields.status, TaobaoStatusConstant.REFUND_SUCCESS))
            .must(rangeQuery(EsRefundFields.refundFee).gt(0));
        if (request.getStartTime() != null && request.getEndTime() != null) {
            RangeQueryBuilder timeRange = rangeQuery(EsRefundFields.created)
                .gte(request.getStartTime().format(minuteSecondFormatter))
                .lt(request.getEndTime().format(minuteSecondFormatter));
            queryCondition.must(timeRange);
        }

        if (request.getOrderStatus() != null) {
            switch (request.getOrderStatus()) {
                case WAIT_SELLER_SEND_GOODS:
                    queryCondition.must(termQuery(EsRefundFields.orderStatus, AyStatusConstant.WAIT_SELLER_SEND_GOODS));
                    break;
            }
        }

        if (request.getRefundStatus() != null) {
            switch (request.getRefundStatus()) {
                case NO_REFUND:
                    LOGGER.logWarn("在售后单中查询无售后的数据");
                    break;
            }
        }

        return queryCondition;
    }


    /**
     * 通过订单的数据统计结果进行退款数据报表统计(erp)
     *
     * @param reportFormDataStatisticsDTO
     * @param userInfoDTO
     * @param groupValueList
     */
    public StatisticsReportFormRefundDataResult statisticsReportFormDataByOrderStatisticsResult(ReportFormDataStatisticsDTO reportFormDataStatisticsDTO,
        UserInfoDTO userInfoDTO, List<String> groupValueList) {
        NativeSearchQueryBuilder builder =
            aiyongListGetCommonBuilder(userInfoDTO, new String[] {EsRefundFields.refundId});

        BoolQueryBuilder boolQueryBuilder =
            generateReportFormStatisticsQueryBuilder(reportFormDataStatisticsDTO, userInfoDTO);
        builder.withQuery(filterQuery(boolQueryBuilder));

        // 1.先通过条件查询数据总数(订单数)
        long total =
            count(AyRefundSearchES.of(userInfoDTO.getSellerId(), userInfoDTO.getStoreId(), userInfoDTO.getAppName()),
                userInfoDTO.getTargetSellerList(), builder.build());
        if (total == 0) {
            return null;
        }

        // 控制是否排除仅退款类型
        Boolean isRefundExcludedOnlyRefund = reportFormDataStatisticsDTO.getIsRefundExcludedOnlyRefund();

        // 构建主聚合的过滤条件（动态判断是否排除 REFUND_ONLY）
        BoolQueryBuilder mainFilter = QueryBuilders.boolQuery();
        if (BooleanUtils.isTrue(isRefundExcludedOnlyRefund)) {
            mainFilter.mustNot(termQuery(EsRefundFields.afterSaleType, RefundSaleTypeConstant.REFUND_ONLY));
        }

        // 1. 退款总金额
        SumAggregationBuilder refundAmountSumAggBuilder =
            AggregationBuilders.sum("refundAmountSum").field(EsRefundFields.refundFee);
        // 2. 订单数
        CardinalityAggregationBuilder refundOrderCountAggBuilder =
            AggregationBuilders.cardinality("orderCount").field(EsRefundFields.tidKeyword);

        // 3.商品件数
        SumAggregationBuilder refundedItemsNumSumAggBuilder =
            AggregationBuilders.sum("refundedItemsNumSum").field(EsRefundFields.num);

        // 4.货品成本
        SumAggregationBuilder warehouseCostSumAggBuilder =
            AggregationBuilders.sum("warehouseCostSum").field(EsRefundFields.refundItemInfosWarehouseCost);
        SumAggregationBuilder warehouseCostLiSumAggBuilder =
            AggregationBuilders.sum("warehouseCostLiSum").field(EsRefundFields.refundItemInfosWarehouseCostLi);
        NestedAggregationBuilder refundItemInfoAgg =
            AggregationBuilders.nested("refundItemInfoAgg", EsRefundFields.refundItemInfos)
                .subAggregation(warehouseCostSumAggBuilder).subAggregation(warehouseCostLiSumAggBuilder);


        // 总数统计（退款金额、订单数、商品件数）
        FilterAggregationBuilder mainAggBuilder = AggregationBuilders.filter("refundMainAgg", mainFilter)
            // 退款金额总和
            .subAggregation(refundAmountSumAggBuilder)
            // 订单数（去重统计）
            .subAggregation(refundOrderCountAggBuilder)
            // 商品件数总和
            .subAggregation(refundedItemsNumSumAggBuilder)
            // 货品成本
            .subAggregation(refundItemInfoAgg);


        // 4.未发货仅退款
        FilterAggregationBuilder notSendGoodOnlyRefundAggBuilder = AggregationBuilders
            .filter("notSendGoodOnlyRefund",
                boolQuery().must(termQuery(EsRefundFields.afterSaleType, RefundSaleTypeConstant.REFUND_ONLY)))
            // 退款金额
            .subAggregation(AggregationBuilders.sum("notSendGoodOnlyRefundAmountSum").field(EsRefundFields.refundFee))
            // 退款件数
            .subAggregation(AggregationBuilders.sum("notSendGoodOnlyRefundItemsNumSum").field(EsRefundFields.num))
            // 订单数
            .subAggregation(refundOrderCountAggBuilder);

        // 5.退货退款
        FilterAggregationBuilder returnGoodAndRefundAggBuilder = AggregationBuilders
            .filter("returnGoodAndRefund",
                boolQuery().must(termQuery(EsRefundFields.afterSaleType, RefundSaleTypeConstant.RETURN_GOOD_AND_REFUND)))
            // 退款金额
            .subAggregation(AggregationBuilders.sum("returnGoodAndRefundAmountSum").field(EsRefundFields.refundFee))
            // 退款件数
            .subAggregation(AggregationBuilders.sum("returnGoodAndRefundItemsNumSum").field(EsRefundFields.num))
            // 订单数
            .subAggregation(refundOrderCountAggBuilder);

        StatisticsReportFormRefundDataResult refundDataResult = new StatisticsReportFormRefundDataResult();
        ReportFormDataStaticsTagEnum tag = reportFormDataStatisticsDTO.getTag();
        switch (tag) {
            case dataOverview:
                getReportFormDataOverview(refundDataResult, builder, reportFormDataStatisticsDTO, userInfoDTO,
                    mainAggBuilder, notSendGoodOnlyRefundAggBuilder, returnGoodAndRefundAggBuilder);
                break;
            case storeId:
                refundDataResult.setStatisticsDataList(groupAgg(EsRefundFields.storeId, builder, total, mainAggBuilder,
                    notSendGoodOnlyRefundAggBuilder, returnGoodAndRefundAggBuilder));
                break;
            case sellerId:
                refundDataResult.setStatisticsDataList(groupAgg(EsRefundFields.sellerId, builder, total, mainAggBuilder,
                    notSendGoodOnlyRefundAggBuilder, returnGoodAndRefundAggBuilder));
                break;
            case refundReason:
                refundDataResult.setStatisticsDataList(groupAgg(EsRefundFields.reasonKeyword, builder, total,
                    mainAggBuilder, notSendGoodOnlyRefundAggBuilder, returnGoodAndRefundAggBuilder));
                break;
            case numIid:
                if (CollectionUtils.isEmpty(groupValueList)) {
                    return null;
                }

                boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos,
                    boolQuery().must(termsQuery(EsRefundFields.refundItemInfosNumIid, groupValueList)),
                    ScoreMode.None));
                builder.withQuery(filterQuery(boolQueryBuilder));
                refundDataResult.setStatisticsDataList(
                    refundItemInfoFieldGroupAgg(EsRefundFields.refundItemInfosNumIid, builder, mainFilter, total));
                break;
            case skuId:
                if (CollectionUtils.isEmpty(groupValueList)) {
                    return null;
                }

                boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos,
                    boolQuery().must(termsQuery(EsRefundFields.refundItemInfosSkuId, groupValueList)), ScoreMode.None));
                builder.withQuery(filterQuery(boolQueryBuilder));

                refundDataResult.setStatisticsDataList(
                    refundItemInfoFieldGroupAgg(EsRefundFields.refundItemInfosSkuId, builder, mainFilter, total));
                break;
            case warehouseSpuId:
                if (CollectionUtils.isEmpty(groupValueList)) {
                    return null;
                }

                boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos,
                    boolQuery().must(termsQuery(EsRefundFields.refundItemInfosWarehouseSpuId, groupValueList)),
                    ScoreMode.None));
                builder.withQuery(filterQuery(boolQueryBuilder));

                refundDataResult.setStatisticsDataList(
                    refundItemInfoFieldGroupAgg(EsRefundFields.refundItemInfosWarehouseSpuId, builder, mainFilter,
                        total));
                break;
            case warehouseSkuId:
                if (CollectionUtils.isEmpty(groupValueList)) {
                    return null;
                }

                boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos,
                    boolQuery().must(termsQuery(EsRefundFields.refundItemInfosWarehouseSkuId, groupValueList)),
                    ScoreMode.None));
                builder.withQuery(filterQuery(boolQueryBuilder));

                refundDataResult.setStatisticsDataList(
                    refundItemInfoFieldGroupAgg(EsRefundFields.refundItemInfosWarehouseSkuId, builder, mainFilter,
                        total));
                break;
            default:
                return null;
        }


        return refundDataResult;
    }


    /**
     * 生成数据报表统计查询builder
     *
     * @param reportFormDataStatisticsDTO
     * @param userInfoDTO
     * @return
     */
    private BoolQueryBuilder generateReportFormStatisticsQueryBuilder(
        ReportFormDataStatisticsDTO reportFormDataStatisticsDTO, UserInfoDTO userInfoDTO) {
        BoolQueryBuilder boolQueryBuilder = boolQuery();
        boolQueryBuilder.mustNot(termsQuery(EsRefundFields.isDeleted, true))
            .mustNot(existsQuery(EsRefundFields.ayRefundOrderType));
        // 处理下用户信息
        handleUserSearchInfo(boolQueryBuilder, userInfoDTO);
        // 统计维度为退款成功
        boolQueryBuilder.must(termQuery(EsRefundFields.status, TaobaoStatusConstant.REFUND_SUCCESS));
        // 处理前段传的搜索条件
        if (reportFormDataStatisticsDTO.getDataOverviewStartTime() != null) {
            // 数据概览的统计时间不为null时使用数据概览的统计时间
            boolQueryBuilder.must(rangeQuery(EsRefundFields.endTime)
                .gte(minuteSecondFormatter.format(reportFormDataStatisticsDTO.getDataOverviewStartTime()))
                .lte(minuteSecondFormatter.format(reportFormDataStatisticsDTO.getEndTime())));
        } else {
            boolQueryBuilder.must(rangeQuery(EsRefundFields.endTime)
                .gte(minuteSecondFormatter.format(reportFormDataStatisticsDTO.getStartTime()))
                .lte(minuteSecondFormatter.format(reportFormDataStatisticsDTO.getEndTime())));
        }

        // 订单来源
        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getOrderSource())) {
            boolQueryBuilder
                .must(termQuery(EsRefundFields.orderSource, reportFormDataStatisticsDTO.getOrderSource()));
        }

        // 订单状态
        if (reportFormDataStatisticsDTO.getOrderStatus() != null) {
            boolQueryBuilder
                .must(termQuery(EsRefundFields.orderStatus, reportFormDataStatisticsDTO.getOrderStatus()));
        }

        // 订单号
        if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getTidList())) {
            boolQueryBuilder.must(termsQuery(EsRefundFields.tid, reportFormDataStatisticsDTO.getTidList()));
        }

        // 卖家备注
        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getSellerMemo())) {
            boolQueryBuilder.must(matchPhraseQuery(EsRefundFields.orderSellerMemo,
                splitAlphanumeric(reportFormDataStatisticsDTO.getSellerMemo())));
        }

        appendItemInfoSearch(reportFormDataStatisticsDTO, boolQueryBuilder);
        appendFlagSearch(reportFormDataStatisticsDTO, boolQueryBuilder);
        return boolQueryBuilder;
    }

    /**
     * 追加旗帜搜索
     *
     * @param reportFormDataStatisticsDTO
     * @param boolQueryBuilder
     */
    private void appendFlagSearch(ReportFormDataStatisticsDTO reportFormDataStatisticsDTO,
        BoolQueryBuilder boolQueryBuilder) {
        AySearchTypeEnum flagSearchType = reportFormDataStatisticsDTO.getFlagSearchType();
        BoolQueryBuilder fuzzyQueryBuilder = null;
        if (flagSearchType != null) {
            switch (flagSearchType) {
                case FUZZY_SEARCH:
                    // 模糊搜索
                    fuzzyQueryBuilder = handleReportFormFlagFuzzySearch(reportFormDataStatisticsDTO);
                    boolQueryBuilder.must(fuzzyQueryBuilder);
                    break;
                case ACCURATE_SEARCH:
                    // 精确搜索
                    if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getOrderSellerFlagList())) {
                        boolQueryBuilder.must(termsQuery(EsRefundFields.orderSellerFlag,
                            reportFormDataStatisticsDTO.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList())) {
                        boolQueryBuilder.must(termsQuery(EsRefundFields.ayCustomOrderFlag,
                            reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList()));
                    }
                    break;
                case REVERSE_FUZZY_SEARCH:
                    fuzzyQueryBuilder = handleReportFormFlagFuzzySearch(reportFormDataStatisticsDTO);
                    boolQueryBuilder.mustNot(fuzzyQueryBuilder);
                    break;
            }
        }
    }

    /**
     * 追加商品信息搜索
     *
     * @param reportFormDataStatisticsDTO
     * @param boolQueryBuilder
     */
    private void appendItemInfoSearch(ReportFormDataStatisticsDTO reportFormDataStatisticsDTO, BoolQueryBuilder boolQueryBuilder) {
        AySearchTypeEnum itemInfoSearchType = reportFormDataStatisticsDTO.getItemInfoSearchType();
        if (itemInfoSearchType != null) {
            switch (itemInfoSearchType) {
                case FUZZY_SEARCH:
                    // 模糊搜索
                    handleReportFormItemInfoFuzzySearch(boolQueryBuilder, reportFormDataStatisticsDTO, false);
                    break;
                case ACCURATE_SEARCH:
                    // 精确搜索
                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getSpuName())) {
                        boolQueryBuilder.must(termsQuery(EsRefundFields.title,
                            splitAlphanumeric(reportFormDataStatisticsDTO.getSpuName())));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getOuterSpuId())) {
                        boolQueryBuilder
                            .must(termsQuery(EsRefundFields.outerId, splitAlphanumeric(reportFormDataStatisticsDTO.getOuterSpuId())));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getSkuName())) {
                        boolQueryBuilder
                            .must(termsQuery(EsRefundFields.skuAttributives, reportFormDataStatisticsDTO.getSkuName()));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getOuterSkuId())) {
                        boolQueryBuilder
                            .must(termsQuery(EsRefundFields.skuOuterIds, splitAlphanumeric(reportFormDataStatisticsDTO.getOuterSkuId())));
                    }

                    // 货品
                    BoolQueryBuilder refundItemInfosBoolQueryBuilder = boolQuery();
                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseSpuName())) {
                        refundItemInfosBoolQueryBuilder.must(termsQuery(EsRefundFields.refundItemInfosWarehouseSpuTitleKeyword,
                            splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseSpuName())));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseOuterSpuId())) {
                        refundItemInfosBoolQueryBuilder.must(termsQuery(EsRefundFields.refundItemInfosWarehouseOuterIdKeyword,
                            splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseOuterSpuId())));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseSkuName())) {
                        refundItemInfosBoolQueryBuilder.must(termsQuery(EsRefundFields.refundItemInfosWarehouseSkuTitleKeyword,
                            splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseSkuName())));
                    }

                    if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseOuterSkuId())) {
                        refundItemInfosBoolQueryBuilder.must(termsQuery(EsRefundFields.refundItemInfosWarehouseSkuOuterIdKeyword,
                            splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseOuterSkuId())));
                    }

                    if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getOrderSellerFlagList())) {
                        boolQueryBuilder.should(termsQuery(EsRefundFields.orderSellerFlag,
                            reportFormDataStatisticsDTO.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList())) {
                        boolQueryBuilder.should(termsQuery(EsRefundFields.ayCustomOrderFlag,
                            reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList()));
                    }

                    if (refundItemInfosBoolQueryBuilder.hasClauses()) {
                        boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos, refundItemInfosBoolQueryBuilder, ScoreMode.None));
                    }
                    break;
                case REVERSE_FUZZY_SEARCH:
                    handleReportFormItemInfoFuzzySearch(boolQueryBuilder, reportFormDataStatisticsDTO, true);
                    break;
            }
        }
    }


    /**
     * 处理数据报表查询的模糊搜索类型
     *
     * @param reportFormDataStatisticsDTO
     */
    private void handleReportFormItemInfoFuzzySearch(BoolQueryBuilder boolQueryBuilder,
                                                     ReportFormDataStatisticsDTO reportFormDataStatisticsDTO, boolean isReverseFuzzySearch) {
        BoolQueryBuilder fuzzyBoolQueryBuilder = boolQuery();
        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getSpuName())) {
            fuzzyBoolQueryBuilder
                .must(matchPhraseQuery(EsRefundFields.title, splitAlphanumeric(reportFormDataStatisticsDTO.getSpuName())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getOuterSpuId())) {
            fuzzyBoolQueryBuilder.must(
                matchPhraseQuery(EsRefundFields.outerIdText, splitAlphanumeric(reportFormDataStatisticsDTO.getOuterSpuId())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getSkuName())) {
            fuzzyBoolQueryBuilder
                .must(matchPhraseQuery(EsRefundFields.skuAttributivesText, splitAlphanumeric(reportFormDataStatisticsDTO.getSkuName())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getOuterSkuId())) {
            fuzzyBoolQueryBuilder.must(matchPhraseQuery(EsRefundFields.skuOuterIdsText,
                splitAlphanumeric(reportFormDataStatisticsDTO.getOuterSkuId())));
        }

        // 货品
        BoolQueryBuilder refundItemInfoBoolQueryBuilder = boolQuery();
        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseSpuName())) {
            refundItemInfoBoolQueryBuilder.must(matchPhraseQuery(EsRefundFields.refundItemInfosWarehouseSpuTitle,
                splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseSpuName())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseOuterSpuId())) {
            refundItemInfoBoolQueryBuilder.must(matchPhraseQuery(EsRefundFields.refundItemInfosWarehouseOuterId,
                splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseOuterSpuId())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseSkuName())) {
            refundItemInfoBoolQueryBuilder.must(matchPhraseQuery(EsRefundFields.refundItemInfosWarehouseSkuTitle,
                splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseSkuName())));
        }

        if (StringUtils.isNotEmpty(reportFormDataStatisticsDTO.getWarehouseOuterSkuId())) {
            refundItemInfoBoolQueryBuilder.must(matchPhraseQuery(EsRefundFields.refundItemInfosWarehouseSkuOuterId,
                splitAlphanumeric(reportFormDataStatisticsDTO.getWarehouseOuterSkuId())));
        }

        if (isReverseFuzzySearch) {
            boolQueryBuilder.mustNot(fuzzyBoolQueryBuilder);
            boolQueryBuilder.mustNot(nestedQuery(EsRefundFields.refundItemInfos, refundItemInfoBoolQueryBuilder, ScoreMode.None));
        } else {
            boolQueryBuilder.must(fuzzyBoolQueryBuilder);
            boolQueryBuilder.must(nestedQuery(EsRefundFields.refundItemInfos, refundItemInfoBoolQueryBuilder, ScoreMode.None));
        }
    }




    /**
     * 处理数据报表查询的模糊搜索类型
     *
     * @param reportFormDataStatisticsDTO
     */
    private BoolQueryBuilder handleReportFormFlagFuzzySearch(ReportFormDataStatisticsDTO reportFormDataStatisticsDTO) {
        BoolQueryBuilder fuzzyBoolQueryBuilder = boolQuery();

        if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getOrderSellerFlagList())
            || CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList())) {
            BoolQueryBuilder flagQueryBuilder = boolQuery();
            if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getOrderSellerFlagList())) {
                flagQueryBuilder
                    .should(termsQuery(EsRefundFields.orderSellerFlag, reportFormDataStatisticsDTO.getOrderSellerFlagList()));
            }

            if (CollectionUtils.isNotEmpty(reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList())) {
                flagQueryBuilder.should(
                    termsQuery(EsRefundFields.ayCustomFlag, reportFormDataStatisticsDTO.getAyCustomOrderSellerFlagList()));
            }

            fuzzyBoolQueryBuilder.must(flagQueryBuilder);
        }

        return fuzzyBoolQueryBuilder;
    }

    /**
     * 处理搜索用户信息
     *
     * @param queryCondition
     * @param userInfoDTO
     */
    private void handleUserSearchInfo(BoolQueryBuilder queryCondition, UserInfoDTO userInfoDTO) {
        if (userInfoDTO.isMultiShops() && CollectionUtils.isNotEmpty(userInfoDTO.getTargetSellerList())) {
            BoolQueryBuilder should = new BoolQueryBuilder();
            if (aiyongTradeApiServiceConfig.isSearchByAyUserIdEnable()) {
                List<String> ayUserIdList = new ArrayList<>();
                Set<String> storeIdAndAppNameSet = new HashSet<>();
                for (TargetSellerInfo sellerInfo : userInfoDTO.getTargetSellerList()) {
                    String targetStoreId = sellerInfo.getTargetStoreId();
                    String targetAppName = sellerInfo.getTargetAppName();
                    String targetSellerId = sellerInfo.getTargetSellerId();
                    ayUserIdList.add(OrderUtil.generalAyUserId(targetSellerId, targetStoreId, targetAppName));
                    String searchStoreIdKey = targetStoreId + targetAppName;
                    if (storeIdAndAppNameSet.contains(searchStoreIdKey)) {
                        // 存在多个相同平台，处理一次即可
                        continue;
                    }
                    storeIdAndAppNameSet.add(searchStoreIdKey);
                    BoolQueryBuilder builder = boolQuery();
                    should.must(builder);
                }
                queryCondition.must(should);
                queryCondition.must(termsQuery(EsFields.ayUserId, ayUserIdList));
            } else {
                for (TargetSellerInfo sellerInfo : userInfoDTO.getTargetSellerList()) {
                    String targetStoreId = sellerInfo.getTargetStoreId();
                    BoolQueryBuilder must = boolQuery().must(termQuery(EsFields.storeId, targetStoreId));
                    if (StringUtils.isNotEmpty(sellerInfo.getTargetSellerId())) {
                        must.must(termQuery(EsFields.sellerId, sellerInfo.getTargetSellerId()));
                    }
                    appendAppNameQuery(must, sellerInfo.getTargetAppName());
                    should.should(must);
                }
                queryCondition.must(should);
            }
        } else {
            String storeId = userInfoDTO.getStoreId();
            String sellerId = userInfoDTO.getSellerId();
            String appName = userInfoDTO.getAppName();
            if (aiyongTradeApiServiceConfig.isSearchByAyUserIdEnable()) {
                queryCondition
                    .must(termQuery(EsFields.ayUserId, OrderUtil.generalAyUserId(sellerId, storeId, appName)));
            } else {
                if (StringUtils.isNotEmpty(sellerId)) {
                    queryCondition.must(termQuery(EsFields.sellerId, sellerId));
                }
                queryCondition.must(termQuery(EsFields.storeId, storeId));
                appendAppNameQuery(queryCondition, appName);
            }
        }
    }

    private void getReportFormDataOverview(StatisticsReportFormRefundDataResult refundDataResult,
                                           NativeSearchQueryBuilder builder, ReportFormDataStatisticsDTO reportFormDataStatisticsDTO,
                                           UserInfoDTO userInfoDTO, AggregationBuilder... agg) {

        // 概览的数据列表
        LocalDateTime originalStartTime = reportFormDataStatisticsDTO.getStartTime();
        // 因要计算环比，则需要追加时间，重置下查询条件
        BoolQueryBuilder boolQueryBuilder =
            generateReportFormStatisticsQueryBuilder(reportFormDataStatisticsDTO, userInfoDTO);
        builder.withQuery(filterQuery(boolQueryBuilder));

        DateHistogramAggregationBuilder dataOverviewAggBuilder = AggregationBuilders.dateHistogram("dataOverview")
            .field(EsFields.endTime).fixedInterval(DateHistogramInterval.days(1)).format(DateUtil.DATETIME_STR_PATTERN);
        for (AggregationBuilder aggregationBuilder : agg) {
            dataOverviewAggBuilder.subAggregation(aggregationBuilder);
        }

        Aggregations aggs = aggs(builder.build(), dataOverviewAggBuilder);
        Histogram dataOverviewListAgg = aggs.get("dataOverview");
        if (CollectionUtils.isEmpty(dataOverviewListAgg.getBuckets())) {
            return;
        }

        // 退款总维度
        // 退款单数基期总计
        int basePeriodByRefundOrderCount = 0;
        // 退款金额基期总计
        int basePeriodByRefundAmountSum = 0;
        // 退款商品数基期总计
        int basePeriodByRefundItemNumSum = 0;
        // 退款商品对应货品成本基期总计
        int basePeriodByWarehouseCostSum = 0;
        // 退款商品对应货品单价成本基期总计
        int basePeriodByWarehouseUnitPriceCostSum = 0;

        // 退款单数现期总计
        int currentPeriodByRefundOrderCount = 0;
        // 退款商品数现期总计
        int currentPeriodByRefundAmountSum = 0;
        // 退款商品对应货品成本现期总计
        int currentPeriodByRefundItemNumSum = 0;
        // 退款商品对应货品成本现期总计
        int currentPeriodByWarehouseCostSum = 0;
        // 退款商品对应货品单价成本现期总计
        int currentPeriodByWarehouseUnitPriceCostSum = 0;


        // 未发货仅退款
        // 未发货仅退款退款金额数基期总计
        int basePeriodByOnlyRefundRefundAmountSum = 0;
        // 未发货仅退款退款商品数基期总计
        int basePeriodByOnlyRefundItemNumSum = 0;
        // 未发货仅退款退款单数基期总计
        int basePeriodByOnlyRefundOrderCount = 0;

        // 未发货仅退款退款金额数现期总计
        int currentPeriodByOnlyRefundRefundAmountSum = 0;
        // 未发货仅退款退款商品数现期总计
        int currentPeriodByOnlyRefundItemNumSum = 0;
        // 未发货仅退款退款单数现期总计
        int currentPeriodByOnlyRefundOrderCount = 0;


        // 退货退款
        // 退货退款退款金额数基期总计
        int basePeriodByReturnGoodAndRefundAmountSum = 0;
        // 退货退款退款商品数基期总计
        int basePeriodByReturnGoodAndRefundItemNumSum = 0;
        // 退货退款退款单数基期总计
        int basePeriodByReturnGoodAndRefundOrderCount = 0;

        // 退货退款退款金额数现期总计
        int currentPeriodByReturnGoodAndRefundAmountSum = 0;
        // 退货退款退款商品数现期总计
        int currentPeriodByReturnGoodAndRefundItemNumSum = 0;
        // 退货退款退款单数现期总计
        int currentPeriodByReturnGoodAndRefundOrderCount = 0;

        List<StatisticsReportFormRefundDataResult.StatisticsData> statisticsDataList = Lists.newArrayList();
        for (Histogram.Bucket bucket : dataOverviewListAgg.getBuckets()) {
            StatisticsReportFormRefundDataResult.StatisticsData statisticsData = new StatisticsReportFormRefundDataResult.StatisticsData();
            String groupValue = bucket.getKeyAsString();
            statisticsData.setGroupValue(groupValue);
            Aggregations aggregations = bucket.getAggregations();
            // 退款总概览
            Filter mainAggFilter = aggregations.get("refundMainAgg");
            Sum sumRefundAmount = mainAggFilter.getAggregations().get("refundAmountSum");
            int refundAmountSum = sumRefundAmount == null ? 0: (int) sumRefundAmount.getValue();

            Sum sumRefundedItemsNum = mainAggFilter.getAggregations().get("refundedItemsNumSum");
            int refundedItemsNum = sumRefundedItemsNum == null ? 0: (int) sumRefundedItemsNum.getValue();

            NumericMetricsAggregation.SingleValue orderCountAgg= mainAggFilter.getAggregations().get("orderCount");
            int orderCount = orderCountAgg == null ? 0 : (int)orderCountAgg.value();

            ParsedNested refundItemInfoAgg  = mainAggFilter.getAggregations().get("refundItemInfoAgg");
            double warehouseCostSum = 0;
            double warehouseCostLiSum = 0;
            if (refundItemInfoAgg != null) {
                ParsedSum warehouseCostSumAgg = refundItemInfoAgg.getAggregations().get("warehouseCostSum");
                warehouseCostSum = warehouseCostSumAgg == null ? 0: warehouseCostSumAgg.getValue();

                ParsedSum warehouseCostLiSumAgg = refundItemInfoAgg.getAggregations().get("warehouseCostLiSum");
                warehouseCostLiSum = warehouseCostLiSumAgg == null ? 0: warehouseCostLiSumAgg.getValue();

                warehouseCostSum += CurrencyUtils.cents2fen(warehouseCostLiSum);
            }

            // 未发货仅退款
            Filter notSendGoodOnlyRefundFilter = aggregations.get("notSendGoodOnlyRefund");
            Sum sumNotSendGoodOnlyRefundAmount =
                notSendGoodOnlyRefundFilter.getAggregations().get("notSendGoodOnlyRefundAmountSum");
            int notSendGoodOnlyRefundAmountSum =
                sumNotSendGoodOnlyRefundAmount == null ? 0 : (int)sumNotSendGoodOnlyRefundAmount.getValue();
            Sum sumNotSendGoodOnlyRefundItemsNumSum =
                notSendGoodOnlyRefundFilter.getAggregations().get("notSendGoodOnlyRefundItemsNumSum");
            int notSendGoodOnlyRefundItemsNumSum =
                sumNotSendGoodOnlyRefundItemsNumSum == null ? 0 : (int)sumNotSendGoodOnlyRefundItemsNumSum.getValue();
            NumericMetricsAggregation.SingleValue notSendGoodOnlyRefundOrderCountAgg =
                notSendGoodOnlyRefundFilter.getAggregations().get("orderCount");
            int notSendGoodOnlyRefundOrderCount =
                notSendGoodOnlyRefundOrderCountAgg == null ? 0 : (int)notSendGoodOnlyRefundOrderCountAgg.value();

            // 退货退款
            Filter nreturnGoodAndRefundFilter = aggregations.get("returnGoodAndRefund");
            Sum sumReturnGoodAndRefundAmount =
                nreturnGoodAndRefundFilter.getAggregations().get("returnGoodAndRefundAmountSum");
            int returnGoodAndRefundAmountSum =
                sumReturnGoodAndRefundAmount == null ? 0 : (int)sumReturnGoodAndRefundAmount.getValue();
            Sum sumReturnGoodAndRefundItemsNum =
                nreturnGoodAndRefundFilter.getAggregations().get("returnGoodAndRefundItemsNumSum");
            int returnGoodAndRefundItemsNumSum =
                sumReturnGoodAndRefundItemsNum == null ? 0 : (int)sumReturnGoodAndRefundItemsNum.getValue();
            NumericMetricsAggregation.SingleValue returnGoodAndRefundOrderCountAgg =
                nreturnGoodAndRefundFilter.getAggregations().get("orderCount");
            int returnGoodAndRefundOrderCount =
                returnGoodAndRefundOrderCountAgg == null ? 0 : (int)returnGoodAndRefundOrderCountAgg.value();


            if (LocalDate.parse(groupValue, DateUtil.FORMATTER_DATE).isBefore(originalStartTime.toLocalDate())) {
                basePeriodByRefundAmountSum += refundAmountSum;
                basePeriodByRefundItemNumSum += refundedItemsNum;
                basePeriodByRefundOrderCount += orderCount;
                basePeriodByWarehouseCostSum += warehouseCostSum;
                if (refundedItemsNum != 0) {
                    basePeriodByWarehouseUnitPriceCostSum += warehouseCostSum / refundedItemsNum;
                } else {
                    basePeriodByWarehouseUnitPriceCostSum += warehouseCostSum;
                }

                basePeriodByOnlyRefundRefundAmountSum += notSendGoodOnlyRefundAmountSum;
                basePeriodByOnlyRefundItemNumSum += notSendGoodOnlyRefundItemsNumSum;
                basePeriodByOnlyRefundOrderCount += notSendGoodOnlyRefundOrderCount;

                basePeriodByReturnGoodAndRefundAmountSum += returnGoodAndRefundAmountSum;
                basePeriodByReturnGoodAndRefundItemNumSum += returnGoodAndRefundItemsNumSum;
                basePeriodByReturnGoodAndRefundOrderCount += returnGoodAndRefundOrderCount;
            } else {
                currentPeriodByRefundAmountSum += refundAmountSum;
                currentPeriodByRefundItemNumSum += refundedItemsNum;
                currentPeriodByRefundOrderCount += orderCount;
                currentPeriodByWarehouseCostSum += warehouseCostSum;
                double refundWarehouseUnitPriceCost = warehouseCostSum;
                if (refundedItemsNum != 0) {
                    refundWarehouseUnitPriceCost = warehouseCostSum / refundedItemsNum;
                }

                currentPeriodByWarehouseUnitPriceCostSum += refundWarehouseUnitPriceCost;
                currentPeriodByOnlyRefundRefundAmountSum += notSendGoodOnlyRefundAmountSum;
                currentPeriodByOnlyRefundItemNumSum += notSendGoodOnlyRefundItemsNumSum;
                currentPeriodByOnlyRefundOrderCount += notSendGoodOnlyRefundOrderCount;

                currentPeriodByReturnGoodAndRefundAmountSum += returnGoodAndRefundAmountSum;
                currentPeriodByReturnGoodAndRefundItemNumSum += returnGoodAndRefundItemsNumSum;
                currentPeriodByReturnGoodAndRefundOrderCount += returnGoodAndRefundOrderCount;

                statisticsData.setRefundAmountSum(refundAmountSum);
                statisticsData.setRefundedItemsNumSum(refundedItemsNum);
                statisticsData.setRefundOrderCount(orderCount);
                statisticsData.setRefundWarehouseCost(warehouseCostSum);
                // 未发货仅退款
                statisticsData.setNotSendGoodOnlyRefundAmountSum(notSendGoodOnlyRefundAmountSum);
                statisticsData.setNotSendGoodOnlyRefundItemsNumSum(notSendGoodOnlyRefundItemsNumSum);
                statisticsData.setNotSendGoodOnlyRefundOrderCount(notSendGoodOnlyRefundOrderCount);
                // 退货退款
                statisticsData.setReturnGoodAndRefundAmountSum(returnGoodAndRefundAmountSum);
                statisticsData.setReturnGoodAndRefundItemsNumSum(returnGoodAndRefundItemsNumSum);
                statisticsData.setReturnGoodAndRefundOrderCount(returnGoodAndRefundOrderCount);

                statisticsDataList.add(statisticsData);
            }

        }

        // 退款总览
        refundDataResult.setRefundAmountSum(currentPeriodByRefundAmountSum);
        refundDataResult.setRefundAmountSumGrowthRate(
            MathUtil.calculateGrowthRate(basePeriodByRefundAmountSum, currentPeriodByRefundAmountSum));
        refundDataResult.setRefundedItemsNumSum(currentPeriodByRefundItemNumSum);
        refundDataResult.setRefundedItemsNumSumGrowthRate(
            MathUtil.calculateGrowthRate(basePeriodByRefundItemNumSum, currentPeriodByRefundItemNumSum));
        refundDataResult.setRefundOrderCount(currentPeriodByRefundOrderCount);
        refundDataResult.setRefundOrderCountGrowthRate(
            MathUtil.calculateGrowthRate(basePeriodByRefundOrderCount, currentPeriodByRefundOrderCount));
        refundDataResult.setRefundWarehouseCost(currentPeriodByWarehouseCostSum);
        refundDataResult.setRefundWarehouseCostGrowthRate(MathUtil.calculateGrowthRate(basePeriodByWarehouseCostSum, currentPeriodByWarehouseCostSum));
        refundDataResult.setRefundWarehouseUnitPriceCost(currentPeriodByWarehouseUnitPriceCostSum);
        refundDataResult.setRefundWarehouseUnitPriceCostGrowthRate(MathUtil.calculateGrowthRate(basePeriodByWarehouseUnitPriceCostSum, currentPeriodByWarehouseUnitPriceCostSum));

        // 未发货仅退款
        refundDataResult.setNotSendGoodOnlyRefundAmountSum(currentPeriodByOnlyRefundRefundAmountSum);
        refundDataResult.setNotSendGoodOnlyRefundAmountSumGrowthRate(MathUtil
            .calculateGrowthRate(basePeriodByOnlyRefundRefundAmountSum, currentPeriodByOnlyRefundRefundAmountSum));
        refundDataResult.setNotSendGoodOnlyRefundItemsNumSum(currentPeriodByOnlyRefundItemNumSum);
        refundDataResult.setNotSendGoodOnlyRefundItemsNumSumGrowthRate(
            MathUtil.calculateGrowthRate(basePeriodByOnlyRefundItemNumSum, currentPeriodByOnlyRefundItemNumSum));
        refundDataResult.setNotSendGoodOnlyRefundOrderCount(currentPeriodByOnlyRefundOrderCount);
        refundDataResult.setNotSendGoodOnlyRefundOrderCountGrowthRate(
            MathUtil.calculateGrowthRate(basePeriodByOnlyRefundOrderCount, currentPeriodByOnlyRefundOrderCount));

        // 退货退款
        refundDataResult.setReturnGoodAndRefundAmountSum(currentPeriodByReturnGoodAndRefundAmountSum);
        refundDataResult.setReturnGoodAndRefundAmountSumGrowthRate(MathUtil.calculateGrowthRate(
            basePeriodByReturnGoodAndRefundAmountSum, currentPeriodByReturnGoodAndRefundAmountSum));
        refundDataResult.setReturnGoodAndRefundItemsNumSum(currentPeriodByReturnGoodAndRefundItemNumSum);
        refundDataResult.setReturnGoodAndRefundItemsNumSumGrowthRate(MathUtil.calculateGrowthRate(
            basePeriodByReturnGoodAndRefundItemNumSum, currentPeriodByReturnGoodAndRefundItemNumSum));
        refundDataResult.setReturnGoodAndRefundOrderCount(currentPeriodByReturnGoodAndRefundOrderCount);
        refundDataResult.setReturnGoodAndRefundOrderCountGrowthRate(MathUtil.calculateGrowthRate(
            basePeriodByReturnGoodAndRefundOrderCount, currentPeriodByReturnGoodAndRefundOrderCount));

        refundDataResult.setStatisticsDataList(statisticsDataList);
    }


    private List<StatisticsReportFormRefundDataResult.StatisticsData> groupAgg(@NotNull String groupFields,
        @NotNull NativeSearchQueryBuilder builder, Long searchResultDocTotal, AggregationBuilder... subAgg) {
        List<StatisticsReportFormRefundDataResult.StatisticsData> statisticsDataList = Lists.newArrayList();
        TermsAggregationBuilder termsAggBuilder = AggregationBuilders.terms("groupFields").field(groupFields).size(100).executionHint(
            searchResultDocTotal != null && searchResultDocTotal <= serviceConfig.getDatacenterExecutionHintMapMax()
                ? EXECUTION_HINT_MAP : null);;
        for (AggregationBuilder aggregationBuilder : subAgg) {
            termsAggBuilder.subAggregation(aggregationBuilder);
        }

        Aggregations aggs = aggs(builder.build(), termsAggBuilder);
        Terms platformIdGroup = aggs.get("groupFields");
        for (Terms.Bucket bucket : platformIdGroup.getBuckets()) {
            StatisticsReportFormRefundDataResult.StatisticsData statisticsData =
                new StatisticsReportFormRefundDataResult.StatisticsData();

            statisticsData.setGroupValue(bucket.getKeyAsString().trim());

            Filter mainAggFilter = bucket.getAggregations().get("refundMainAgg");
            // 退款金额总和
            Sum refundAmountSum = mainAggFilter.getAggregations().get("refundAmountSum");
            statisticsData.setRefundAmountSum(refundAmountSum == null ? 0 : (int)refundAmountSum.getValue());
            // 退款商品数总和
            Sum refundedItemsNumSum = mainAggFilter.getAggregations().get("refundedItemsNumSum");
            statisticsData.setRefundedItemsNumSum(refundedItemsNumSum == null ? 0 : (int)refundedItemsNumSum.getValue());
            // 退款订单数总和
            NumericMetricsAggregation.SingleValue refundOrderCount = mainAggFilter.getAggregations().get("orderCount");
            statisticsData.setRefundOrderCount(refundOrderCount == null ? 0 : (int)refundOrderCount.value());

            // 未发货仅退款
            Filter notSendGoodOnlyRefundFilter = bucket.getAggregations().get("notSendGoodOnlyRefund");
            Sum sumNotSendGoodOnlyRefundAmount =
                notSendGoodOnlyRefundFilter.getAggregations().get("notSendGoodOnlyRefundAmountSum");
            statisticsData.setNotSendGoodOnlyRefundAmountSum(
                sumNotSendGoodOnlyRefundAmount == null ? 0 : (int)sumNotSendGoodOnlyRefundAmount.getValue());;
            Sum sumNotSendGoodOnlyRefundItemsNumSum =
                notSendGoodOnlyRefundFilter.getAggregations().get("notSendGoodOnlyRefundItemsNumSum");
            statisticsData.setNotSendGoodOnlyRefundItemsNumSum(
                sumNotSendGoodOnlyRefundItemsNumSum == null ? 0 : (int)sumNotSendGoodOnlyRefundItemsNumSum.getValue());;
            NumericMetricsAggregation.SingleValue notSendGoodOnlyRefundOrderCountAgg =
                notSendGoodOnlyRefundFilter.getAggregations().get("orderCount");
            statisticsData.setNotSendGoodOnlyRefundOrderCount(
                notSendGoodOnlyRefundOrderCountAgg == null ? 0 : (int)notSendGoodOnlyRefundOrderCountAgg.value());;

            // 退货退款
            Filter nreturnGoodAndRefundFilter = bucket.getAggregations().get("returnGoodAndRefund");
            Sum sumReturnGoodAndRefundAmount =
                nreturnGoodAndRefundFilter.getAggregations().get("returnGoodAndRefundAmountSum");
            statisticsData.setReturnGoodAndRefundAmountSum(
                sumReturnGoodAndRefundAmount == null ? 0 : (int)sumReturnGoodAndRefundAmount.getValue());;

            Sum sumReturnGoodAndRefundItemsNum =
                nreturnGoodAndRefundFilter.getAggregations().get("returnGoodAndRefundItemsNumSum");
            statisticsData.setReturnGoodAndRefundItemsNumSum(
                sumReturnGoodAndRefundItemsNum == null ? 0 : (int)sumReturnGoodAndRefundItemsNum.getValue());;
            NumericMetricsAggregation.SingleValue returnGoodAndRefundOrderCountAgg =
                nreturnGoodAndRefundFilter.getAggregations().get("orderCount");
            statisticsData.setReturnGoodAndRefundOrderCount(
                returnGoodAndRefundOrderCountAgg == null ? 0 : (int)returnGoodAndRefundOrderCountAgg.value());;
            statisticsDataList.add(statisticsData);
        }

        return statisticsDataList;
    }


    /**
     * 根据refundItemInfo中字段【numIid、skuId、warehouseSpuId、warehouseSkuId】进行分组聚合
     * 统计维度： 1. 总计：退款单金额、退款商品件数、退款销售额 2.
     * 未发货仅退款：退款单金额、退款商品件数、退款销售额 3. 退货退款：退款单金额、退款商品件数、退款销售额
     *
     * @param groupFields
     * @param builder
     * @param searchResultDocTotal
     * @return
     */
    private List<StatisticsReportFormRefundDataResult.StatisticsData> refundItemInfoFieldGroupAgg(
        @NotNull String groupFields, @NotNull NativeSearchQueryBuilder builder, BoolQueryBuilder mainFilter , Long searchResultDocTotal) {

        List<StatisticsReportFormRefundDataResult.StatisticsData> statisticsDataList = Lists.newArrayList();
        NestedAggregationBuilder refundItemsNestedAggBuilder =
            AggregationBuilders.nested("refundItemsNested", EsRefundFields.refundItemInfos);

        NestedAggregationBuilder filterSubRefundItemsNestedAggBuilder =
            AggregationBuilders.nested("refundItemsNested", EsRefundFields.refundItemInfos);

        TermsAggregationBuilder termsAggBuilder =
            AggregationBuilders.terms("groupFields").field(groupFields).size(100).executionHint(
                searchResultDocTotal != null && searchResultDocTotal <= serviceConfig.getDatacenterExecutionHintMapMax()
                    ? EXECUTION_HINT_MAP : null);

        // 退款金额
        SumAggregationBuilder refundItemInfosRefundPriceAggBuilder =
            AggregationBuilders.sum("refundItemInfosRefundPrice").field(EsRefundFields.refundItemInfosRefundPrice);

        // 退款商品数
        SumAggregationBuilder refundItemInfosNumAggBuilder =
            AggregationBuilders.sum("refundItemInfosNum").field(EsRefundFields.refundItemInfosNum);

        // 退款成本
        SumAggregationBuilder warehouseCostSumAggBuilder =
            AggregationBuilders.sum("warehouseCostSum").field(EsRefundFields.refundItemInfosWarehouseCost);

        // 订单数
        ReverseNestedAggregationBuilder orderCountToRootBuilder = AggregationBuilders
            .reverseNested("toRoot")
            .subAggregation(AggregationBuilders.cardinality("orderCount")
                .field("tid.keyword"));

        // 聚合时条件过滤
        FilterAggregationBuilder mainAggBuilder = AggregationBuilders.filter("refundMainAgg", mainFilter).
            subAggregation(AggregationBuilders.nested("refundItemsNested", EsRefundFields.refundItemInfos)
                // 退款金额
                .subAggregation(refundItemInfosRefundPriceAggBuilder)
                // 退款商品数
                .subAggregation(refundItemInfosNumAggBuilder)
                // 退款成本
                .subAggregation(warehouseCostSumAggBuilder)
            )
            // 订单数
            .subAggregation(orderCountToRootBuilder);


        filterSubRefundItemsNestedAggBuilder
            .subAggregation(AggregationBuilders.sum("refundAmountSum").field(EsRefundFields.refundItemInfosRefundPrice))
            .subAggregation(AggregationBuilders.sum("refundItemsNumSum").field(EsRefundFields.refundItemInfosNum));

        // 4.未发货仅退款
        FilterAggregationBuilder notSendGoodOnlyRefundAggBuilder = AggregationBuilders
            .filter("notSendGoodOnlyRefund", boolQuery().must(termQuery(EsRefundFields.afterSaleType, RefundSaleTypeConstant.REFUND_ONLY)))
            .subAggregation(filterSubRefundItemsNestedAggBuilder)
            // 订单数
            .subAggregation(orderCountToRootBuilder);

        // 5.退货退款
        FilterAggregationBuilder returnGoodAndRefundAggBuilder = AggregationBuilders
            .filter("returnGoodAndRefund",
                boolQuery()
                    .must(termQuery(EsRefundFields.afterSaleType, RefundSaleTypeConstant.RETURN_GOOD_AND_REFUND)))
            .subAggregation(filterSubRefundItemsNestedAggBuilder)
            // 订单数
            .subAggregation(orderCountToRootBuilder);

        ReverseNestedAggregationBuilder itemInfosReverseNestedAggBuilder = AggregationBuilders
            .reverseNested("itemInfosReverseNestedAgg")
            .subAggregation(mainAggBuilder)
            .subAggregation(notSendGoodOnlyRefundAggBuilder)
            .subAggregation(returnGoodAndRefundAggBuilder);
        termsAggBuilder.subAggregation(itemInfosReverseNestedAggBuilder);

        refundItemsNestedAggBuilder.subAggregation(termsAggBuilder);
        Aggregations aggs = aggs(builder.build(), refundItemsNestedAggBuilder);
        ParsedNested refundItemsNested  = aggs.get("refundItemsNested");
        Terms groupFieldTerms = refundItemsNested.getAggregations().get("groupFields");
        for (Terms.Bucket bucket : groupFieldTerms.getBuckets()) {
            StatisticsReportFormRefundDataResult.StatisticsData statisticsData = new StatisticsReportFormRefundDataResult.StatisticsData();
            statisticsData.setGroupValue(bucket.getKeyAsString().trim());

            ParsedReverseNested itemInfosReverseNestedAgg = bucket.getAggregations().get("itemInfosReverseNestedAgg");
            if (itemInfosReverseNestedAgg != null) {
                Filter mainAggFilter = itemInfosReverseNestedAgg.getAggregations().get("refundMainAgg");
                ParsedNested nestedRefund = mainAggFilter.getAggregations().get("refundItemsNested");
                // 退款金额
                ParsedSum refundItemInfosRefundPriceAgg = nestedRefund.getAggregations().get("refundItemInfosRefundPrice");
                statisticsData.setRefundAmountSum(refundItemInfosRefundPriceAgg == null ? 0 : (int)refundItemInfosRefundPriceAgg.getValue());
                // 退款件数
                ParsedSum refundItemInfosNumAgg = nestedRefund.getAggregations().get("refundItemInfosNum");
                statisticsData.setRefundedItemsNumSum(refundItemInfosNumAgg == null ? 0 : (int)refundItemInfosNumAgg.getValue());
                // 货品成本
                ParsedSum warehouseCostSumAgg = nestedRefund.getAggregations().get("warehouseCostSum");
                statisticsData.setRefundWarehouseCost(warehouseCostSumAgg == null ? 0 : (int)warehouseCostSumAgg.getValue());
                // 退款订单数
                ParsedReverseNested toRootAgg = mainAggFilter.getAggregations().get("toRoot");
                long orderCount = ((ParsedCardinality)toRootAgg.getAggregations().get("orderCount")).getValue();
                statisticsData.setRefundOrderCount((int)orderCount);

                // 1. 未发货仅退款
                ParsedFilter notSendGoodOnlyRefundFilter = itemInfosReverseNestedAgg.getAggregations().get("notSendGoodOnlyRefund");
                ParsedNested nestedForNotSendGoodOnlyRefund =  notSendGoodOnlyRefundFilter.getAggregations().get("refundItemsNested");
                // 1.1 退款金额
                Sum notSendGoodOnlyRefundRefundAmountSum = nestedForNotSendGoodOnlyRefund.getAggregations().get("refundAmountSum");
                statisticsData.setNotSendGoodOnlyRefundAmountSum(notSendGoodOnlyRefundRefundAmountSum == null ? 0 : (int)notSendGoodOnlyRefundRefundAmountSum.getValue());
                // 1.2 退款商品数
                Sum notSendGoodOnlyRefundRefundItemsNumSumSum = nestedForNotSendGoodOnlyRefund.getAggregations().get("refundItemsNumSum");
                statisticsData.setNotSendGoodOnlyRefundItemsNumSum(notSendGoodOnlyRefundRefundItemsNumSumSum == null ? 0 : (int)notSendGoodOnlyRefundRefundItemsNumSumSum.getValue());
                // 1.3 退款订单数
                ParsedReverseNested notSendGoodOnlyRefundToRootAgg = notSendGoodOnlyRefundFilter.getAggregations().get("toRoot");
                long notSendGoodOnlyRefundOrderCount = ((ParsedCardinality)notSendGoodOnlyRefundToRootAgg.getAggregations().get("orderCount")).getValue();
                statisticsData.setNotSendGoodOnlyRefundOrderCount( (int)notSendGoodOnlyRefundOrderCount);

                // 2. 退货退款
                ParsedFilter returnGoodAndRefundFilter = itemInfosReverseNestedAgg.getAggregations().get("returnGoodAndRefund");
                ParsedNested nestedForReturnGoodAndRefund =  returnGoodAndRefundFilter.getAggregations().get("refundItemsNested");
                // 2.1 退款金额
                Sum returnGoodAndRefundRefundAmountSum = nestedForReturnGoodAndRefund.getAggregations().get("refundAmountSum");
                statisticsData.setReturnGoodAndRefundAmountSum(returnGoodAndRefundRefundAmountSum == null ? 0 : (int)returnGoodAndRefundRefundAmountSum.getValue());;
                // 2.2 退款商品数
                Sum returnGoodAndRefundRefundItemsNumSumSum = nestedForReturnGoodAndRefund.getAggregations().get("refundItemsNumSum");
                statisticsData.setReturnGoodAndRefundItemsNumSum(returnGoodAndRefundRefundItemsNumSumSum == null ? 0 : (int)returnGoodAndRefundRefundItemsNumSumSum.getValue());;
                // 2.3 退款订单数
                ParsedReverseNested returnGoodAndRefundToRootAgg = returnGoodAndRefundFilter.getAggregations().get("toRoot");
                long returnGoodAndRefundOrderCount = ((ParsedCardinality)returnGoodAndRefundToRootAgg.getAggregations().get("orderCount")).getValue();
                statisticsData.setReturnGoodAndRefundOrderCount((int)returnGoodAndRefundOrderCount);
            }

            statisticsDataList.add(statisticsData);
        }

        return statisticsDataList;
    }

}
